<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai.meituan.waimai.heron</groupId>
        <artifactId>waimai_e_heron_contract_gateway</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>thrift客户端模块</description>
    <artifactId>gateway-thrift-client</artifactId>
    <version>1.0.36</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>thrift-xframe-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimai.heron.logistics</groupId>
            <artifactId>logistics-contract-client</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimai.heron</groupId>
            <artifactId>poilogistics-client</artifactId>
            <scope>provided</scope>
        </dependency>


        <dependency>
            <groupId>com.sankuai.meituan.banma.deliverycontract</groupId>
            <artifactId>delivery-contract-platform-process-sdk</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.banma.business</groupId>
            <artifactId>banma_service_poi_sp_area_manage_client</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.banma</groupId>
            <artifactId>banma_open_client</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.banma.business</groupId>
            <artifactId>banma_service_poi_sparea_client</artifactId>
            <scope>provided</scope>
        </dependency>

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                    <encoding>UTF-8</encoding>
<!--                    <skip>true</skip>-->
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>false</skipTests>
                    <junitArtifactName>junit:junit</junitArtifactName>
                    <argLine>-Xmx2048m</argLine>
                    <excludes>
                        <exclude>**/*_Roo_*</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.meituan.servicecatalog</groupId>
                <artifactId>api-thrift-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>process-generated-class</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>thriftDoc</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <distributionManagement>
                <repository>
                    <id>meituan-offline-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/releases/</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-offline-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
        <profile>
            <id>test</id>
            <distributionManagement>
                <repository>
                    <id>meituan-offline-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/releases/</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-offline-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
        <profile>
            <id>product</id>
            <distributionManagement>
                <repository>
                    <id>meituan-nexus-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.sankuai.com/nexus/content/repositories/releases/</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-nexus-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.sankuai.com/nexus/content/repositories/snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
    </profiles>
</project>
