package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.save;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/4/23
 */
@TypeDoc(description = "合同迁移和拆分保存结果")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
public class ContractMigrateSaveResult {

    /**
     * 流程sessionId
     */
    @FieldDoc(description = "流程sessionId")
    private Long sessionId;

    /**
     * 结果编码
     */
    @FieldDoc(description = "结果编码")
    private Integer code;

    /**
     * 错误消息
     */
    @FieldDoc(description = "错误消息")
    private String message;

    /**
     * 是否成功
     */
    @FieldDoc(description = "是否成功")
    private Boolean success;

    /**
     * 合同版本ID
     */
    @FieldDoc(description = "合同版本ID")
    private Long contractVersionId;

    @ThriftField(1)
    public Long getSessionId() {
        return sessionId;
    }

    @ThriftField
    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    @ThriftField(2)
    public Integer getCode() {
        return code;
    }

    @ThriftField
    public void setCode(Integer code) {
        this.code = code;
    }

    @ThriftField(3)
    public String getMessage() {
        return message;
    }

    @ThriftField
    public void setMessage(String message) {
        this.message = message;
    }

    @ThriftField(4)
    public Boolean getSuccess() {
        return success;
    }

    @ThriftField
    public void setSuccess(Boolean success) {
        this.success = success;
    }

    @ThriftField(5)
    public Long getContractVersionId() {
        return contractVersionId;
    }

    @ThriftField
    public void setContractVersionId(Long contractVersionId) {
        this.contractVersionId = contractVersionId;
    }
}
