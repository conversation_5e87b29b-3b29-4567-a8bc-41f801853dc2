package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.CleanSceneEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CleanLogistcsExtParam {
    @FieldDoc(description = "清除场景")
    private CleanSceneEnum cleanSceneEnum;  // 修改类型为 CleanSceneEnum

    @ThriftField(1)
    public CleanSceneEnum getCleanSceneEnum() {  // 修改返回类型
        return cleanSceneEnum;
    }

    @ThriftField
    public void setCleanSceneEnum(CleanSceneEnum cleanSceneEnum) {  // 修改参数类型
        this.cleanSceneEnum = cleanSceneEnum;
    }
}