package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/27
 */
@TypeDoc(description = "Phf单门店签约流程扩展参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PhfContractSingleSaveExtParamStruct {

    @FieldDoc(description = "费率模式")
    private Integer feeMode;

    @FieldDoc(description = "是否更新商品价格")
    private Boolean updateProductPrice;

    @FieldDoc(description = "服务品牌产品列表")
    private List<LogisticsBrandProductParamStruct> serviceBrandProductList;

}
