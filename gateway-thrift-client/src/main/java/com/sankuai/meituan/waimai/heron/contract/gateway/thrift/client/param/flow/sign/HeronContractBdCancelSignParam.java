package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/4/21
 */
@TypeDoc(description = "bd取消签约参数")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
public class HeronContractBdCancelSignParam {

    /**
     * 商家id
     */
    @FieldDoc(description = "商家id")
    private Long wmPoiId;

    /**
     * 操作人
     */
    @FieldDoc(description = "操作人")
    private HeronContractOperator operator;

    /**
     * 流程分组
     **/
    private SessionCategoryEnum sessionCategory;

    @ThriftField(1)
    public Long getWmPoiId() {
        return wmPoiId;
    }

    @ThriftField
    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    @ThriftField(2)
    public HeronContractOperator getOperator() {
        return operator;
    }

    @ThriftField
    public void setOperator(HeronContractOperator operator) {
        this.operator = operator;
    }

    @ThriftField(3)
    public SessionCategoryEnum getSessionCategory() {
        return sessionCategory;
    }

    @ThriftField
    public void setSessionCategory(SessionCategoryEnum sessionCategory) {
        this.sessionCategory = sessionCategory;
    }
}
