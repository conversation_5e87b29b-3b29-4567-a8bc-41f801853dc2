package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.flow;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/11/29
 */
@TypeDoc(description = "PHF合同BD取消签约参数")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
@Data
public class PhfContractBdCancelSignParam {

    @FieldDoc(description = "门店id")
    @ThriftField(1)
    private Long wmPoiId;

    @FieldDoc(description = "操作人 operateSource=PHF_BD_SYSTEM")
    @ThriftField(2)
    private HeronContractOperator operator;

}
