package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.ContractFeeItem;
import com.sankuai.meituan.banma.thrift.business.open.vo.BmOpenPoiDeliverySpAreaView;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.area.station.LogisticsBussinessStationDTO;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.sla.WmPoiSlaPackageDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.WmLogisticsContractItemDTO;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
@TypeDoc(description = "生效数据聚合结果")
@ToString
@ThriftStruct
public class EffectiveAggreInfoResult {

    /**
     * 商家id
     */
    @FieldDoc(description = "商家id")
    private Long wmPoiId;

    /**
     * 费率模式
     */
    @FieldDoc(description = "费率模式")
    private Integer feeMode;

    /**
     * 配送服务产品列表
     */
    @FieldDoc(description = "配送服务品牌和产品列表")
    private List<PoiLogisticsBrandAndProduct> brandAndProductList;

    /**
     * 平台合同itemList
     */
    @FieldDoc(description = "平台合同itemList")
    private List<WmLogisticsContractItemDTO> platContractItemList;


    /**
     * 履约合同itemList
     */
    @FieldDoc(description = "履约合同itemList")
    private List<ContractFeeItem> perfContractItemList;

    /**
     * 商家配送范围列表
     */
    @FieldDoc(description = "商家配送范围列表")
    private List<LogisticsAreaView> spAreaList;

    /**
     * 聚合配范围站点列表
     */
    @FieldDoc(description = "聚合配范围站点列表")
    private List<LogisticsBussinessStationDTO> stationRelPoList;

    /**
     * SLA列表
     */
    @FieldDoc(description = "SLA列表")
    private List<WmPoiSlaPackageDTO> slaInfoList;

    /**
     * 是否履约双跑，只有bdSettle需要关注，企客履约拆分全量后删除此字段
     */
    @FieldDoc(description = "履约双跑")
    private Boolean perfDR;

    @ThriftField(1)
    public Long getWmPoiId() {
        return wmPoiId;
    }

    @ThriftField
    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    @ThriftField(2)
    public Integer getFeeMode() {
        return feeMode;
    }

    @ThriftField
    public void setFeeMode(Integer feeMode) {
        this.feeMode = feeMode;
    }

    @ThriftField(3)
    public List<PoiLogisticsBrandAndProduct> getBrandAndProductList() {
        return brandAndProductList;
    }

    @ThriftField
    public void setBrandAndProductList(List<PoiLogisticsBrandAndProduct> brandAndProductList) {
        this.brandAndProductList = brandAndProductList;
    }

    @ThriftField(4)
    public List<WmLogisticsContractItemDTO> getPlatContractItemList() {
        return platContractItemList;
    }

    @ThriftField
    public void setPlatContractItemList(List<WmLogisticsContractItemDTO> platContractItemList) {
        this.platContractItemList = platContractItemList;
    }

    @ThriftField(5)
    public List<ContractFeeItem> getPerfContractItemList() {
        return perfContractItemList;
    }

    @ThriftField
    public void setPerfContractItemList(List<ContractFeeItem> perfContractItemList) {
        this.perfContractItemList = perfContractItemList;
    }

    @ThriftField(6)
    public List<LogisticsAreaView> getSpAreaList() {
        return spAreaList;
    }

    @ThriftField
    public void setSpAreaList(List<LogisticsAreaView> spAreaList) {
        this.spAreaList = spAreaList;
    }

    @ThriftField(7)
    public List<LogisticsBussinessStationDTO> getStationRelPoList() {
        return stationRelPoList;
    }

    @ThriftField
    public void setStationRelPoList(List<LogisticsBussinessStationDTO> stationRelPoList) {
        this.stationRelPoList = stationRelPoList;
    }

    @ThriftField(8)
    public List<WmPoiSlaPackageDTO> getSlaInfoList() {
        return slaInfoList;
    }

    @ThriftField
    public void setSlaInfoList(List<WmPoiSlaPackageDTO> slaInfoList) {
        this.slaInfoList = slaInfoList;
    }

    @ThriftField(9)
    public Boolean getPerfDR() {
        return perfDR;
    }

    @ThriftField
    public void setPerfDR(Boolean perfDR) {
        this.perfDR = perfDR;
    }
}
