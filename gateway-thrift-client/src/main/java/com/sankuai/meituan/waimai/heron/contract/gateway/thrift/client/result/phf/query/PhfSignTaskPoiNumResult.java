package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.phf.query;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/1/15
 */
@TypeDoc(description = "PHF签约任务门店数量结果")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
@Data
public class PhfSignTaskPoiNumResult {

    @FieldDoc(description = "批次关联任务id")
    @ThriftField(1)
    private Long batchRelationId;

    @FieldDoc(description = "门店数量")
    @ThriftField(2)
    private Integer poiNum;
}
