package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

/**
 * @description: 通用result
 * @author: chenyihao04
 * @create: 2023-05-11 15:43
 */
@TypeDoc(description = "处理结果")
@ThriftStruct
public class GatewayResult {
    /**
     * 是否成功
     */
    @FieldDoc(description = "是否成功")
    private Boolean success;

    /**
     * 结果编码
     */
    @FieldDoc(description = "结果编码")
    private Integer code;
    /**
     * 错误消息
     */
    @FieldDoc(description = "是否成功")
    private String msg;

    public GatewayResult() {
    }

    @ThriftField(1)
    public Boolean getSuccess() {
        return this.success;
    }

    @ThriftField
    public void setSuccess(Boolean success) {
        this.success = success;
    }

    @ThriftField(2)
    public Integer getCode() {
        return this.code;
    }

    @ThriftField
    public void setCode(Integer code) {
        this.code = code;
    }

    @ThriftField(3)
    public String getMsg() {
        return this.msg;
    }

    @ThriftField
    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static GatewayResult fail(Integer code, String msg) {
        GatewayResult gatewayResult = new GatewayResult();
        gatewayResult.setCode(code);
        gatewayResult.setMsg(msg);
        gatewayResult.setSuccess(false);
        return gatewayResult;
    }

    public static GatewayResult success() {
        GatewayResult gatewayResult = new GatewayResult();
        gatewayResult.setSuccess(true);
        gatewayResult.setCode(0);
        return gatewayResult;
    }
}