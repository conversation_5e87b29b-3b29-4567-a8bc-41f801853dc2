package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/2/18
 */
@TypeDoc(description = "TR同步TSP")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
@Data
public class TrContractSyncToTspParam {

    @FieldDoc(description = "门店ID")
    @ThriftField(1)
    private Long wmPoiId;

    @FieldDoc(description = "合同版本ID")
    @ThriftField(2)
    private Long contractVersionId;

    @FieldDoc(description = "当前生效合同的sessionId")
    @ThriftField(3)
    private Long currentSessionId;


}
