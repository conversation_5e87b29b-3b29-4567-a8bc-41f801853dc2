package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-05-08 19:26
 */
@TypeDoc(description = "先富BD保存参数结构")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HeronContractAuditCallbackStruct {

    /**
     * 商家id
     */
    @FieldDoc(description = "商家id")
    private Long wmPoiId;

    /**
     * 流程sessionId
     */
    @FieldDoc(description = "流程sessionId")
    private Long sessionId;

    /**
     * 是否需要签约
     */
    @FieldDoc(description = "是否需要签约")
    private Boolean needSign;

    /**
     * 是否需要终止流程
     */
    @FieldDoc(description = "是否需要终止流程")
    private Boolean terminateFlow;

    /**
     * 审核通过
     */
    @FieldDoc(description = "审核通过")
    private Boolean auditPass;

    /**
     *
     */
    @FieldDoc(description = "")
    private String context;

    @FieldDoc(description = "操作人misId，有则传")
    private String opMisId;

    /**
     * 操作人id
     */
    @FieldDoc(description = "操作人id")
    private Long opId;

    /**
     * 操作人名称
     */
    @FieldDoc(description = "操作人名称")
    private String opName;

    @FieldDoc(description = "是否创建履约pdf")
    private Boolean needPerfCreatePdf;

    @FieldDoc(description = "是否需要签署国补协议")
    private Boolean signNationalSubsidy;
}