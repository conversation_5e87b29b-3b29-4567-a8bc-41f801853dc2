package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhfBatchOpResultMessageStruct {

    @FieldDoc(description = "操作类型")
    private String opType;

    @FieldDoc(description = "批次id")
    private Long batchBizId;

    @FieldDoc(description = "成功门店列表")
    private List<PoiDetailInfo> successPoiList;

    @FieldDoc(description = "失败门店列表")
    private List<PoiDetailInfo> errorPoiList;

    @Data
    public static class PoiDetailInfo {

        @FieldDoc(description = "门店id")
        private Long wmPoiId;

        @FieldDoc(description = "合同id")
        private Long contractId;

        @FieldDoc(description = "失败原因")
        private String message;
    }
}


