package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2025-02-19 14:20
 */
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AggrePerfChangeNoticeParam {
    @FieldDoc(description = "商家id")
    private Long wmPoiId;

    @FieldDoc(description = "操作人")
    private HeronContractOperator operator;

    @FieldDoc(description = "流程分组")
    private SessionCategoryEnum sessionCategory;

    @ThriftField(1)
    public Long getWmPoiId() {
        return wmPoiId;
    }

    @ThriftField
    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    @ThriftField(2)
    public HeronContractOperator getOperator() {
        return operator;
    }

    @ThriftField
    public void setOperator(HeronContractOperator operator) {
        this.operator = operator;
    }

    @ThriftField(3)
    public SessionCategoryEnum getSessionCategory() {
        return sessionCategory;
    }

    @ThriftField
    public void setSessionCategory(SessionCategoryEnum sessionCategory) {
        this.sessionCategory = sessionCategory;
    }
}