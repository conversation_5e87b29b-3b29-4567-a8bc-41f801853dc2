package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/4/21
 */
@TypeDoc(description = "签约确认参数")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
public class HeronContractConfirmSignParam {

    /**
     * 商家id
     */
    @FieldDoc(description = "商家id")
    private Long wmPoiId;

    /**
     * 签约confirmId
     */
    @FieldDoc(description = "签约confirmId")
    private Long confirmId;

    /**
     * 电子合同版本
     */
    @FieldDoc(description = "电子合同版本")
    private String versionNum;

    /**
     * 合同pdf url
     */
    @FieldDoc(description = "合同pdf url")
    private String pdfUrl;

    /**
     * 操作人
     */
    @FieldDoc(description = "操作人")
    private HeronContractOperator operator;

    /**
     * 签约类型
     * EcontractTaskApplyTypeEnum.getName
     */
    @FieldDoc(description = "签约类型")
    private String applyType;

    @ThriftField(1)
    public Long getWmPoiId() {
        return wmPoiId;
    }

    @ThriftField
    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    @ThriftField(2)
    public HeronContractOperator getOperator() {
        return operator;
    }

    @ThriftField
    public void setOperator(HeronContractOperator operator) {
        this.operator = operator;
    }

    @ThriftField(3)
    public Long getConfirmId() {
        return confirmId;
    }

    @ThriftField
    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    @ThriftField(4)
    public String getVersionNum() {
        return versionNum;
    }

    @ThriftField
    public void setVersionNum(String versionNum) {
        this.versionNum = versionNum;
    }

    @ThriftField(5)
    public String getPdfUrl() {
        return pdfUrl;
    }

    @ThriftField
    public void setPdfUrl(String pdfUrl) {
        this.pdfUrl = pdfUrl;
    }

    @ThriftField(6)
    public String getApplyType() {
        return applyType;
    }

    @ThriftField
    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }
}
