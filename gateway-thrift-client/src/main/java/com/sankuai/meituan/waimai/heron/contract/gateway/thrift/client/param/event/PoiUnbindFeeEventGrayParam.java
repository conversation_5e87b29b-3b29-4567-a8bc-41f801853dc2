package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-06-20 11:52
 */
@ThriftStruct
public class PoiUnbindFeeEventGrayParam {

    private Long wmPoiId;

    private Long dealTime;

    @ThriftField(1)
    public Long getWmPoiId() {
        return wmPoiId;
    }

    @ThriftField
    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    @ThriftField(2)
    public Long getDealTime() {
        return dealTime;
    }

    @ThriftField
    public void setDealTime(Long dealTime) {
        this.dealTime = dealTime;
    }
}