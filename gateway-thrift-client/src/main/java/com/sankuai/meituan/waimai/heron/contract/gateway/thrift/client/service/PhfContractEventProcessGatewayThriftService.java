package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.exception.HeronContractGatewayThriftException;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfContractDrMigrateCleanParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfContractInvalidParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfBatchModifyVariableFeeParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfPoiBindSchoolStationParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.SinglePoiResult;
import org.apache.thrift.TException;

import java.util.List;

/**
 * PHF合同事件处理网关Thrift服务
 * <AUTHOR>
 * @date 2024/11/28
 */
@ThriftService
@InterfaceDoc(displayName = "PHF合同事件处理网关Thrift服务", type = "octo.thrift.annotation", description = "PHF合同事件处理网关Thrift服务", scenarios = "PHF合同事件处理网关Thrift服务")
public interface PhfContractEventProcessGatewayThriftService {

    @ThriftMethod
    @MethodDoc(description = "作废PHF合同")
    void invalidContract(PhfContractInvalidParam param) throws HeronContractGatewayThriftException, TException;

    @ThriftMethod
    @MethodDoc(description = "作废PHF双写阶段合同，在双写时PHF已解除合同后调用，双写结束后逐步下线")
    void invalidDrContract(PhfContractInvalidParam param) throws HeronContractGatewayThriftException, TException;


    @ThriftMethod
    @MethodDoc(description = "BD绑定校园站点")
    void bindSchoolStation(PhfPoiBindSchoolStationParam param) throws HeronContractGatewayThriftException, TException;

    @ThriftMethod
    @MethodDoc(description = "批量修改可变费率")
    List<SinglePoiResult> batchModifyVariableFee(PhfBatchModifyVariableFeeParam param) throws HeronContractGatewayThriftException, TException;


    @ThriftMethod
    @MethodDoc(description = "双写数据迁移清洗，参数为同一个门店的合同数据（最新+生效）从PHF迁移到一体化，迁移中部分失败则全部失败，迁移完成后逐步下线")
    void migrateToDrData(List<PhfContractDrMigrateCleanParam> poiParamList) throws HeronContractGatewayThriftException, TException;

    @ThriftMethod
    @MethodDoc(description = "拆分数据迁移清洗，履约合同数据+门店标签清洗")
    void migrateToSplit(Long wmPoiId) throws TException;

    @ThriftMethod
    @MethodDoc(description = "拼好饭迁移回滚")
    void migrateRollback(Long wmPoiId) throws TException;

}
