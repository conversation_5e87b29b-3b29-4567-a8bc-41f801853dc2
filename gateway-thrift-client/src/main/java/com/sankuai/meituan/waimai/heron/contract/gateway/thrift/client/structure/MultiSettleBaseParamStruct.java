package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @description: 多店自入驻原始入参
 * @author: chenyihao04
 * @create: 2023-05-29 09:59
 */
@TypeDoc(description = "多店入驻参数结构")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MultiSettleBaseParamStruct {

    /**
     * 外卖门店ID【必填】
     */
    @FieldDoc(description = "外卖门店ID")
    private Long wmPoiId;

    /**
     * 配送方式【必填】
     */
    @FieldDoc(description = "配送方式和服务产品")
    private List<LogisticsBrandProductParamStruct> serviceBrandProductParam;

    /**
     * 是否代理商
     */
    @FieldDoc(description = "是否代理商")
    private Boolean isAgent;

    /**
     * 代理商ID
     */
    @FieldDoc(description = "是否代理商")
    private Long agentId;

    /**
     * 主营品类（叶子节点）
     */
    @FieldDoc(description = "主营品类（叶子节点）")
    private Integer primaryCate;

    /**
     * 经度
     */
    @FieldDoc(description = "经度")
    private Long longitude;

    /**
     * 纬度
     */
    @FieldDoc(description = "纬度")
    private Long latitude;

    /**
     * 二级物理城市id
     */
    @FieldDoc(description = "二级物理城市id")
    private Long cityId;

    /**
     * 业务品牌ID
     */
    @FieldDoc(description = "业务品牌ID")
    private Long brandId;

    /**
     * 门店归属
     */
    @FieldDoc(description = "门店归属")
    private Integer ownerType;

    /**
     * 客户ID
     */
    @FieldDoc(description = "客户ID")
    private long customerId;

    /**
     * 客户ID
     */
    @FieldDoc(description = "客户ID")
    private Long wmCustomerId;

    /**
     * 业务类型
     */
    @FieldDoc(description = "业务类型")
    private Integer bizOrgCode;

    /**
     * 费率模式
     */
    @FieldDoc(description = "费率模式")
    private Integer feeMode;

    /**
     * 入驻任务ID
     */
    @FieldDoc(description = "入驻任务ID")
    private Long taskId;

    /**
     * 品牌类型
     */
    @FieldDoc(description = "品牌类型")
    private Integer brandType;

    /**
     * 门店营业时间
     */
    @FieldDoc(description = "门店营业时间")
    private String shippingTimeX;

    /**
     * 是否开通优惠
     */
    @FieldDoc(description = "是否开通优惠")
    private Boolean isOpenDiscount;

    /**
     * 优惠结束时间
     */
    @FieldDoc(description = "优惠结束时间")
    private String discountEndTime;

    /**
     * 指定配送方式的对应的配送费
     */
    @FieldDoc(description = "指定配送方式的对应的配送费")
    private Map<String, Double> deliveryFeeMap;

    /**
     * 指定配送方式的起送价
     */
    @FieldDoc(description = "指定配送方式的起送价")
    private Map<String, Double> initDeliveryPriceMap;

    /**
     * 代理平台使用费
     */
    @FieldDoc(description = "代理平台使用费")
    private Double agentPlatformFee;

    /**
     * 代理商业支持服务费
     */
    @FieldDoc(description = "代理商业支持服务费")
    private Double agentBusinessSupportFee;

}