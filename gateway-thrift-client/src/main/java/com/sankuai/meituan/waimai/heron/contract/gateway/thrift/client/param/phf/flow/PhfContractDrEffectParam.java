package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.flow;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/11/29
 */
@TypeDoc(description = "PHF合同双写阶段生效参数")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
@Data
public class PhfContractDrEffectParam {

    @FieldDoc(description = "门店ID")
    @ThriftField(1)
    private Long wmPoiId;

    @FieldDoc(description = "流程会话ID")
    @ThriftField(2)
    private Long sessionId;

    @FieldDoc(description = "生效时间")
    @ThriftField(3)
    private Long effectiveTime;

    @FieldDoc(description = "技术合同PDF URL")
    @ThriftField(4)
    private String techPdfUrl;

    @FieldDoc(description = "履约合同PDF URL")
    @ThriftField(5)
    private String perfPdfUrl;

    @FieldDoc(description = "操作人")
    @ThriftField(6)
    private HeronContractOperator operator;
}
