package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-07-03 19:19
 */
@ThriftStruct
public class LogisticsAreaView {

    public long id;
    public long poiId;
    public int sourceType;
    public int type;
    public int cls;
    public String clsName;
    public String logisticsCode;
    public String logisticstype;
    public int serviceProduct;
    public int spAreaType;
    public String area;
    public String areaHash;
    public long minLat;
    public long maxLat;
    public long minLng;
    public long maxLng;
    public long startTime;
    public long endTime;
    public int valid;
    public int affect;
    public String appShippingCode;
    public double shippingFee;
    public double minPrice;
    public int temp;
    public long ctime;
    public long utime;
    public long cuid;
    public long muid;
    public long auditId;
    public long batchAuditId;
    @FieldDoc(
            description = "活动标签,共32位,每位表示一类标签,如实验范围0000_0000..._0001 转换后等于1",
            example = {}
    )
    public Integer activityTag;
    @FieldDoc(
            description = "扩展属性,需要存储为对象,如{\"bucket\":1}",
            example = {}
    )
    public String extensionField;
    @FieldDoc(
            description = "标准配送费",
            example = {}
    )
    private String standardCBasicPrice;
    @FieldDoc(
            description = "标准配送费",
            example = {}
    )
    private String specialCBasicPrice;

    @ThriftField(1)
    public long getId() {
        return id;
    }

    @ThriftField
    public void setId(long id) {
        this.id = id;
    }

    @ThriftField(2)
    public long getPoiId() {
        return poiId;
    }

    @ThriftField
    public void setPoiId(long poiId) {
        this.poiId = poiId;
    }

    @ThriftField(3)
    public int getSourceType() {
        return sourceType;
    }

    @ThriftField
    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }

    @ThriftField(4)
    public int getType() {
        return type;
    }

    @ThriftField
    public void setType(int type) {
        this.type = type;
    }

    @ThriftField(5)
    public int getCls() {
        return cls;
    }

    @ThriftField
    public void setCls(int cls) {
        this.cls = cls;
    }

    @ThriftField(6)
    public String getClsName() {
        return clsName;
    }

    @ThriftField
    public void setClsName(String clsName) {
        this.clsName = clsName;
    }

    @ThriftField(7)
    public String getLogisticsCode() {
        return logisticsCode;
    }

    @ThriftField
    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }

    @ThriftField(8)
    public String getLogisticstype() {
        return logisticstype;
    }

    @ThriftField
    public void setLogisticstype(String logisticstype) {
        this.logisticstype = logisticstype;
    }

    @ThriftField(9)
    public int getServiceProduct() {
        return serviceProduct;
    }

    @ThriftField
    public void setServiceProduct(int serviceProduct) {
        this.serviceProduct = serviceProduct;
    }

    @ThriftField(10)
    public int getSpAreaType() {
        return spAreaType;
    }

    @ThriftField
    public void setSpAreaType(int spAreaType) {
        this.spAreaType = spAreaType;
    }

    @ThriftField(11)
    public String getArea() {
        return area;
    }

    @ThriftField
    public void setArea(String area) {
        this.area = area;
    }

    @ThriftField(12)
    public String getAreaHash() {
        return areaHash;
    }

    @ThriftField
    public void setAreaHash(String areaHash) {
        this.areaHash = areaHash;
    }

    @ThriftField(13)
    public long getMinLat() {
        return minLat;
    }

    @ThriftField
    public void setMinLat(long minLat) {
        this.minLat = minLat;
    }

    @ThriftField(14)
    public long getMaxLat() {
        return maxLat;
    }

    @ThriftField
    public void setMaxLat(long maxLat) {
        this.maxLat = maxLat;
    }

    @ThriftField(15)
    public long getMinLng() {
        return minLng;
    }

    @ThriftField
    public void setMinLng(long minLng) {
        this.minLng = minLng;
    }

    @ThriftField(16)
    public long getMaxLng() {
        return maxLng;
    }

    @ThriftField
    public void setMaxLng(long maxLng) {
        this.maxLng = maxLng;
    }

    @ThriftField(17)
    public long getStartTime() {
        return startTime;
    }

    @ThriftField
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    @ThriftField(18)
    public long getEndTime() {
        return endTime;
    }

    @ThriftField
    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    @ThriftField(19)
    public int getValid() {
        return valid;
    }

    @ThriftField
    public void setValid(int valid) {
        this.valid = valid;
    }

    @ThriftField(20)
    public int getAffect() {
        return affect;
    }

    @ThriftField
    public void setAffect(int affect) {
        this.affect = affect;
    }

    @ThriftField(21)
    public String getAppShippingCode() {
        return appShippingCode;
    }

    @ThriftField
    public void setAppShippingCode(String appShippingCode) {
        this.appShippingCode = appShippingCode;
    }

    @ThriftField(22)
    public double getShippingFee() {
        return shippingFee;
    }

    @ThriftField
    public void setShippingFee(double shippingFee) {
        this.shippingFee = shippingFee;
    }

    @ThriftField(23)
    public double getMinPrice() {
        return minPrice;
    }

    @ThriftField
    public void setMinPrice(double minPrice) {
        this.minPrice = minPrice;
    }

    @ThriftField(24)
    public int getTemp() {
        return temp;
    }

    @ThriftField
    public void setTemp(int temp) {
        this.temp = temp;
    }

    @ThriftField(25)
    public long getCtime() {
        return ctime;
    }

    @ThriftField
    public void setCtime(long ctime) {
        this.ctime = ctime;
    }

    @ThriftField(26)
    public long getUtime() {
        return utime;
    }

    @ThriftField
    public void setUtime(long utime) {
        this.utime = utime;
    }

    @ThriftField(27)
    public long getCuid() {
        return cuid;
    }

    @ThriftField
    public void setCuid(long cuid) {
        this.cuid = cuid;
    }

    @ThriftField(28)
    public long getMuid() {
        return muid;
    }

    @ThriftField
    public void setMuid(long muid) {
        this.muid = muid;
    }

    @ThriftField(29)
    public long getAuditId() {
        return auditId;
    }

    @ThriftField
    public void setAuditId(long auditId) {
        this.auditId = auditId;
    }

    @ThriftField(30)
    public long getBatchAuditId() {
        return batchAuditId;
    }

    @ThriftField
    public void setBatchAuditId(long batchAuditId) {
        this.batchAuditId = batchAuditId;
    }

    @ThriftField(31)
    public Integer getActivityTag() {
        return activityTag;
    }

    @ThriftField
    public void setActivityTag(Integer activityTag) {
        this.activityTag = activityTag;
    }

    @ThriftField(32)
    public String getExtensionField() {
        return extensionField;
    }

    @ThriftField
    public void setExtensionField(String extensionField) {
        this.extensionField = extensionField;
    }

    @ThriftField(33)
    public String getStandardCBasicPrice() {
        return standardCBasicPrice;
    }

    @ThriftField
    public void setStandardCBasicPrice(String standardCBasicPrice) {
        this.standardCBasicPrice = standardCBasicPrice;
    }

    @ThriftField(34)
    public String getSpecialCBasicPrice() {
        return specialCBasicPrice;
    }

    @ThriftField
    public void setSpecialCBasicPrice(String specialCBasicPrice) {
        this.specialCBasicPrice = specialCBasicPrice;
    }
}