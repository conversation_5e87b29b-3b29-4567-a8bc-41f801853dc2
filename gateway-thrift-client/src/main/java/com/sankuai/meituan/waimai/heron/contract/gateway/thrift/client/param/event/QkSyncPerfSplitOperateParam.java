package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-14 20:14
 */
@TypeDoc(description = "企客解耦刷数据到拆分")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
public class QkSyncPerfSplitOperateParam {

    @FieldDoc(description = "门店ID")
    private Long wmPoiId;

    @FieldDoc(description = "操作人")
    private HeronContractOperator operator;

    @ThriftField(1)
    public Long getWmPoiId() {
        return wmPoiId;
    }

    @ThriftField
    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    @ThriftField(2)
    public HeronContractOperator getOperator() {
        return operator;
    }

    @ThriftField
    public void setOperator(HeronContractOperator operator) {
        this.operator = operator;
    }
}