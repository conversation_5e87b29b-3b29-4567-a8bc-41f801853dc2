package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.query;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;

/**
 * @description: by SessionCategory查询门店生效配送合同拆分标签参数
 * @author: chenyihao04
 * @create: 2024-08-21 10:10
 */
@ThriftStruct
@TypeDoc(description = "by SessionCategory查询门店生效配送合同拆分标签参数")
public class LogisticsExtTagsQueryParam {

    private Long wmPoiId;

    private SessionCategoryEnum sessionCategory;

    private Boolean rt;

    @ThriftField(1)
    public Long getWmPoiId() {
        return wmPoiId;
    }

    @ThriftField
    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    @ThriftField(2)
    public SessionCategoryEnum getSessionCategory() {
        return sessionCategory;
    }

    @ThriftField
    public void setSessionCategory(SessionCategoryEnum sessionCategory) {
        this.sessionCategory = sessionCategory;
    }

    @ThriftField(3)
    public Boolean getRt() {
        return rt;
    }

    @ThriftField
    public void setRt(Boolean rt) {
        this.rt = rt;
    }
}