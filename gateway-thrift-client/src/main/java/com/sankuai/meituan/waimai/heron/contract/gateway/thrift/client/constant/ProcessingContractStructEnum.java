package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant;

import java.util.Objects;

public enum ProcessingContractStructEnum {
    CONTRACT_STRUCT(1, " 新合同结构"),
    OLD_STRUCT(2, "老结构"),
    SG_STRUCT(3, "闪购结构");

    private final int code;
    private final String desc;

    ProcessingContractStructEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Boolean isContractStruct(){
        return Objects.equals(this, CONTRACT_STRUCT);
    }

    public Boolean isOldStruct(){
        return Objects.equals(this, OLD_STRUCT);
    }

    public Boolean isSgStruct(){
        return Objects.equals(this, SG_STRUCT);
    }
}
