package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.annotation.StructureDefinition;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.BdSettleSaveBaseParamStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/6/6
 */
@TypeDoc(description = "先富入驻判断是否需要签约参数")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
public class HeronContractBDSettleJudgeSignParam {

    /**
     * 商家id
     */
    @FieldDoc(description = "商家id")
    private Long wmPoiId;

    /**
     * 先富入驻和换签的参数
     */
    @StructureDefinition(structClass = BdSettleSaveBaseParamStruct.class, description = "先富单店入驻")
    @FieldDoc(description = "先富入驻和换签的参数")
    private String bdSaveBaseParam;

    /**
     * 操作人
     */
    @FieldDoc(description = "操作人")
    private HeronContractOperator operator;

    /**
     * 流程分组
     */
    @FieldDoc(description = "流程分组")
    private SessionCategoryEnum sessionCategory;


    @ThriftField(1)
    public Long getWmPoiId() {
        return wmPoiId;
    }

    @ThriftField
    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    @ThriftField(2)
    public String getBdSaveBaseParam() {
        return bdSaveBaseParam;
    }

    @ThriftField
    public void setBdSaveBaseParam(String bdSaveBaseParam) {
        this.bdSaveBaseParam = bdSaveBaseParam;
    }

    @ThriftField(3)
    public HeronContractOperator getOperator() {
        return operator;
    }

    @ThriftField
    public void setOperator(HeronContractOperator operator) {
        this.operator = operator;
    }

    @ThriftField(4)
    public SessionCategoryEnum getSessionCategory() {
        return sessionCategory;
    }

    @ThriftField
    public void setSessionCategory(SessionCategoryEnum sessionCategory) {
        this.sessionCategory = sessionCategory;
    }
}
