package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2024-04-26 15:20
 */
@TypeDoc(description = "医药迁移回滚")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
public class YyTransferRollBackParam {

    @FieldDoc(description = "门店ID")
    private Long wmPoiId;

    @FieldDoc(description = "失败的sessionId")
    private Long failSessionId;

    @FieldDoc(description = "恢复的目标sessionId")
    private Long targetSessionId;

    @FieldDoc(description = "门店费率模式")
    private Integer feeMode;

    @FieldDoc(description = "恢复的目标合同版本id")
    private Long contractVersionId;

    @FieldDoc(description = "回滚后的tags")
    private String rollbackTags;

    @ThriftField(1)
    public Long getWmPoiId() {
        return wmPoiId;
    }

    @ThriftField
    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    @ThriftField(2)
    public Long getFailSessionId() {
        return failSessionId;
    }

    @ThriftField
    public void setFailSessionId(Long failSessionId) {
        this.failSessionId = failSessionId;
    }

    @ThriftField(3)
    public Long getTargetSessionId() {
        return targetSessionId;
    }

    @ThriftField
    public void setTargetSessionId(Long targetSessionId) {
        this.targetSessionId = targetSessionId;
    }

    @ThriftField(4)
    public Integer getFeeMode() {
        return feeMode;
    }

    @ThriftField
    public void setFeeMode(Integer feeMode) {
        this.feeMode = feeMode;
    }

    @ThriftField(5)
    public Long getContractVersionId() {
        return contractVersionId;
    }

    @ThriftField
    public void setContractVersionId(Long contractVersionId) {
        this.contractVersionId = contractVersionId;
    }

    @ThriftField(6)
    public String getRollbackTags() {
        return rollbackTags;
    }

    @ThriftField
    public void setRollbackTags(String rollbackTags) {
        this.rollbackTags = rollbackTags;
    }
}