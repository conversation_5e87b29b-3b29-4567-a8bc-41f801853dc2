package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/11/27
 */
@TypeDoc(description = "PHF合同信息参数")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
@Data
public class PhfContractInfoParam {

    @FieldDoc(description = "门店ID")
    @ThriftField(1)
    private Long wmPoiId;

    @FieldDoc(description = "PHF合同ID")
    @ThriftField(2)
    private Long phfContractId;

    @FieldDoc(description = "双写：拼好送正式版，PHF履约合同ID")
    @ThriftField(3)
    private Long phfPerfContractId;

    @FieldDoc(description = "PHF合同编号")
    @ThriftField(4)
    private String phfContractCode;

    @FieldDoc(description = "双写：PHF原流程电子合同记录key")
    @ThriftField(5)
    private String econtractRecordKey;

    @FieldDoc(description = "PHF任务ID, phf_batchsign_poisign_task表的主键id")
    @ThriftField(6)
    private Long phfPoiTaskId;

    @FieldDoc(description = "PHF合同费率版本")
    @ThriftField(7)
    private String phfFeeVersion;


}
