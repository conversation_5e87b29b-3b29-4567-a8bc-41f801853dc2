package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.phf.query;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.ContractFeeItem;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ContractVersionData;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.WmLogisticsContractItemDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfContractBaseInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/1
 */
@TypeDoc(description = "PHF合同聚合数据")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ThriftStruct
@Data
public class PhfContractAggreInfoResult {

    @FieldDoc(description = "门店id")
    @ThriftField(1)
    private Long wmPoiId;

    @FieldDoc(description = "合同基础信息")
    @ThriftField(2)
    private PhfContractBaseInfoDTO contractBaseInfo;

    @FieldDoc(description = "履约合同基础信息")
    @ThriftField(3)
    private ContractVersionData contractVersionData;

    @FieldDoc(description = "平台合同费率项")
    @ThriftField(4)
    private List<WmLogisticsContractItemDTO> platContractItemList;

    @FieldDoc(description = "履约合同费率项")
    @ThriftField(5)
    private List<ContractFeeItem> perfContractItemList;



}
