package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.exception;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.service.mobile.mtthrift.annotation.AbstractThriftException;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

/**
 * <AUTHOR>
 * @date 2023/4/23
 */
@TypeDoc(description = "网关服务rpc异常")
@ThriftStruct
public class HeronContractGatewayThriftException extends AbstractThriftException {

    private int code;

    private String message;

    private String causeExceptionName;

    @ThriftConstructor
    public HeronContractGatewayThriftException() {
    }

    public HeronContractGatewayThriftException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public HeronContractGatewayThriftException(int code, Exception cause) {
        this.code = code;
        if (cause != null) {
            this.message = cause.getMessage();
            this.causeExceptionName = cause.getClass().getName();
        }
    }

    public HeronContractGatewayThriftException(int code, String message, Exception cause) {
        this.code = code;
        this.message = message;
        if (cause != null) {
            this.causeExceptionName = cause.getClass().getName();
        }
    }

    public HeronContractGatewayThriftException(AbstractThriftException other) {
        super(other);
        if (other != null) {
            this.causeExceptionName = other.getClass().getName();
        }
    }

    @ThriftField(1)
    public int getCode() {
        return code;
    }

    @ThriftField
    public void setCode(int code) {
        this.code = code;
    }

    @Override
    @ThriftField(2)
    public String getMessage() {
        return message;
    }

    @ThriftField
    public void setMessage(String message) {
        this.message = message;
    }

    @ThriftField(3)
    public String getCauseExceptionName() {
        return causeExceptionName;
    }

    @ThriftField
    public void setCauseExceptionName(String causeExceptionName) {
        this.causeExceptionName = causeExceptionName;
    }
}
