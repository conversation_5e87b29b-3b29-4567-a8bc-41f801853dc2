package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-18 11:21
 */
@TypeDoc(description = "平台共享费率信息结构")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlatShareFeeInfoStruct {

    /**
     * 特批有效期
     */
    @FieldDoc(
            description = "特批有效期",
            example = {}
    )
    private String specialEndTime;

    /**
     * 履约服务费特批折扣系数
     */
    @FieldDoc(
            description = "履约服务费特批折扣系数",
            example = {}
    )
    private String specialPerfRate;

    /**
     * 履约服务费优惠折扣系数
     */
    @FieldDoc(
            description = "履约服务费优惠折扣系数",
            example = {}
    )
    private String discountPerfRate;
}