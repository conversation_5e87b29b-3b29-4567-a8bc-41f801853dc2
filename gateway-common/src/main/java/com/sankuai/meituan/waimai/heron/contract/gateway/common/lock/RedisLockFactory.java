package com.sankuai.meituan.waimai.heron.contract.gateway.common.lock;

import com.sankuai.meituan.waimai.kv.grouppoi.domain.BizLockKey;
import com.sankuai.meituan.waimai.kv.grouppoi.service.RedisLockHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RedisLockFactory {

    @Autowired
    private RedisLockHandler redisLockHandler;




    public Lock lock(final BizLockKey bizLockKey, String key) {
        return new Lock(redisLockHandler, bizLockKey, key);
    }



}