package com.sankuai.meituan.waimai.heron.contract.gateway.common.utils;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/2
 */
@Component
public class SpringBeanUtil implements ApplicationContextAware {

    private static ApplicationContext ctx;

    public SpringBeanUtil() {
    }

    public static Object getBean(String name) {
        return ctx.getBean(name);
    }

    public static <T> T getBean(String name, Class<T> requiredType) {
        return ctx.getBean(name, requiredType);
    }

    public static <T> T getBean(Class<T> clazz) {
        T t = ctx.getBean(clazz);
        return t;
    }

    public static <T> Map<String, T> getBeansOfType(Class<T> clazz) {
        return ctx.getBeansOfType(clazz);
    }

    public static boolean hasBeanOfType(Class clazz) {
        return MapUtils.isNotEmpty(ctx.getBeansOfType(clazz));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationcontext) throws BeansException {
        ctx = applicationcontext;
    }
}
