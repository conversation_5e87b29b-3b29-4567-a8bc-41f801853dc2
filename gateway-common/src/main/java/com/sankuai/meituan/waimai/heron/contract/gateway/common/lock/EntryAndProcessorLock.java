package com.sankuai.meituan.waimai.heron.contract.gateway.common.lock;

import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 表示mission或event的方法需要按门店增加同步锁，门店id必须是long类型
 *
 * <AUTHOR>
 * @date 2022/3/2
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface EntryAndProcessorLock {

    WmPoiIdExtractTypeEnum extractType() default WmPoiIdExtractTypeEnum.PARAM_FIELD;

    String wmPoiIdFieldName() default "wmPoiId";

    SessionCategoryEnum[] sessionCategory() default {};

    String sessionCategoryFieldName() default "sessionCategory";


    int argIndex() default 0;


    AcquireFailActionEnum acquireFailAction() default AcquireFailActionEnum.WAIT;

    boolean supportReentry() default false;

    enum WmPoiIdExtractTypeEnum {
        //取args[index]的arg作为门店id
        PARAM_INDEX,
        //取args[index]的arg.field的作为门店id
        PARAM_FIELD
    }


}
