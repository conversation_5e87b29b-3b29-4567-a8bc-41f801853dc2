package com.sankuai.meituan.waimai.heron.contract.gateway.common.lock;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.waimai.kv.grouppoi.domain.BizLockKey;
import com.sankuai.meituan.waimai.kv.grouppoi.domain.LockResult;
import com.sankuai.meituan.waimai.kv.grouppoi.service.RedisLockHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

@Slf4j
public class Lock implements RLock {

    private RedisLockHandler redisLockHandler;

    private BizLockKey bizLockKey;

    private String key;

    private LockResult lockResult;

    Lock(RedisLockHandler redisLockHandler, BizLockKey bizLockKey, String key) {
        Preconditions.checkNotNull(redisLockHandler, "Redis Lock Handler can't found");
        Preconditions.checkNotNull(bizLockKey, "bizLockKey can't be null");
        Preconditions.checkNotNull(key, "redis key name can't be null");
        this.redisLockHandler = redisLockHandler;
        this.bizLockKey = bizLockKey;
        this.key = key;
    }

    @Override
    public boolean tryLock(long waitTime, long leaseTime, TimeUnit timeUnit) {
        log.info("lockKey: {} tryLock waitTime: {} leaseTime: {} timeUnit: {}", key, waitTime, leaseTime, timeUnit);
        if (waitTime < 0) {
            return false;
        }
        if (timeUnit.toMillis(leaseTime) < 1000) {
            // fix expire<1000 bug
            throw new IllegalArgumentException("expireTime must greater than 1000!!!");
        }
        lockResult = redisLockHandler.tryLock(bizLockKey, key, timeUnit.toMillis(waitTime),
                timeUnit.toMillis(leaseTime));
        return lockResult.getCode();
    }

    @Override
    public void unLock() {
        redisLockHandler.unLock(bizLockKey, key, lockResult);
    }

    @Override
    public String getKey() {
        return bizLockKey+":"+key;
    }
}
