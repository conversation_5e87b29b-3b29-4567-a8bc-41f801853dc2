package com.sankuai.meituan.waimai.heron.contract.gateway.common.exception;

/**
 * @description: 服务售卖网关运行时异常
 * @author: chenyihao04
 * @create: 2023-06-12 20:08
 */
public class GatewayRuntimeException extends RuntimeException {

    public GatewayRuntimeException(String message, Throwable cause) {
        super(message, cause);
    }

    public GatewayRuntimeException(String message) {
        super(message);
    }

    public GatewayRuntimeException(Throwable cause) {
        super(cause);
    }
}