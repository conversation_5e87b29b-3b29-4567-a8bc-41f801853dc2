package com.sankuai.meituan.waimai.heron.contract.gateway.common.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySerializeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nullable;


/**
 * <AUTHOR>
 * @since 15 三月 2020
 */
@Slf4j
public class JacksonUtil {

    private final static Supplier<ObjectMapper> OBJECT_MAPPER_SUPPLIER = Suppliers.memoize(() -> {

                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                return objectMapper;
            }
    );


    private final static Supplier<ObjectMapper> IGNORE_NULL_OBJECT_MAPPER_SUPPLIER = Suppliers.memoize(() -> {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
                return objectMapper;
            }
    );


    public static ObjectMapper getMapper() {
        return OBJECT_MAPPER_SUPPLIER.get();

    }

    public static ObjectMapper getMapperForIgnoreNull() {
        return IGNORE_NULL_OBJECT_MAPPER_SUPPLIER.get();
    }
    /**
     * 获取对象的副本（深拷贝）
     */
    @Nullable
    public static <T> T getCopyOf(T source, Class<T> clz) {
        if (source == null) {
            return null;
        }

        try {
            String json = getMapper().writeValueAsString(source);
            return getMapper().readValue(json, clz);
        } catch (JsonProcessingException e) {
            throw new GatewaySerializeException("对象深拷贝异常", e);
        }
    }

    /**
     * 获取对象的副本（深拷贝）
     */
    @Nullable
    public static <T> T getCopyOf(T source, TypeReference<T> tf) {
        if (source == null) {
            return null;
        }

        try {
            String json = getMapper().writeValueAsString(source);
            return getMapper().readValue(json, tf);
        } catch (JsonProcessingException e) {
            throw new GatewaySerializeException("对象深拷贝异常", e);
        }
    }

    public static String writeAsJsonStr(Object value) {
        try {
            return getMapper().writeValueAsString(value);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            return "";
        }
    }

    public static <T> T readValue(String json, Class<T> clz) {
        try {
            if (StringUtils.isBlank(json)) {
                return null;
            }
            return getMapper().readValue(json, clz);
        } catch (Exception e) {
            log.warn("readValue json={} exception", json, e);
            return null;
        }
    }


    public static <T> T readValueNotNull(String json, Class<T> clz) throws GatewayArgumentException {
        try {
            if (StringUtils.isBlank(json)) {
                log.warn("readValueNotNull json为空");
                throw new GatewayArgumentException("json为空");
            }
            return getMapper().readValue(json, clz);
        } catch (Exception e) {
            log.warn("readValueNotNull json={} exception", json, e);
            throw new GatewayArgumentException(e.getMessage());
        }
    }

    public static <T> T readValue(String json, TypeReference<T> reference) {
        try {
            if (StringUtils.isBlank(json)) {
                return null;
            }
            return getMapper().readValue(json, reference);
        } catch (Exception e) {
            log.warn("readValue json={} exception", json, e);
            return null;
        }
    }

    public static <T> T readValueNotNull(String json, TypeReference<T> reference) throws GatewayArgumentException {
        try {
            if (StringUtils.isBlank(json)) {
                log.warn("readValueNotNull json为空");
                throw new GatewayArgumentException("json为空");
            }
            return getMapper().readValue(json, reference);
        } catch (Exception e) {
            log.warn("readValueNotNull json={} exception", json, e);
            throw new GatewayArgumentException(e.getMessage());
        }
    }

    public static JsonNode readTree(String json) {
        try {
            return getMapper().readTree(json);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            return null;
        }
    }

    public static JsonNode readTree(Object obj) {
        try {
            String json = getMapper().writeValueAsString(obj);
            return getMapper().readTree(json);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            return null;
        }
    }

    public static ObjectNode createObjectNode() {
        return getMapper().createObjectNode();
    }

    public static ArrayNode createArrayNode() {
        return getMapper().createArrayNode();
    }

    public static String readValue(String json, String property) {
        JsonNode jsonNode = readTree(json);
        if (null == jsonNode) {
            return null;
        }
        JsonNode valueNode = jsonNode.get(property);
        if (null == valueNode) {
            return null;
        }
        return valueNode.asText();
    }

    public static String writeAsJsonStrIgnoreNull(Object value) {
        try {
            return getMapperForIgnoreNull().writeValueAsString(value);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            return "";
        }
    }

}
