package com.sankuai.meituan.waimai.heron.contract.gateway.common.lock;

import com.dianping.squirrel.client.StoreKey;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mtrace.Tracer;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.config.MccConfig;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBusinessException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.redis.RedisKvService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.kv.grouppoi.domain.BizLockKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.GatewayConstants.GATEWAY_REENTRY_CATEGORY;

/**
 * <AUTHOR>
 * @date 2023/7/13
 */
@Component
@Slf4j
public class EntryAndProcessorLockHandler {

    @Resource
    private RedisLockFactory redisLockFactory;
    @Resource
    private RedisKvService redisKvService;

    private ThreadLocal<Set<Long>> hasLockTagThreadPoiIdThreadLocal = new ThreadLocal<>();

    private ThreadLocal<Set<Long>> getHasLockTagThreadPoiIdThreadLocal() {
        return hasLockTagThreadPoiIdThreadLocal;
    }


    private static final ExecutorServiceTraceWrapper BATCH_POI_LOCK_EXECUTOR = new ExecutorServiceTraceWrapper(new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors() * 4, 30, TimeUnit.SECONDS, new ArrayBlockingQueue<>(500), new ThreadPoolExecutor.AbortPolicy()));

    public <T, R> R batchApplyWithLock(List<Long> wmPoiIdList, SessionCategoryEnum sessionCategory, T functionParam, LockedFunction<T, R> function) throws Throwable {
        return batchApplyWithLock(wmPoiIdList, sessionCategory, functionParam, function, false);
    }


    public <T, R> R batchApplyWithLock(List<Long> wmPoiIdList, SessionCategoryEnum sessionCategory, T functionParam, LockedFunction<T, R> function, Boolean supportReentry) throws Throwable {
        log.info("batchApplyWithLock wmPoiIdList: {} functionParam: {} functionClazz: {}", wmPoiIdList, JacksonUtil.writeAsJsonStr(functionParam), function.getClass().getName());
        long entryAndProcessorLockLeaseTime = MccConfig.getEntryAndProcessorLockLeaseTime();
        Map<Long, Future<List<Lock>>> lockFutureMap = Maps.newHashMap();
        Map<Long, List<Lock>> wmPoiLockMap = Maps.newHashMap();
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            for (Long wmPoiId : wmPoiIdList) {
                Set<Long> lockedWmPoiIdSet = this.getHasLockTagThreadPoiIdThreadLocal().get();
                if (supportReentry && matchReentryCondition(wmPoiId)) {
                    continue;
                }
                if (lockedWmPoiIdSet == null || !lockedWmPoiIdSet.contains(wmPoiId)) {
                    Future<List<Lock>> lockFuture = BATCH_POI_LOCK_EXECUTOR.submit(() -> {
                        List<Lock> lockList = buildLockList(wmPoiId, Lists.newArrayList(sessionCategory));
                        //多门店情况就不关注是否失败了
                        lockList.forEach(lock -> lock.tryLock(entryAndProcessorLockLeaseTime, entryAndProcessorLockLeaseTime, TimeUnit.MILLISECONDS));
                        return lockList;
                    });
                    lockFutureMap.put(wmPoiId, lockFuture);
                } else {
                    log.info("batchApplyWithLock 该线程和门店: {} 已经获取过锁，直接放行", wmPoiId);
                }
            }
            for (Map.Entry<Long, Future<List<Lock>>> lockFutureEntry : lockFutureMap.entrySet()) {
                Long wmPoiId = lockFutureEntry.getKey();
                List<Lock> lockList = lockFutureEntry.getValue().get();
                wmPoiLockMap.put(wmPoiId, lockList);
            }
        } catch (Exception e) {
            log.warn("batchApplyWithLock 批量门店锁失败 wmPoiIdList: {}", wmPoiIdList, e);
        }
        log.info("batchApplyWithLock lock 门店数量: {} 耗时: {} ms", wmPoiIdList.size(), stopWatch.getTime());
        try {
            stopWatch.reset();
            stopWatch.start();
            for (Long wmPoiId : wmPoiLockMap.keySet()) {
                setHasLockWmPoiId(wmPoiId);
                addReentryCache(wmPoiId, function);
            }
            R result = function.apply(functionParam);
            log.info("batchApplyWithLock functionApply 门店数量: {} 耗时: {} ms", wmPoiIdList.size(), stopWatch.getTime());
            return result;
        } finally {
            stopWatch.reset();
            stopWatch.start();
            try {
                for (Map.Entry<Long, List<Lock>> lockEntry : wmPoiLockMap.entrySet()) {
                    Long wmPoiId = lockEntry.getKey();
                    List<Lock> lockList = lockEntry.getValue();
                    removeUnLockWmPoiId(wmPoiId);
                    removeReentryCache(wmPoiId);
                    if (CollectionUtils.isNotEmpty(lockList)) {
                        lockList.forEach(Lock::unLock);
                    }
                }
                log.info("batchApplyWithLock unLock 门店数量: {} 耗时: {} ms", wmPoiIdList.size(), stopWatch.getTime());
            } catch (Exception e) {
                log.warn("batchApplyWithLock 批量门店释放锁失败 wmPoiIdList: {}", wmPoiIdList, e);
            }
        }
    }

    public <T, R> R applyWithLock(Long wmPoiId, SessionCategoryEnum sessionCategoryEnum, T functionParam, LockedFunction<T, R> function) throws Throwable {
        return this.applyWithLock(wmPoiId, Lists.newArrayList(sessionCategoryEnum), AcquireFailActionEnum.WAIT, true, functionParam, function);
    }

    public <T, R> R applyWithLock(Long wmPoiId, List<SessionCategoryEnum> sessionCategoryList, AcquireFailActionEnum failActionEnum, Boolean supportReentry, T functionParam, LockedFunction<T, R> function) throws Throwable {
        log.info("applyWithLock wmPoiId: {} failActionEnum: {}, supportReentry: {}, functionParam: {}, functionClazz: {}", wmPoiId, failActionEnum, supportReentry, JacksonUtil.writeAsJsonStr(functionParam), function.getClass().getName());
        Set<Long> lockedWmPoiIdSet = this.getHasLockTagThreadPoiIdThreadLocal().get();
        //可重入锁，避免同一调用链都有注解需要同步锁，在获取锁时发送死锁；加入sessionCategory概念后，需要注意在
        if (lockedWmPoiIdSet != null && lockedWmPoiIdSet.contains(wmPoiId)) {
            log.info("applyWithLock 该线程和门店: {} 已经获取过锁，直接放行", wmPoiId);
            return function.apply(functionParam);
        }
        if (supportReentry && matchReentryCondition(wmPoiId)) {
            return function.apply(functionParam);
        }
        log.info("applyWithLock entryAndProcessorLock failActionEnum: {} wmPoiId: {}", failActionEnum, wmPoiId);
        if (AcquireFailActionEnum.WAIT == failActionEnum) {
            return proceedWithWaitLock(wmPoiId, sessionCategoryList, functionParam, function);
        } else {
            return proceedWithFailFastLock(wmPoiId, sessionCategoryList, functionParam, function);
        }
    }


    private <T, R> R proceedWithWaitLock(Long wmPoiId, List<SessionCategoryEnum> sessionCategoryList, T param, LockedFunction<T, R> function) throws Throwable {
        long entryAndProcessorLockLeaseTime = MccConfig.getEntryAndProcessorLockLeaseTime();
        List<Lock> lockList = null;
        boolean lockSuccess = false;
        try {
            lockList = buildLockList(wmPoiId, sessionCategoryList);
            lockSuccess = lockList.stream().map(lock -> lock.tryLock(entryAndProcessorLockLeaseTime, entryAndProcessorLockLeaseTime, TimeUnit.MILLISECONDS)).allMatch(BooleanUtils::isTrue);
        } catch (Exception e) {
            //获取锁失败，不进行同步处理，记录日志
            log.error("proceedWithWaitLock wmPoiId: {} 获取同步锁失败", wmPoiId, e);
        }
        //获取锁失败，线程sleep一段时间，避免出现并行
        if (!lockSuccess) {
            log.warn("proceedWithWaitLock 获取锁失败 wmPoiId: {}", wmPoiId);
            TimeUnit.MILLISECONDS.sleep(MccConfig.getLockFailedThreadSleepTime());
        }
        try {
            setHasLockWmPoiId(wmPoiId);
            addReentryCache(wmPoiId, function);
            return function.apply(param);
        } finally {
            try {
                removeUnLockWmPoiId(wmPoiId);
                removeReentryCache(wmPoiId);
                if (CollectionUtils.isNotEmpty(lockList)) {
                    lockList.forEach(Lock::unLock);
                }
            } catch (Exception e) {
                log.error("proceedWithWaitLock finally 释放锁 执行失败 wmPoiId: {}", wmPoiId, e);
            }
        }
    }

    private List<Lock> buildLockList(Long wmPoiId, List<SessionCategoryEnum> sessionCategoryList) {
        List<Lock> lockList = sessionCategoryList.stream().map(sessionCategory -> redisLockFactory.lock(BizLockKey.POI_LOGISTICS_LOCK, wmPoiId + LockKeyConstants.MISSION_AND_EVENT_SYNC + "_" + sessionCategory.name())).collect(Collectors.toList());
        if (MccConfig.getUseNonSessionCategoryLock()) {
            lockList.add(redisLockFactory.lock(BizLockKey.POI_LOGISTICS_LOCK, wmPoiId + LockKeyConstants.MISSION_AND_EVENT_SYNC));
        }
        return lockList;
    }


    private <T, R> R proceedWithFailFastLock(Long wmPoiId, List<SessionCategoryEnum> sessionCategoryList, T param, LockedFunction<T, R> function) throws Throwable {
        //锁过期时间，和等待锁的时间保持一致
        long entryAndProcessorLockLeaseTime = MccConfig.getEntryAndProcessorLockLeaseTime();
        //这个时间远小于锁过期时间（一般100毫秒），获取不到锁就返回失败
        long entryAndProcessorLockFailWaitTime = MccConfig.getEntryAndProcessorLockFailWaitTime();
        List<Lock> lockList = null;
        boolean lockSuccess = false;
        try {
            lockList = buildLockList(wmPoiId, sessionCategoryList);
            lockSuccess = lockList.stream().map(lock -> lock.tryLock(entryAndProcessorLockFailWaitTime, entryAndProcessorLockLeaseTime, TimeUnit.MILLISECONDS)).allMatch(BooleanUtils::isTrue);
        } catch (Exception e) {
            //获取锁失败，不进行同步处理，记录日志，调用目标方法
            log.error("proceedWithFailFastLock  wmPoiId: {} 获取同步锁失败", wmPoiId, e);
            throw e;
        }
        if (!lockSuccess) {
            log.warn("proceedWithFailFastLock 未获取到锁 wmPoiId: {} ", wmPoiId);
            throw new GatewayBusinessException(-1, "获取分布式锁失败");
        }
        try {
            setHasLockWmPoiId(wmPoiId);
            addReentryCache(wmPoiId, function);
            return function.apply(param);
        } finally {
            try {
                removeUnLockWmPoiId(wmPoiId);
                removeReentryCache(wmPoiId);
                if (CollectionUtils.isNotEmpty(lockList)) {
                    lockList.forEach(Lock::unLock);
                }
            } catch (Exception e) {
                log.error("proceedWithFailFastLock finally 执行失败 释放锁失败 wmPoiId: {}", wmPoiId, e);
            }
        }
    }

    /**
     * 引入sessionCategory概念后，依旧是traceId+wmPoiId就可重入。考虑多个锁的分别或一起重入较复杂
     */
    private boolean matchReentryCondition(Long wmPoiId) {
        try {
            StoreKey resultKeyStore = new StoreKey(GATEWAY_REENTRY_CATEGORY, Tracer.id() + "_" + wmPoiId);
            String reentryValue = redisKvService.get(resultKeyStore);
            if (StringUtils.isNotBlank(reentryValue)) {
                log.info("matchReentryCondition 该Trace和门店: {} 已经获取过锁，直接放行，该锁是 {} 获取到的", wmPoiId, reentryValue);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.warn("判断是否满足重入条件异常, 按不可重入处理, wmPoiId: {}", wmPoiId);
            return false;
        }
    }

    private <T, R> void addReentryCache(Long wmPoiId, LockedFunction<T, R> function) {
        try {
            StoreKey resultKeyStore = new StoreKey(GATEWAY_REENTRY_CATEGORY, Tracer.id() + "_" + wmPoiId);
            redisKvService.set(resultKeyStore, function.getClass().getName(), MccConfig.getReentryCacheDuration());
        } catch (Exception e) {
            log.warn("addReentryCache error, ", e);
        }
    }

    private void removeReentryCache(Long wmPoiId) {
        try {
            StoreKey resultKeyStore = new StoreKey(GATEWAY_REENTRY_CATEGORY, Tracer.id() + "_" + wmPoiId);
            redisKvService.delete(resultKeyStore);
        } catch (Exception e) {
            log.warn("removeReentryCache error, ", e);
        }
    }


    private void setHasLockWmPoiId(Long wmPoiId) {
        Set<Long> wmPoiIdSet = this.getHasLockTagThreadPoiIdThreadLocal().get();
        if (wmPoiIdSet == null) {
            this.getHasLockTagThreadPoiIdThreadLocal().set(Sets.newHashSet(wmPoiId));
        } else {
            wmPoiIdSet.add(wmPoiId);
        }
    }

    private void removeUnLockWmPoiId(Long wmPoiId) {
        Set<Long> wmPoiIdSet = this.getHasLockTagThreadPoiIdThreadLocal().get();
        if (wmPoiIdSet.size() == 1) {
            this.getHasLockTagThreadPoiIdThreadLocal().remove();
        } else {
            this.getHasLockTagThreadPoiIdThreadLocal().get().remove(wmPoiId);
        }
    }
}
