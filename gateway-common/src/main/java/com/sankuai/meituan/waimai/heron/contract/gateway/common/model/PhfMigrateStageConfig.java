package com.sankuai.meituan.waimai.heron.contract.gateway.common.model;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractMigrateStageEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PhfMigrateStageConfig {

    private ContractMigrateStageEnum migrateStage;

    private Integer openTime;

    private Integer grayRate;

    public static void main(String[] args) {
        PhfMigrateStageConfig config = new PhfMigrateStageConfig();
        config.setMigrateStage(ContractMigrateStageEnum.NO_MIGRATE);
        config.setOpenTime(0);
        config.setGrayRate(100);
        List<PhfMigrateStageConfig> configList = new ArrayList<>();
        configList.add(config);
        PhfMigrateStageConfig config2 = new PhfMigrateStageConfig();
        config2.setMigrateStage(ContractMigrateStageEnum.BOTH_RUN);
        config2.setOpenTime(Integer.MAX_VALUE);
        config2.setGrayRate(0);
        configList.add(config2);

        PhfMigrateStageConfig config3 = new PhfMigrateStageConfig();
        config3.setMigrateStage(ContractMigrateStageEnum.MIGRATE_NEW);
        config3.setOpenTime(Integer.MAX_VALUE);
        config3.setGrayRate(0);
        configList.add(config3);
        System.out.println(JacksonUtil.writeAsJsonStr(configList));
    }
}
