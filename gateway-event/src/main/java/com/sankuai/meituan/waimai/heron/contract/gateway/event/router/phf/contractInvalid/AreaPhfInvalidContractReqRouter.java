package com.sankuai.meituan.waimai.heron.contract.gateway.event.router.phf.contractInvalid;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.event.AreaEventRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.LogisticsExtTagsHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.EventRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.phf.areaInvalid.AreaPhfInvalidRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractMigrateStageEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.SessionCategoryExtTagsBo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/24
 */
@Component
public class AreaPhfInvalidContractReqRouter extends AreaEventRequestRouter {

    @Resource
    private AreaPhfInvalidRequestSender areaPhfInvalidRequestSender;

    @Resource
    private LogisticsExtTagsHelper logisticsExtTagsHelper;

    @Override
    public RouteResult route(EventRouteParam routeParam) throws GatewaySystemException {
        SessionCategoryExtTagsBo extTagsBo = logisticsExtTagsHelper.getTagsBySessionCategory(routeParam.getWmPoiId(), SessionCategoryEnum.PHF);
        if (extTagsBo == null  || ContractMigrateStageEnum.BOTH_RUN.equals(extTagsBo.getMigrateStage())) {
            return new RouteResult();
        }
        //拆分阶段，失效拼好饭合同都会失效范围
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setRequestSender(areaPhfInvalidRequestSender);
        return routeResult;
    }
}
