package com.sankuai.meituan.waimai.heron.contract.gateway.event.dispatcher;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.dispatcher.BaseEventRequestDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.EventRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.StationChangeParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.router.stationChange.PerfStationChangeReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.router.stationChange.PlatStationChangeReqRouter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 站点变更请求分发
 * @author: chenyihao04
 * @create: 2024-04-02 11:49
 */
@Component
public class StationChangeReqDispatcher extends BaseEventRequestDispatcher<StationChangeParam> {

    @Resource
    private RouteHelper routeHelper;

    @Override
    protected List<Class<? extends RequestRouter>> registerRouterClass() {
        return ImmutableList.of(PlatStationChangeReqRouter.class, PerfStationChangeReqRouter.class);
    }

    @Override
    protected EventRouteParam buildRouteParam(StationChangeParam originParam) throws GatewayAdapterException {
        return routeHelper.buildEventRouteParam(originParam.getWmPoiId());
    }
}