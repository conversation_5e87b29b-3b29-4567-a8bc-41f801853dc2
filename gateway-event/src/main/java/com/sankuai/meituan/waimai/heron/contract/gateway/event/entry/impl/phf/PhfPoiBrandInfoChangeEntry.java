package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.phf;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBusinessException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.LogisticsExtTagsHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.EventHandleResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.AbstractPhfContractEventSessionEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.phf.PhfPoiOwnerTypeChangeParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.phf.brandChange.PlatPhfBrandChangeRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.AcquireFailActionEnum.WAIT;
import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock.WmPoiIdExtractTypeEnum.PARAM_FIELD;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Component
@Slf4j
public class PhfPoiBrandInfoChangeEntry extends AbstractPhfContractEventSessionEntry<PhfPoiOwnerTypeChangeParam, Void> {

    @Resource
    private PlatPhfBrandChangeRequestSender platPhfBrandChangeRequestSender;

    @Resource
    private LogisticsExtTagsHelper logisticsExtTagsHelper;

    @Override
    protected void fillSession(HeronContractGatewaySession session, PhfPoiOwnerTypeChangeParam param) {
        session.setOpId(-1L);
        session.setOpName(eventScene().getDescription());
    }

    @Override
    protected ContractCreateAndUpdateSceneEnum eventScene() {
        return ContractCreateAndUpdateSceneEnum.PHF_BRAND_INFO_CHANGE;
    }

    @Override
    @EntryAndProcessorLock(extractType = PARAM_FIELD, acquireFailAction = WAIT, sessionCategory = SessionCategoryEnum.PHF)
    public Void process(PhfPoiOwnerTypeChangeParam param) throws GatewaySystemException, GatewayBusinessException {
        HeronContractGatewaySession session = super.buildSession(param.getWmPoiId(), param);
        SendResult sendResult = GlobalEventSessionHandler.applyWithSession(session, new SendParam<>(param), platPhfBrandChangeRequestSender::sendRequest);
        log.info("PhfPoiBrandInfoChangeEntry 品牌信息变更 param={} sendResult={} ", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(sendResult));
        if (!sendResult.getSuccess()) {
            super.fillSessionStatusAndSave(session, ContractSessionStatusEnum.EVENT_FAIL);
            throw new GatewayBusinessException(sendResult.getCode(), "平台合同拼好饭品牌信息变更处理失败:" + sendResult.getMessage());
        }
        EventHandleResult eventHandleResult = JacksonUtil.readValue(sendResult.getSendAdditionalInfo(), EventHandleResult.class);
        boolean contractInvalid = Optional.ofNullable(eventHandleResult).map(EventHandleResult::getContractInvalid).orElse(false);
        boolean needStopFlow = Optional.ofNullable(eventHandleResult).map(EventHandleResult::getNeedStopFlow).orElse(false);
        if (contractInvalid || needStopFlow) {
            super.terminateFlow(param.getWmPoiId(), "品牌属性变更失效合同和流程数据", HeronContractOperator.builder()
                    .opName(ContractCreateAndUpdateSceneEnum.PHF_BRAND_INFO_CHANGE.getDescription())
                    .opId(-1L).build());
            super.fillSessionStatusAndSave(session, ContractSessionStatusEnum.EVENT_SUCCESS);
            if (contractInvalid) {
                logisticsExtTagsHelper.updateTagsByPhfContractInvalid(param.getWmPoiId());
            }
        }

        return null;
    }
}
