package com.sankuai.meituan.waimai.heron.contract.gateway.event.router.unbindLogisticsInfo;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.event.PerfEventRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PerfDomainEventRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.EventRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.unbindLogisticsInfo.PerfNewUnbindLogisticsInfoReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.unbindLogisticsInfo.PerfUnbindLogisticsInfoReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.unbindLogisticsInfo.PerfUnbindSupplierPoiReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.CleanSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-02 15:41
 */
@Component
@Slf4j
public class PerfUnbindLogisticsInfoReqRouter extends PerfEventRequestRouter {

    @Resource
    private PerfUnbindLogisticsInfoReqSender perfUnbindLogisticsInfoReqSender;

    @Resource
    private PerfNewUnbindLogisticsInfoReqSender perfNewUnbindLogisticsInfoReqSender;

    @Resource
    private PerfUnbindSupplierPoiReqSender perfUnbindSupplierPoiReqSender;

    @Override
    public RouteResult route(EventRouteParam routeParam) throws GatewaySystemException {
        return buildPerfEventRouteResult(routeParam, matchRequestSender());
    }
    private PerfDomainEventRequestSender matchRequestSender() {
        // 判断sender
        if (CleanSceneEnum.DEALERS_DELETE_SUBSTAY_AGGREMENT.equals(GlobalEventSessionHandler.getCleanSceneEnum())) {
            return perfUnbindSupplierPoiReqSender;
        } else {
            return SessionCategoryEnum.isCore(GlobalEventSessionHandler.getSessionCategory()) ? perfUnbindLogisticsInfoReqSender : perfNewUnbindLogisticsInfoReqSender;
        }
    }
}