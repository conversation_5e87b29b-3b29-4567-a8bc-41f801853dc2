package com.sankuai.meituan.waimai.heron.contract.gateway.event.router.qkSync;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.event.PlatEventRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.EventRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.qkSync.PlatSgTransferSyncToSplitReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-28 10:05
 */
@Component
public class PlatSgTransferSyncToSplitReqRouter extends PlatEventRequestRouter {

    @Resource
    private PlatSgTransferSyncToSplitReqSender platSgTransferSyncToSplitReqSender;

    @Override
    public RouteResult route(EventRouteParam routeParam) throws GatewaySystemException {
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setRequestSender(platSgTransferSyncToSplitReqSender);
        return routeResult;
    }
}