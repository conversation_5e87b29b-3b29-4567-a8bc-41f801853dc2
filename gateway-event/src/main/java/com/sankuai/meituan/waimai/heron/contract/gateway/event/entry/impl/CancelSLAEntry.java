package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBusinessException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.AbstractLogisticsEventBaseEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.cancelSLA.PlatCancelSLAReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.degradePaotui.PlatDegradePaotuiLogisticsReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.LogisticsOperatorDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.event.WmLogisticsDegradeZbRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.event.WmLogisticsEventProcessRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.AcquireFailActionEnum.WAIT;
import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock.WmPoiIdExtractTypeEnum.PARAM_FIELD;

/**
 * 关闭SLA
 */
@Service
@Slf4j
public class CancelSLAEntry extends AbstractLogisticsEventBaseEntry<WmLogisticsEventProcessRequestDTO, Boolean> {

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private PlatCancelSLAReqSender platCancelSLAReqSender;

    @Override
    protected ContractCreateAndUpdateSceneEnum eventScene() {
        return ContractCreateAndUpdateSceneEnum.CANCEL_SLA;
    }

    @Override
    protected void fillSession(HeronContractGatewaySession session, WmLogisticsEventProcessRequestDTO param) {
        session.setWmPoiId(param.getWmPoiId());
        LogisticsOperatorDTO operatorDTO = param.getEventRequest().getOperatorDTO();
        session.setOpId(Optional.ofNullable(operatorDTO).map(LogisticsOperatorDTO::getOpId).orElse(0L));
        session.setOpName(Optional.ofNullable(operatorDTO).map(LogisticsOperatorDTO::getOpName).orElse(""));
        session.setSessionCategory(SessionCategoryEnum.CORE);
    }

    @Override
    @EntryAndProcessorLock(extractType = PARAM_FIELD, acquireFailAction = WAIT, sessionCategory = SessionCategoryEnum.CORE)
    public Boolean process(WmLogisticsEventProcessRequestDTO param) throws GatewayBusinessException {
        HeronContractGatewaySession session = super.buildSession(param);
        heronContractGatewaySessionBasicService.insert(session);
        SendResult sendResult = GlobalEventSessionHandler.applyWithSession(session, new SendParam<>(param), platCancelSLAReqSender::sendRequest);
        if (sendResult.isFail()) {
            heronContractGatewaySessionBasicService.updateStatus(session.getSessionLeafId(), ContractSessionStatusEnum.EVENT_FAIL, Lists.newArrayList());
            throw new GatewayBusinessException(-1, "关闭SLA失败");
        } else {
            heronContractGatewaySessionBasicService.updateStatus(session.getSessionLeafId(), ContractSessionStatusEnum.EVENT_SUCCESS, Lists.newArrayList());
            return JacksonUtil.readValue(sendResult.getSendAdditionalInfo(), Boolean.class);
        }
    }
}
