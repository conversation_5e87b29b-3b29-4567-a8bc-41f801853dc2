package com.sankuai.meituan.waimai.heron.contract.gateway.event.router.droneRefreshFee;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.event.PerfEventRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.EventRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.droneRefreshFee.PerfDroneRefreshFeeReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-22 11:37
 */
@Component
public class PerfDroneRefreshFeeReqRouter extends PerfEventRequestRouter {

    @Resource
    private PerfDroneRefreshFeeReqSender perfDroneRefreshFeeReqSender;

    @Override
    public RouteResult route(EventRouteParam routeParam) throws GatewaySystemException {
        return buildPerfEventRouteResult(routeParam, perfDroneRefreshFeeReqSender);
    }
}