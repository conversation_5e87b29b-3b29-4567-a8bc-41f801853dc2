package com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.phf.coordinateChange;

import com.sankuai.meituan.banma.business.poi.sparea.client.core.ultimatesparea.request.SpAreaPoiOperationalConciseEffectRequest;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmSpAreaEventThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.AreaDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.PoiCoordinateMoveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.AreaIdentityHelper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter.toAreaOperator;

/**
 * <AUTHOR>
 * @date 2025/1/17
 */
@Component
public class AreaPhfCoordinateChangeReDrawRequestSender extends AreaDomainRequestSender<PoiCoordinateMoveParam, SpAreaPoiOperationalConciseEffectRequest> {

    @Resource
    private BmSpAreaEventThriftServiceAdapter bmSpAreaEventThriftServiceAdapter;
    @Override
    protected void sendToSpArea(SpAreaPoiOperationalConciseEffectRequest remoteParam) throws GatewayAdapterException {
        bmSpAreaEventThriftServiceAdapter.operationalConciseEffectPoiSpArea(remoteParam, AreaIdentityHelper.convertFromSessionCategory(GlobalEventSessionHandler.getSessionCategory()));
    }

    @Override
    protected SpAreaPoiOperationalConciseEffectRequest buildRemoteParam(SendParam<PoiCoordinateMoveParam> param) throws GatewayBaseException {
        PoiCoordinateMoveParam originParam = param.getOriginParam();
        SpAreaPoiOperationalConciseEffectRequest request = new SpAreaPoiOperationalConciseEffectRequest();
        request.setWmPoiId(originParam.getWmPoiId());
        request.setOperator(toAreaOperator(GlobalEventSessionHandler.getSession()));
//        request.setLatitude(originParam.getNewCoordinate().getLatitude());
//        request.setLongitude(originParam.getNewCoordinate().getLongitude());
//        request.setOrigLatitude(originParam.getOldCoordinate().getLatitude());
//        request.setOrigLongitude(originParam.getOldCoordinate().getLongitude());
//        request.setScene(SpAreaReDeawSceneEnum.COORDICATE_MOVING);
        return request;
    }



    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.COORDINATE_MODIFY;
    }
}
