package com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.expireDiscount;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractEventThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestErrorTypeEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.ExpireDiscountParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.event.WmPoiDiscountFeeCancelRequestDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-22 14:23
 */
@Component
public class PlatContractExpireDiscountReqSender extends PlatDomainRequestSender<ExpireDiscountParam, WmPoiDiscountFeeCancelRequestDTO> {

    @Resource
    private PlatContractEventThriftServiceAdapter platContractEventThriftServiceAdapter;

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.EXPIRE_DISCOUNT;
    }

    @Override
    protected WmPoiDiscountFeeCancelRequestDTO buildRemoteParam(SendParam<ExpireDiscountParam> param) {
        ExpireDiscountParam originParam = param.getOriginParam();
        WmPoiDiscountFeeCancelRequestDTO wmPoiDiscountFeeCancelRequestDTO = new WmPoiDiscountFeeCancelRequestDTO();
        wmPoiDiscountFeeCancelRequestDTO.setSessionId(GlobalEventSessionHandler.getSessionId());
        wmPoiDiscountFeeCancelRequestDTO.setWmPoiId(originParam.getWmPoiId());
        wmPoiDiscountFeeCancelRequestDTO.setAreaSplit(param.getRouteResult().getAreaSplit());
        wmPoiDiscountFeeCancelRequestDTO.setPerfSplit(param.getRouteResult().getPerfSplit());
        wmPoiDiscountFeeCancelRequestDTO.setPerfDR(param.getRouteResult().getPerfDR());
        wmPoiDiscountFeeCancelRequestDTO.setDealTime(GlobalEventSessionHandler.getEventDealTime());
        wmPoiDiscountFeeCancelRequestDTO.setOperatorDTO(OperatorConverter.toHeronOperator(originParam.getOperator()));
        wmPoiDiscountFeeCancelRequestDTO.setBatchAuditId(originParam.getBatchAuditId());
        return wmPoiDiscountFeeCancelRequestDTO;
    }

    @Override
    protected SendResult doSend(WmPoiDiscountFeeCancelRequestDTO remoteParam) throws GatewayAdapterException {
        Boolean success = platContractEventThriftServiceAdapter.cancelDiscountAndSpecial(remoteParam);
        return success ? SendResult.success() : SendResult.fail(-1, "平台取消过期优惠失败", RequestErrorTypeEnum.DOMAIN_BUSINESS_EXCEPTION);
    }
}