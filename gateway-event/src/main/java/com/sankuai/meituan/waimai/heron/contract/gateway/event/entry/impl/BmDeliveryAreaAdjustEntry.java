package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.LogisticsExtTagsHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.DispatchParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.EventDispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.monitor.CatLogMonitor;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.dispatcher.BmDeliveryAreaAdjustReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.AbstractLogisticsEventBaseEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.BmAreaAdjustParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.AllStructFlowTerminateFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.terminate.HeronContractFlowTerminateParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.SinglePoiResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.AcquireFailActionEnum.WAIT;
import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock.WmPoiIdExtractTypeEnum.PARAM_FIELD;

/**
 * @description: 配送区域调整
 * @author: chenyihao04
 * @create: 2023-04-26 14:34
 */
@Service
@Slf4j
public class BmDeliveryAreaAdjustEntry extends AbstractLogisticsEventBaseEntry<BmAreaAdjustParam, SinglePoiResult> {

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private BmDeliveryAreaAdjustReqDispatcher bmDeliveryAreaAdjustReqDispatcher;

    @Resource
    private AllStructFlowTerminateFlowStep allStructFlowTerminateFlowStep;

    @Resource
    private CatLogMonitor catLogMonitor;

    @Resource
    private LogisticsExtTagsHelper logisticsExtTagsHelper;

    @Override
    @EntryAndProcessorLock(extractType = PARAM_FIELD, acquireFailAction = WAIT)
    public SinglePoiResult process(BmAreaAdjustParam param) throws GatewaySystemException {
        catLogMonitor.catMonitor("bm_delivery_adjust", param.getWmPoiId());
        HeronContractGatewaySession session = super.buildSession(param);
        return GlobalEventSessionHandler.applyWithSession(session, null, p -> {
            EventDispatchResult dispatchResult = bmDeliveryAreaAdjustReqDispatcher.dispatch(new DispatchParam<>(param));
            boolean contractInvalid = CollectionUtils.isNotEmpty(dispatchResult.getResultList()) && dispatchResult.getResultList().stream().filter(Objects::nonNull).anyMatch(o -> BooleanUtils.isTrue(o.getContractInvalid()));
            if (contractInvalid) {
                logisticsExtTagsHelper.cleanTagsBySessionCategory(param.getWmPoiId(), session.getSessionCategory());
            }
            if (CollectionUtils.isNotEmpty(dispatchResult.getResultList()) && dispatchResult.getResultList().stream().anyMatch(o -> Objects.nonNull(o) && BooleanUtils.isTrue(o.getNeedStopFlow()))) {
                heronContractGatewaySessionBasicService.insert(session);
                HeronContractFlowTerminateParam flowTerminateParam = buildTerminateParam(param.getWmPoiId(), param.getOperator());
                FlowExecResult terminateResult = allStructFlowTerminateFlowStep.execute(flowTerminateParam);
                eventFinishCallback(param.getWmPoiId());
                return new SinglePoiResult(param.getWmPoiId(), handleEventResultWithTerminate(session, dispatchResult, terminateResult, flowTerminateParam));
            }
            return new SinglePoiResult(param.getWmPoiId(), GatewayResult.success());
        });

    }

    @Override
    protected ContractCreateAndUpdateSceneEnum eventScene() {
        return ContractCreateAndUpdateSceneEnum.DELIVERY_AREA_ADJUST;
    }

    @Override
    protected void fillSession(HeronContractGatewaySession session, BmAreaAdjustParam bmDeliveryAreaAdjustParam) {
        session.setWmPoiId(bmDeliveryAreaAdjustParam.getWmPoiId());
        session.setOpId(-1L);
        session.setOpName(ContractCreateAndUpdateSceneEnum.DELIVERY_AREA_ADJUST.name());
        session.setSessionCategory(bmDeliveryAreaAdjustParam.getSessionCategory());
    }
}