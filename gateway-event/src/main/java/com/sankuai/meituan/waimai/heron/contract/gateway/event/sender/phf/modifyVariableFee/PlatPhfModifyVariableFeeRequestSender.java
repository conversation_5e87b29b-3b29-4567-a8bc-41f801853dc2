package com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.phf.modifyVariableFee;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractEventThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.phf.PhfSinglePoiModifyVariableFeeParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfBatchModifyVariableFeeParam;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfSaveResponseDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfVariableContractSaveRequestDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/15
 */
@Component
public class PlatPhfModifyVariableFeeRequestSender extends PlatDomainRequestSender<PhfSinglePoiModifyVariableFeeParam, PhfVariableContractSaveRequestDTO> {

    @Resource
    private PlatContractEventThriftServiceAdapter platContractEventThriftServiceAdapter;

    @Override
    protected PhfVariableContractSaveRequestDTO buildRemoteParam(SendParam<PhfSinglePoiModifyVariableFeeParam> param) throws GatewayBaseException {

        PhfSinglePoiModifyVariableFeeParam originParam = param.getOriginParam();
        PhfBatchModifyVariableFeeParam variableFeeParam = originParam.getPhfBatchModifyVariableFeeParam();
        PhfVariableContractSaveRequestDTO remoteParam = new PhfVariableContractSaveRequestDTO();
        remoteParam.setWmPoiId(originParam.getWmPoiId());
        remoteParam.setDealTime(GlobalEventSessionHandler.getEventDealTime());
        remoteParam.setSessionId(GlobalEventSessionHandler.getSessionId());
        remoteParam.setItemDTOList(variableFeeParam.getPlatVariableFeeItemList());
        remoteParam.setOperatorDTO(OperatorConverter.toPlatOperator(variableFeeParam.getOperator()));
        return remoteParam;
    }

    @Override
    protected SendResult doSend(PhfVariableContractSaveRequestDTO remoteParam) throws GatewayAdapterException {
        PhfSaveResponseDTO responseDTO = platContractEventThriftServiceAdapter.savePhfVariableFeeContract(remoteParam);
        return convertResult(responseDTO.getResult());
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.PHF_VARIABLE_FEE_MODIFY;
    }
}
