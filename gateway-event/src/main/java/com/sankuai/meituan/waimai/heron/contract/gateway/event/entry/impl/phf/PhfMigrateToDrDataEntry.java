package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.phf;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.config.MccConfig;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.GatewayPreconditions;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.LogisticsExtTagsHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.LogisticsEventSessionEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfMigrateToDrParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.phf.PhfMigrateToDrDataProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractMigrateStageEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfContractDrMigrateCleanParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.SessionCategoryExtTagsBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Component
@Slf4j
public class PhfMigrateToDrDataEntry implements LogisticsEventSessionEntry<List<PhfContractDrMigrateCleanParam>, Void> {

    @Resource
    private PhfMigrateToDrDataProcessor phfMigrateToDrDataProcessor;

    @Resource
    private LogisticsExtTagsHelper logisticsExtTagsHelper;



    @Override
    public Void process(List<PhfContractDrMigrateCleanParam> param) throws GatewayBaseException {
        GatewayPreconditions.checkNotEmpty(param, "参数不能为空");
        List<Long> distinctWmPoiIdList = param.stream().map(PhfContractDrMigrateCleanParam::getWmPoiId).distinct().collect(Collectors.toList());
        if (distinctWmPoiIdList.size() > 1) {
            throw new GatewayArgumentException("迁移到配送合同只支持单个门店");
        }
        SessionCategoryExtTagsBo sessionCategoryExtTagsBo = logisticsExtTagsHelper.getTagsBySessionCategory(param.get(0).getWmPoiId(), SessionCategoryEnum.PHF);
        if (sessionCategoryExtTagsBo != null && ContractMigrateStageEnum.MIGRATE_NEW.equals(sessionCategoryExtTagsBo.getMigrateStage())) {
            return null;
        }
        PhfMigrateToDrParam phfMigrateToDrParam = PhfMigrateToDrParam.builder()
                .poiContractParamList(param)
                .wmPoiId(param.get(0).getWmPoiId())
                .dryRun(MccConfig.getPhfMigrateDryRunSwitch())
                .build();
        phfMigrateToDrDataProcessor.process(phfMigrateToDrParam);


        return null;
    }




}
