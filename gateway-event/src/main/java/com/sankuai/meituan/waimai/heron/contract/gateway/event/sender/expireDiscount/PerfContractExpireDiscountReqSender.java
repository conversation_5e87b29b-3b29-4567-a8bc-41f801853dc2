package com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.expireDiscount;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.CancelDiscountEventParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfSettleEventThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PerfDomainEventRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.ExpireDiscountParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-22 14:24
 */
@Component
public class PerfContractExpireDiscountReqSender extends PerfDomainEventRequestSender<ExpireDiscountParam, CancelDiscountEventParam> {

    @Resource
    private BmPerfSettleEventThriftServiceAdapter bmPerfSettleEventThriftServiceAdapter;

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.EXPIRE_DISCOUNT;
    }

    @Override
    protected CancelDiscountEventParam buildRemoteParam(SendParam<ExpireDiscountParam> param) {
        ExpireDiscountParam originParam = param.getOriginParam();
        CancelDiscountEventParam cancelDiscountEventParam = new CancelDiscountEventParam();
        cancelDiscountEventParam.setWmPoiId(originParam.getWmPoiId());
        cancelDiscountEventParam.setOperatorParam(OperatorConverter.toPerfOperator(originParam.getOperator()));
        cancelDiscountEventParam.setDealTime(GlobalEventSessionHandler.getEventDealTime());
        cancelDiscountEventParam.setRequestId(param.getRequestLeafId());
        cancelDiscountEventParam.setSessionId(GlobalEventSessionHandler.getSessionId());
        cancelDiscountEventParam.setBusinessIdentity(GlobalEventSessionHandler.getPerfBusinessIdentity());
        return cancelDiscountEventParam;
    }

    @Override
    protected BmContractPlatformProcessResp sendToPerf(CancelDiscountEventParam remoteParam) throws GatewayAdapterException {
        return bmPerfSettleEventThriftServiceAdapter.cancelDiscountEvent(remoteParam);
    }
}