package com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.phf.agentChange;

import com.meituan.dbus.common.StaticUtils;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractEventThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.phf.PhfPoiAgentInfoChangeParam;
import com.sankuai.meituan.waimai.logistics.contract.client.constants.OperateTypeEnum;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.EventResultDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfAgentChangeRequestDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Component
public class PlatPhfAgentChangeRequestSender extends PlatDomainRequestSender<PhfPoiAgentInfoChangeParam, PhfAgentChangeRequestDTO> {

    @Resource
    private PlatContractEventThriftServiceAdapter platContractEventThriftServiceAdapter;

    @Override
    protected PhfAgentChangeRequestDTO buildRemoteParam(SendParam<PhfPoiAgentInfoChangeParam> param) throws GatewayBaseException {
        PhfPoiAgentInfoChangeParam originParam = param.getOriginParam();
        PhfAgentChangeRequestDTO remoteParam = new PhfAgentChangeRequestDTO();
        remoteParam.setWmPoiId(originParam.getWmPoiId());
        remoteParam.setOperateType(convertOperateType(originParam.getEventType()));
        remoteParam.setDealTime(GlobalEventSessionHandler.getEventDealTime());
        remoteParam.setSessionId(GlobalEventSessionHandler.getSession().getSessionLeafId());
        return remoteParam;
    }

    @Override
    protected SendResult doSend(PhfAgentChangeRequestDTO remoteParam) throws GatewayAdapterException {
        EventResultDTO eventResultDTO = platContractEventThriftServiceAdapter.processPhfPoiAgentChange(remoteParam);
        return convertEventResult(eventResultDTO);
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.PHF_POI_AGENT_INFO_CHANGE;
    }

    private OperateTypeEnum convertOperateType(StaticUtils.EventType eventType) throws GatewayArgumentException {
        if (eventType == StaticUtils.EventType.insert) {
            return OperateTypeEnum.INSERT;
        } else if (eventType == StaticUtils.EventType.delete) {
            return OperateTypeEnum.DELETE;
        } else {
            throw new GatewayArgumentException("不支持的操作类型");
        }
    }
}
