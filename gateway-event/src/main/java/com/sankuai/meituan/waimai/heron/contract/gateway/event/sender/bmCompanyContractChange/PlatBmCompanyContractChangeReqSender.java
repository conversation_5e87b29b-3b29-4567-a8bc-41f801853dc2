package com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.bmCompanyContractChange;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractEventThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.BmCompanyContractChangeParam;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.BmContractChangeResultDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.EventResultDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SinglePoiBmCompanyContractChangeRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Optional;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-29 11:55
 */
@Slf4j
@Component
public class PlatBmCompanyContractChangeReqSender extends PlatDomainRequestSender<BmCompanyContractChangeParam, SinglePoiBmCompanyContractChangeRequestDTO> {

    @Resource
    private PlatContractEventThriftServiceAdapter platContractEventThriftServiceAdapter;

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.BM_COMPANY_CONTRACT_CHANGE;
    }

    @Override
    protected SinglePoiBmCompanyContractChangeRequestDTO buildRemoteParam(SendParam<BmCompanyContractChangeParam> param) {
        BmCompanyContractChangeParam originParam = param.getOriginParam();
        SinglePoiBmCompanyContractChangeRequestDTO remoteParam = new SinglePoiBmCompanyContractChangeRequestDTO();
        remoteParam.setCustomerId(originParam.getCustomerId());
        remoteParam.setBmCompanyContractId(originParam.getBmCompanyContractId());
        remoteParam.setWmPoiId(originParam.getWmPoiId());
        remoteParam.setDealTime(GlobalEventSessionHandler.getEventDealTime());
        remoteParam.setSessionId(GlobalEventSessionHandler.getSessionId());
        remoteParam.setAreaSplit(param.getRouteResult().getAreaSplit());
        remoteParam.setPerfSplit(param.getRouteResult().getPerfSplit());
        remoteParam.setPerfDR(param.getRouteResult().getPerfDR());
        return remoteParam;
    }

    @Override
    protected SendResult doSend(SinglePoiBmCompanyContractChangeRequestDTO remoteParam) throws GatewayAdapterException {
        EventResultDTO eventResultDTO = platContractEventThriftServiceAdapter.bmCompanyContractChangeEvent(remoteParam);
        return convertEventResult(eventResultDTO,
                Optional.ofNullable(eventResultDTO.getBmContractChangeResultDTO()).map(BmContractChangeResultDTO::getUnBindLogisticsCodeList).orElse(new ArrayList<>()),
                new ArrayList<>()
        );
    }

    @Override
    protected Boolean needRecordParam() {
        return true;
    }
}