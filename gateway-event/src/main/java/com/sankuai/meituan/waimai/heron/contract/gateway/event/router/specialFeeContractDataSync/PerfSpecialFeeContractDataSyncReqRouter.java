package com.sankuai.meituan.waimai.heron.contract.gateway.event.router.specialFeeContractDataSync;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.event.PerfEventRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.EventRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.specialFeeContractDataSync.PerfSpecialFeeContractDataSyncReqSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class PerfSpecialFeeContractDataSyncReqRouter extends PerfEventRequestRouter {

    @Resource
    private PerfSpecialFeeContractDataSyncReqSender perfSpecialFeeContractDataSyncReqSender;

    @Override
    public RouteResult route(EventRouteParam routeParam) throws GatewaySystemException {
        return buildPerfEventRouteResult(routeParam, perfSpecialFeeContractDataSyncReqSender);
    }
}