package com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.deliveryAreaAdjust;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.WmPoiLogisticsThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestErrorTypeEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.OldLogisticsDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.EventHandleResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.BmAreaAdjustParam;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.PoiAdjustResultConstruct;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.WmPoiAdjustLogisticsBizTypeResult;
import com.sankuai.meituan.waimai.poilogistics.thrift.vo.logistics.WmOldStructDeliveryAreaAdjustParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @description: 老服务-区域调整事件发送
 * @author: chenyihao04
 * @create: 2023-04-26 15:02
 */
@Component
@Slf4j
public class LogisticsOldBmDeliveryAreaAdjustReqSender extends OldLogisticsDomainRequestSender<BmAreaAdjustParam, WmOldStructDeliveryAreaAdjustParam> {

    @Resource
    private WmPoiLogisticsThriftServiceAdapter wmPoiLogisticsThriftServiceAdapter;


    @Override
    protected WmOldStructDeliveryAreaAdjustParam buildRemoteParam(SendParam<BmAreaAdjustParam> param) {
        BmAreaAdjustParam bmAreaAdjustParam = param.getOriginParam();
        WmOldStructDeliveryAreaAdjustParam wmOldStructDeliveryAreaAdjustParam = new WmOldStructDeliveryAreaAdjustParam();
        wmOldStructDeliveryAreaAdjustParam.setWmPoiIdList(Collections.singletonList(bmAreaAdjustParam.getWmPoiId()));
        wmOldStructDeliveryAreaAdjustParam.setBizType(bmAreaAdjustParam.getBizType());
        wmOldStructDeliveryAreaAdjustParam.setOpUid(bmAreaAdjustParam.getOperator().getOpId().intValue());
        wmOldStructDeliveryAreaAdjustParam.setOpUname(bmAreaAdjustParam.getOperator().getOpName());
        return wmOldStructDeliveryAreaAdjustParam;
    }

    @Override
    protected SendResult doSend(WmOldStructDeliveryAreaAdjustParam remoteParam) {
        try {
            List<WmPoiAdjustLogisticsBizTypeResult> wmPoiAdjustLogisticsBizTypeResults = wmPoiLogisticsThriftServiceAdapter.notifyPoiBmDeliveryAreaAdjust(remoteParam);
            return convertEventResult(wmPoiAdjustLogisticsBizTypeResults);
        } catch (GatewayAdapterException e) {
            return SendResult.failByAdapterException(e);
        }

    }

    private SendResult convertEventResult(List<WmPoiAdjustLogisticsBizTypeResult> wmPoiAdjustLogisticsBizTypeResults) {//实际上这个list只会有一只成员
        EventHandleResult eventHandleResult;
        if (CollectionUtils.isNotEmpty(wmPoiAdjustLogisticsBizTypeResults) && wmPoiAdjustLogisticsBizTypeResults.stream().anyMatch(o -> Objects.equals(o.getResultCode(), PoiAdjustResultConstruct.PROC_SUCC))) {
            if (wmPoiAdjustLogisticsBizTypeResults.stream().anyMatch(o -> BooleanUtils.isTrue(o.isNeedStopProcess()))) {
                eventHandleResult = EventHandleResult.buildResult(this.getSubDomain(), true, false, false);
            } else {
                eventHandleResult = EventHandleResult.buildResult(this.getSubDomain(), false, false, false);
            }
        } else {
            return SendResult.fail(-1, wmPoiAdjustLogisticsBizTypeResults.get(0).getResulstMsg(), RequestErrorTypeEnum.RETURN_ERROR_CODE);
        }
        SendResult sendResult = SendResult.success(JacksonUtil.writeAsJsonStr(eventHandleResult), EventHandleResult.class);
        return sendResult;
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.BM_AREA_ADJUST;
    }
}