package com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.unbindLogisticsInfo;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.UnbindSupplierPoiEventParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfSettleEventThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PerfDomainEventRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.UnbindLogisticsInfoParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 履约解绑供应商履约协议
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PerfUnbindSupplierPoiReqSender extends PerfDomainEventRequestSender<UnbindLogisticsInfoParam, UnbindSupplierPoiEventParam> {

    @Resource
    private BmPerfSettleEventThriftServiceAdapter bmPerfSettleEventThriftServiceAdapter;


    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.UNBIND_LOGISTICS_INFO;
    }

    @Override
    protected UnbindSupplierPoiEventParam buildRemoteParam(SendParam<UnbindLogisticsInfoParam> param) throws GatewayAdapterException {
        UnbindLogisticsInfoParam originParam = param.getOriginParam();
        UnbindSupplierPoiEventParam eventParam = new UnbindSupplierPoiEventParam();
        eventParam.setWmPoiId(originParam.getWmPoiId());
        eventParam.setDealTime(GlobalEventSessionHandler.getEventDealTime());
        eventParam.setSessionId(GlobalEventSessionHandler.getSessionId());
        eventParam.setOperatorParam(OperatorConverter.toPerfOperator(originParam.getOperator()));
        eventParam.setRequestId(param.getRequestLeafId());
        eventParam.setBusinessIdentity(GlobalEventSessionHandler.getPerfBusinessIdentity());
        return eventParam;
    }


    @Override
    protected BmContractPlatformProcessResp sendToPerf(UnbindSupplierPoiEventParam remoteParam) throws GatewayAdapterException {
        return bmPerfSettleEventThriftServiceAdapter.unbindSupplierPoiEvent(remoteParam);
    }
}