package com.sankuai.meituan.waimai.heron.contract.gateway.event.router.phf.agentChange;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.event.PerfEventRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.EventRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.phf.agentChange.PerfPhfAgentChangeRequestSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/20
 */
@Component
public class PerfPhfPoiAgentInfoChangeReqRouter extends PerfEventRequestRouter {

    @Resource
    private PerfPhfAgentChangeRequestSender perfPhfAgentChangeRequestSender;

    @Override
    public RouteResult route(EventRouteParam routeParam) throws GatewaySystemException {
        return buildPerfEventRouteResult(routeParam, perfPhfAgentChangeRequestSender);
    }
}
