package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry;

import com.google.common.base.Joiner;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.bo.SessionContextBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestErrorTypeEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SessionSceneGroupEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.mafka.MafkaMessageSendService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.TimeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.LogisticsExtTagsHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.leaf.LeafIdGenerator;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.EventRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.EventDispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.router.phf.contractInvalid.AreaPhfInvalidContractReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.SessionFlowTerminateFlowStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.SessionFlowTerminateFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.CancelSignOperateType;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractMigrateStageEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.PhfContractSessionNoticeMessageStruct;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.SessionCategoryExtTagsBo;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfContractTechAggreDataDTO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Optional;

import static com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.SessionContextAndExtTagHelper.fillSessionContextTagFromExt;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Slf4j
public abstract class AbstractPhfContractEventSessionEntry<T, O> implements LogisticsEventSessionEntry<T, O> {

    @Resource
    private LogisticsExtTagsHelper logisticsExtTagsHelper;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private SessionFlowTerminateFlowStep sessionFlowTerminateFlowStep;

    @Resource
    private MafkaMessageSendService mafkaMessageSendService;

    @Resource
    private PlatContractQueryThriftServiceAdapter platContractQueryThriftServiceAdapter;

    @Resource
    private AreaPhfInvalidContractReqRouter areaPhfInvalidContractReqRouter;

    protected HeronContractGatewaySession buildSession(Long wmPoiId, T param) {
        SessionCategoryExtTagsBo sessionCategoryExtTagsBo = logisticsExtTagsHelper.getTagsBySessionCategory(wmPoiId, SessionCategoryEnum.PHF);
        ContractMigrateStageEnum migrateStage = Optional.ofNullable(sessionCategoryExtTagsBo).map(SessionCategoryExtTagsBo::getMigrateStage).orElse(null);
        HeronContractGatewaySession session = new HeronContractGatewaySession();
        session.setWmPoiId(wmPoiId);
        session.setSessionScene(this.eventScene());
        session.setSessionLeafId(LeafIdGenerator.generateSessionId());
        session.setSceneGroup(SessionSceneGroupEnum.EVENT);
        session.setStatus(ContractSessionStatusEnum.EVENT_CREATE);
        SessionContextBo sessionContextBo = SessionContextBo.newEventSession();
        fillSessionContextTagFromExt(sessionCategoryExtTagsBo, sessionContextBo);
        sessionContextBo.setTraceId(Tracer.id());
        sessionContextBo.setEventDealTime(TimeUtil.unixTime());
        sessionContextBo.setMigrateStage(migrateStage);
        session.setContext(sessionContextBo);
        session.setSessionCategory(SessionCategoryEnum.PHF);
        session.setBatchRelationId(LeafIdGenerator.generateBatchRelationId());
        this.fillSession(session, param);
        return session;
    }

    protected HeronContractGatewaySession buildAndSaveSession(Long wmPoiId, T param) {
        HeronContractGatewaySession session = this.buildSession(wmPoiId, param);
        heronContractGatewaySessionBasicService.insert(session);
        return session;
    }

    protected void fillSessionStatusAndSave(HeronContractGatewaySession session, ContractSessionStatusEnum status) {
        session.setStatus(status);
        heronContractGatewaySessionBasicService.insert(session);
    }

    protected abstract void fillSession(HeronContractGatewaySession session, T param);


    protected abstract ContractCreateAndUpdateSceneEnum eventScene();


    protected void updateEventResult(HeronContractGatewaySession session, boolean success, String failMessage) {
        if (success) {
            heronContractGatewaySessionBasicService.updateStatus(session.getSessionLeafId(), ContractSessionStatusEnum.EVENT_SUCCESS, null);
        } else {
            heronContractGatewaySessionBasicService.updateStatusAndFailRemark(session.getSessionLeafId(), ContractSessionStatusEnum.EVENT_FAIL, failMessage);
        }
    }


    protected void terminateFlow(Long wmPoiId, String failReason, HeronContractOperator operator) {
        HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(wmPoiId, SessionCategoryEnum.PHF);
        log.info("terminateFlow wmPoiId={} 流程中session={}", wmPoiId, session);
        if (session == null) {
            return;
        }
        SessionFlowTerminateFlowStepParam param = SessionFlowTerminateFlowStepParam.builder()
                .cancelSignOperateType(CancelSignOperateType.NEED_CANCEL_SIGN)
                .sessionId(session.getSessionLeafId())
                .operator(operator)
                .failReason(failReason)
                .sessionCategory(SessionCategoryEnum.PHF)
                .wmPoiId(wmPoiId)
                .build();
        GlobalFlowSessionHandler.applyWithSession(session, param, sessionFlowTerminateFlowStep::execute);

    }

    protected void sendSessionNotice(HeronContractGatewaySession session) {
        for (int i = 0; i < 3; i++) {
            try {
                PhfContractTechAggreDataDTO phfContractTechAggreDataDTO = platContractQueryThriftServiceAdapter.queryPhfEffectiveContract(session.getWmPoiId());
                if (phfContractTechAggreDataDTO == null || phfContractTechAggreDataDTO.getBaseInfoDTO() == null) {
                    log.info("sendSessionNotice 查询不到合同信息，不发送消息 wmPoiId={}", session.getWmPoiId());
                    return;
                }
                PhfContractSessionNoticeMessageStruct messageStruct = PhfContractSessionNoticeMessageStruct.builder()
                        .wmPoiId(session.getWmPoiId())
                        .phfContractId(phfContractTechAggreDataDTO.getBaseInfoDTO().getPhfContractId())
                        .sessionId(session.getSessionLeafId())
                        .sendTime(TimeUtil.unixTime())
                        .build();
                mafkaMessageSendService.sendPhfSessionNotice(messageStruct);
                return;
            } catch (Exception e) {
                log.error("sendSessionNotice error session={} i={}", JacksonUtil.writeAsJsonStr(session), i, e);
            }
        }
    }


    protected SendResult areaInvalid(Long wmPoiId) {
        EventRouteParam routeParam = new EventRouteParam();
        routeParam.setWmPoiId(wmPoiId);
        try {
            RouteResult routeResult = areaPhfInvalidContractReqRouter.route(routeParam);
            if (routeResult != null && routeResult.getRequestSender() != null) {
                return routeResult.getRequestSender().sendRequest(new SendParam(wmPoiId));
            }
            return SendResult.success();
        } catch (Exception e) {
            log.error("areaInvalid 处理出现异常 wmPoiId={}", wmPoiId, e);
            return SendResult.fail(-1, "拼好饭范围失效出现异常", RequestErrorTypeEnum.GATEWAY_INNER_EXCEPTION);
        }

    }


    protected String buildDispatchErrorMessage(EventDispatchResult eventDispatchResult) {
        if (eventDispatchResult == null) {
            return "失败原因为空";
        }
        return Joiner.on(" , ").skipNulls().join("子领域：" + Optional.ofNullable(eventDispatchResult.getSendFailDomain()).map(SubDomainEnum::getDesc).orElse("未知"), eventDispatchResult.getMessage());
    }



}
