package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.DispatchParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.EventDispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.monitor.CatLogMonitor;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.dispatcher.SpecialFeeContractDataSyncDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.AbstractLogisticsEventBaseEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.CopyContractParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.SinglePoiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.AcquireFailActionEnum.WAIT;
import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock.WmPoiIdExtractTypeEnum.PARAM_FIELD;

/**
 * 特批费率合同的数据同步事件
 * 提供给tsp的一体化费率聚合数据，用于新增specialEndTime字段
 */
@Slf4j
@Service
public class SpecialFeeContractDataSyncEntry extends AbstractLogisticsEventBaseEntry<CopyContractParam, SinglePoiResult> {
    @Resource
    private CatLogMonitor catLogMonitor;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private SpecialFeeContractDataSyncDispatcher specialFeeContractDataSyncDispatcher;

    @Override
    protected ContractCreateAndUpdateSceneEnum eventScene() {
        return ContractCreateAndUpdateSceneEnum.SPECIAL_FEE_CONTRACT_DATA_SYNC;
    }

    @Override
    protected void fillSession(HeronContractGatewaySession session, CopyContractParam param) {
        session.setWmPoiId(param.getWmPoiId());
        session.setOpId(param.getOperator().getOpId());
        session.setOpName(ContractCreateAndUpdateSceneEnum.SPECIAL_FEE_CONTRACT_DATA_SYNC.name());
        session.setSessionCategory(SessionCategoryEnum.CORE);
    }

    @Override
    @EntryAndProcessorLock(extractType = PARAM_FIELD, acquireFailAction = WAIT)
    public SinglePoiResult process(CopyContractParam param) throws GatewaySystemException {
        catLogMonitor.catMonitor("special_fee_contract_data_sync", param.getWmPoiId());
        HeronContractGatewaySession session = super.buildSession(param);
        // CopyContractParam copyContractParam = buildParam(param);
        heronContractGatewaySessionBasicService.insert(session);
        return GlobalEventSessionHandler.applyWithSession(session, null, p -> {
            EventDispatchResult dispatchResult = specialFeeContractDataSyncDispatcher.dispatch(new DispatchParam<>(param));
            if (dispatchResult.getSuccess()) {
                heronContractGatewaySessionBasicService.updateStatus(session.getSessionLeafId(), ContractSessionStatusEnum.EVENT_SUCCESS, new ArrayList<>());
                eventFinishCallback(param.getWmPoiId());
                return new SinglePoiResult(param.getWmPoiId(), GatewayResult.success());
            } else {
                heronContractGatewaySessionBasicService.updateStatus(session.getSessionLeafId(), ContractSessionStatusEnum.EVENT_FAIL, new ArrayList<>());
                return new SinglePoiResult(param.getWmPoiId(), GatewayResult.fail(-1, dispatchResult.getMessage()));
            }
        });
    }
}
