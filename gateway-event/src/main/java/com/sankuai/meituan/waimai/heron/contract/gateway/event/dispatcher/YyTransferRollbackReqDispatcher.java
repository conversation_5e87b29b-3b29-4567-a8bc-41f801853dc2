package com.sankuai.meituan.waimai.heron.contract.gateway.event.dispatcher;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.dispatcher.BaseEventRequestDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.EventRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.SgOrYyTransferSyncSelectiveRollbackParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.router.yyContractSync.PerfYyTransferSyncRollbackReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.router.yyContractSync.PlatYyTransferSyncRollbackReqRouter;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-28 19:06
 */
@Component
public class YyTransferRollbackReqDispatcher extends BaseEventRequestDispatcher<SgOrYyTransferSyncSelectiveRollbackParam> {

    @Resource
    private RouteHelper routeHelper;

    @Override
    protected List<Class<? extends RequestRouter>> registerRouterClass() {
        return ImmutableList.of(PlatYyTransferSyncRollbackReqRouter.class, PerfYyTransferSyncRollbackReqRouter.class);
    }

    @Override
    protected EventRouteParam buildRouteParam(SgOrYyTransferSyncSelectiveRollbackParam originParam) throws GatewayAdapterException {
        EventRouteParam eventRouteParam = new EventRouteParam();
        eventRouteParam.setWmPoiId(originParam.getWmPoiId());
        eventRouteParam.setValidHasPerf(BooleanUtils.isNotFalse(originParam.getHasPerf()));
        return eventRouteParam;
    }
}