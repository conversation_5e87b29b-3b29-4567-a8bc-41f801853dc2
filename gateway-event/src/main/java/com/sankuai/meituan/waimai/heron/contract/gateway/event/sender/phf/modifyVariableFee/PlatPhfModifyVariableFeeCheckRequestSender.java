package com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.phf.modifyVariableFee;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractEventThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.phf.PhfSinglePoiModifyVariableFeeParam;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.ResultDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfVariableContractSaveRequestDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/15
 */
@Component
public class PlatPhfModifyVariableFeeCheckRequestSender extends PlatDomainRequestSender<PhfSinglePoiModifyVariableFeeParam, PhfVariableContractSaveRequestDTO> {

    @Resource
    private PlatContractEventThriftServiceAdapter platContractEventThriftServiceAdapter;

    @Override
    protected PhfVariableContractSaveRequestDTO buildRemoteParam(SendParam<PhfSinglePoiModifyVariableFeeParam> param) throws GatewayBaseException {
        PhfSinglePoiModifyVariableFeeParam originParam = param.getOriginParam();
        PhfVariableContractSaveRequestDTO remoteParam = new PhfVariableContractSaveRequestDTO();
        remoteParam.setWmPoiId(originParam.getWmPoiId());
        remoteParam.setDealTime(GlobalEventSessionHandler.getEventDealTime());
        remoteParam.setSessionId(GlobalEventSessionHandler.getSessionId());
        remoteParam.setItemDTOList(originParam.getPhfBatchModifyVariableFeeParam().getPlatVariableFeeItemList());
        return remoteParam;
    }

    @Override
    protected SendResult doSend(PhfVariableContractSaveRequestDTO remoteParam) throws GatewayAdapterException {
        ResultDTO resultDTO = platContractEventThriftServiceAdapter.preCheckSavePhfVariableFeeContract(remoteParam);
        return convertResult(resultDTO);
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.PHF_VARIABLE_FEE_MODIFY_CHECK;
    }
}
