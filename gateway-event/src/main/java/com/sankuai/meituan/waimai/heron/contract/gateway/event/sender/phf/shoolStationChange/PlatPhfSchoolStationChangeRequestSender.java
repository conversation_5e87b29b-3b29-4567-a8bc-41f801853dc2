package com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.phf.shoolStationChange;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractEventThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.phf.PhfSchoolStationChangeParam;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.EventResultDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfSchoolStationChangeRequestDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Component
public class PlatPhfSchoolStationChangeRequestSender extends PlatDomainRequestSender<PhfSchoolStationChangeParam, PhfSchoolStationChangeRequestDTO> {

    @Resource
    private PlatContractEventThriftServiceAdapter platContractEventThriftServiceAdapter;

    @Override
    protected PhfSchoolStationChangeRequestDTO buildRemoteParam(SendParam<PhfSchoolStationChangeParam> param) throws GatewayBaseException {
        PhfSchoolStationChangeParam originParam = param.getOriginParam();
        PhfSchoolStationChangeRequestDTO remoteParam = new PhfSchoolStationChangeRequestDTO();
        remoteParam.setWmPoiId(originParam.getWmPoiId());
        remoteParam.setStationId(originParam.getStationId());
        remoteParam.setSessionId(GlobalEventSessionHandler.getSessionId());
        remoteParam.setStationChangeType(originParam.getEventType());
        return remoteParam;
    }

    @Override
    protected SendResult doSend(PhfSchoolStationChangeRequestDTO remoteParam) throws GatewayAdapterException {
        EventResultDTO eventResultDTO = platContractEventThriftServiceAdapter.processSchoolStationChange(remoteParam);
        return convertEventResult(eventResultDTO);
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.PHF_SCHOOL_STATION_CHANGE;
    }
}
