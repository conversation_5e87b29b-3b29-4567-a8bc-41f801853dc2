package com.sankuai.meituan.waimai.heron.contract.gateway.event.router.yySyncFee;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.event.PerfEventRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.EventRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.yySyncFee.PerfRefreshTechFeeEventReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-29 11:55
 */
@Component
public class PerfRefreshTechFeeEventReqRouter extends PerfEventRequestRouter {

    @Resource
    private PerfRefreshTechFeeEventReqSender perfRefreshTechFeeEventReqSender;

    @Override
    public RouteResult route(EventRouteParam routeParam) throws GatewaySystemException {
        return buildPerfEventRouteResult(routeParam, perfRefreshTechFeeEventReqSender);
    }
}