package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.LogisticsExtTagsHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.DispatchParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.EventDispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.monitor.CatLogMonitor;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.dispatcher.StationChangeReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.AbstractLogisticsEventBaseEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.StationChangeParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.AllStructFlowTerminateFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.terminate.HeronContractFlowTerminateParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.SessionCategoryExtTagsBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.AcquireFailActionEnum.WAIT;
import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock.WmPoiIdExtractTypeEnum.PARAM_FIELD;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2024-04-01 16:14
 */
@Service
@Slf4j
public class StationChangeEntry extends AbstractLogisticsEventBaseEntry<StationChangeParam, GatewayResult> {

    @Resource
    private CatLogMonitor catLogMonitor;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private StationChangeReqDispatcher stationChangeReqDispatcher;

    @Resource
    private AllStructFlowTerminateFlowStep allStructFlowTerminateFlowStep;

    @Resource
    private LogisticsExtTagsHelper logisticsExtTagsHelper;

    @Override
    protected ContractCreateAndUpdateSceneEnum eventScene() {
        return ContractCreateAndUpdateSceneEnum.POI_STATION_CHANGE;
    }

    @Override
    @EntryAndProcessorLock(extractType = PARAM_FIELD, acquireFailAction = WAIT)
    public GatewayResult process(StationChangeParam param) throws GatewaySystemException {
        catLogMonitor.catMonitor(param.getEventType(), param.getWmPoiId());
        HeronContractGatewaySession session = super.buildSession(param);
        heronContractGatewaySessionBasicService.insert(session);
        GatewayResult gatewayResult = GlobalEventSessionHandler.applyWithSession(session, null, p -> {
            EventDispatchResult dispatchResult = stationChangeReqDispatcher.dispatch(new DispatchParam<>(param));
            boolean platContractChange = dispatchResult.getResultList().stream().filter(Objects::nonNull).anyMatch(o -> Objects.equals(o.getSubDomainEnum(), SubDomainEnum.PLAT) && BooleanUtils.isTrue(o.getContractChange()));
            boolean perfContractInvalid = dispatchResult.getResultList().stream().filter(Objects::nonNull).anyMatch(o -> Objects.equals(o.getSubDomainEnum(), SubDomainEnum.PERF) && BooleanUtils.isTrue(o.getContractInvalid()));
            if (platContractChange && perfContractInvalid) {
                SessionCategoryExtTagsBo tagsBySessionCategory = logisticsExtTagsHelper.getTagsBySessionCategory(param.getWmPoiId(), param.getSessionCategory());
                tagsBySessionCategory.setHasPerf(false);
                logisticsExtTagsHelper.updateTagsBySessionCategory(param.getWmPoiId(), tagsBySessionCategory, param.getSessionCategory());
            }
            if (CollectionUtils.isNotEmpty(dispatchResult.getResultList()) && dispatchResult.getResultList().stream().anyMatch(o -> Objects.nonNull(o) && BooleanUtils.isTrue(o.getNeedStopFlow()))) {
                HeronContractFlowTerminateParam flowTerminateParam = buildTerminateParam(param.getWmPoiId(), param.getOperator());
                FlowExecResult terminateResult = allStructFlowTerminateFlowStep.execute(flowTerminateParam);
                eventFinishCallback(param.getWmPoiId());
                return handleEventResultWithTerminate(session, dispatchResult, terminateResult, flowTerminateParam);
            }
            return handleEventResult(session, dispatchResult);
        });
        eventFinishCallback(param.getWmPoiId());
        return gatewayResult;
    }

    @Override
    protected void fillSession(HeronContractGatewaySession session, StationChangeParam param) {
        session.setWmPoiId(param.getWmPoiId());
        session.setOpId(param.getOperator().getOpId());
        session.setOpName(param.getOperator().getOpName());
        session.getContext().setEventDealTime(param.getDealTime());
        session.setSessionCategory(param.getSessionCategory());
    }
}