package com.sankuai.meituan.waimai.heron.contract.gateway.event.dispatcher;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.dispatcher.BaseEventRequestDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.EventRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.SgTransferSyncToSplitParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.router.qkSync.PlatSgTransferSyncToSplitReqRouter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-25 11:04
 */
@Component
public class SgTransferSyncToSplitReqDispatcher extends BaseEventRequestDispatcher<SgTransferSyncToSplitParam> {

    @Override
    protected List<Class<? extends RequestRouter>> registerRouterClass() {
        return ImmutableList.of(PlatSgTransferSyncToSplitReqRouter.class);
    }

    @Override
    protected EventRouteParam buildRouteParam(SgTransferSyncToSplitParam originParam) throws GatewayAdapterException {
        EventRouteParam eventRouteParam = new EventRouteParam();
        eventRouteParam.setWmPoiId(originParam.getWmPoiId());
        return eventRouteParam;
    }
}