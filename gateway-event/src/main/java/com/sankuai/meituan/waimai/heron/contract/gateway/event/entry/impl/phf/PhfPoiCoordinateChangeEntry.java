package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.phf;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.EventHandleResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.AbstractPhfContractEventSessionEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.PoiCoordinateMoveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.phf.coordinateChange.AreaPhfCoordinateChangeReDrawRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.phf.coordinateChange.PlatPhfCoordinateChangeRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.CoordinateMoveResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.AcquireFailActionEnum.WAIT;
import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock.WmPoiIdExtractTypeEnum.PARAM_FIELD;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Component
@Slf4j
public class PhfPoiCoordinateChangeEntry extends AbstractPhfContractEventSessionEntry<PoiCoordinateMoveParam, Void> {

    @Resource
    private PlatPhfCoordinateChangeRequestSender platPhfCoordinateChangeRequestSender;

    @Resource
    private AreaPhfCoordinateChangeReDrawRequestSender areaPhfCoordinateChangeReDrawRequestSender;


    @Override
    protected void fillSession(HeronContractGatewaySession session, PoiCoordinateMoveParam param) {
        session.setOpId(-1L);
        session.setOpName(eventScene().getDescription());
    }

    @Override
    protected ContractCreateAndUpdateSceneEnum eventScene() {
        return ContractCreateAndUpdateSceneEnum.PHF_POI_COORDINATE_MOVE;
    }

    @Override
    @EntryAndProcessorLock(extractType = PARAM_FIELD, acquireFailAction = WAIT)
    public Void process(PoiCoordinateMoveParam param) throws GatewayBaseException {
        HeronContractGatewaySession session = super.buildSession(param.getWmPoiId(), param);
        GatewayResult gatewayResult = GlobalEventSessionHandler.applyWithSession(session, null, p -> {
            SendResult sendResult = platPhfCoordinateChangeRequestSender.sendRequest(new SendParam<>(param));
            if (!sendResult.getSuccess()) {
                super.fillSessionStatusAndSave(session, ContractSessionStatusEnum.EVENT_FAIL);
                log.warn("PhfPoiCoordinateChangeEntry 坐标移动处理失败 param={}, sendResult={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(sendResult));
                return GatewayResult.fail(sendResult.getCode(), "平台合同拼好饭坐标移动处理失败: " + sendResult.getMessage());
            }
            //重画范围
            EventHandleResult<CoordinateMoveResultDTO> eventHandleResult = JacksonUtil.readValue(sendResult.getSendAdditionalInfo(), new TypeReference<EventHandleResult<CoordinateMoveResultDTO>>() {});
            CoordinateMoveResultDTO coordinateMoveResultDTO = eventHandleResult.getDomainEventData();
            boolean reDrawArea = coordinateMoveResultDTO != null && CollectionUtils.isNotEmpty(coordinateMoveResultDTO.getRedrawLogisticsCodeList());
            if (BooleanUtils.isTrue(reDrawArea)) {
                super.fillSessionStatusAndSave(session, ContractSessionStatusEnum.EVENT_SUCCESS);
                SendResult drawAreaResult = areaPhfCoordinateChangeReDrawRequestSender.sendRequest(new SendParam<>(param));
                if (!drawAreaResult.getSuccess()) {
                    log.warn("PhfPoiCoordinateChangeEntry 坐标移动重画范围失败 param={}, drawAreaResult={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(drawAreaResult));
                    return GatewayResult.fail(drawAreaResult.getCode(), drawAreaResult.getMessage());
                }
            }
            return GatewayResult.success();
        });
        if (!gatewayResult.getSuccess()) {
            throw new GatewayBaseException(gatewayResult.getCode(), gatewayResult.getMsg());
        }
        return null;
    }
}
