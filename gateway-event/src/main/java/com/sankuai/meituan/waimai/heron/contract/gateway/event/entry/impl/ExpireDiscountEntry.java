package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.DispatchParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.EventDispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.monitor.CatLogMonitor;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalEventSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.dispatcher.ExpireDiscountReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.AbstractLogisticsEventBaseEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.ExpireDiscountParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event.CancelDiscountAndSpecialParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.SinglePoiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.AcquireFailActionEnum.WAIT;
import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock.WmPoiIdExtractTypeEnum.PARAM_FIELD;

/**
 * @description: 取消过期优惠
 * @author: chenyihao04
 * @create: 2023-08-22 10:35
 */
@Service
@Slf4j
public class ExpireDiscountEntry extends AbstractLogisticsEventBaseEntry<CancelDiscountAndSpecialParam, SinglePoiResult> {

    @Resource
    private CatLogMonitor catLogMonitor;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private ExpireDiscountReqDispatcher expireDiscountReqDispatcher;

    @Override
    protected ContractCreateAndUpdateSceneEnum eventScene() {
        return ContractCreateAndUpdateSceneEnum.DISCOUNT_EXPIRE;
    }

    @Override
    protected void fillSession(HeronContractGatewaySession session, CancelDiscountAndSpecialParam param) {
        session.setWmPoiId(param.getWmPoiId());
        session.setOpId(param.getOperator().getOpId());
        session.setOpName(ContractCreateAndUpdateSceneEnum.DISCOUNT_EXPIRE.name());
        session.setSessionCategory(SessionCategoryEnum.CORE);
    }

    @Override
    @EntryAndProcessorLock(extractType = PARAM_FIELD, acquireFailAction = WAIT)
    public SinglePoiResult process(CancelDiscountAndSpecialParam param) throws GatewaySystemException {
        catLogMonitor.catMonitor("discount_expire", param.getWmPoiId());
        HeronContractGatewaySession session = super.buildSession(param);
        ExpireDiscountParam expireDiscountParam = buildParam(param);
        heronContractGatewaySessionBasicService.insert(session);
        return GlobalEventSessionHandler.applyWithSession(session, null, p -> {
            EventDispatchResult dispatchResult = expireDiscountReqDispatcher.dispatch(new DispatchParam<>(expireDiscountParam));
            if (dispatchResult.getSuccess()) {
                heronContractGatewaySessionBasicService.updateStatus(session.getSessionLeafId(), ContractSessionStatusEnum.EVENT_SUCCESS, new ArrayList<>());
                eventFinishCallback(param.getWmPoiId());
                return new SinglePoiResult(param.getWmPoiId(), GatewayResult.success());
            } else {
                heronContractGatewaySessionBasicService.updateStatus(session.getSessionLeafId(), ContractSessionStatusEnum.EVENT_FAIL, new ArrayList<>());
                return new SinglePoiResult(param.getWmPoiId(), GatewayResult.fail(-1, dispatchResult.getMessage()));
            }
        });
    }

    private ExpireDiscountParam buildParam(CancelDiscountAndSpecialParam param) {
        ExpireDiscountParam expireDiscountParam = new ExpireDiscountParam();
        expireDiscountParam.setOperator(param.getOperator());
        expireDiscountParam.setWmPoiId(param.getWmPoiId());
        expireDiscountParam.setBatchAuditId(param.getBatchAuditId());
        return expireDiscountParam;
    }
}