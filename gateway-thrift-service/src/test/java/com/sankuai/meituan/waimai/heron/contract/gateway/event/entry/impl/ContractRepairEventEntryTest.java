package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.enums.StationChangeEventTypeEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.leaf.LeafIdGenerator;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.DispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.EventDispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.EventHandleResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.monitor.CatLogMonitor;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.dispatcher.ContractRepairReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.StationChangeParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.sender.copyContract.PlatContractCopyContractReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event.ContractRepairEventParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.SessionCategoryExtTagsBo;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class ContractRepairEventEntryTest {

    @InjectMocks
    private ContractRepairEventEntry contractRepairEventEntry;

    @Mock
    private HeronContractGatewaySession session;

    @Mock
    private ContractRepairEventParam param;

    @Mock
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Mock
    private ContractRepairReqDispatcher contractRepairReqDispatcher;

    @Mock
    private PlatContractFlowThriftServiceAdapter platContractFlowThriftServiceAdapter;

    @Mock
    private PlatContractCopyContractReqSender platContractCopyContractReqSender;

    @Mock
    private CatLogMonitor catLogMonitor;

    MockedStatic<LeafIdGenerator> mockedStatic;

    @Before
    public void setUp() {
        mockedStatic = Mockito.mockStatic(LeafIdGenerator.class);
        mockedStatic.when(LeafIdGenerator::generateSessionId).thenReturn(555L);
    }

    @After
    public void tearDown() {
        mockedStatic.close();
    }


    /**
     * 测试 eventScene 方法
     */
    @Test
    public void testEventScene() throws Throwable {
        // arrange
        ContractRepairEventEntry contractRepairEventEntry = new ContractRepairEventEntry();
        // act
        ContractCreateAndUpdateSceneEnum result = contractRepairEventEntry.eventScene();
        // assert
        Assert.assertEquals(ContractCreateAndUpdateSceneEnum.CONTRACT_REPAIR, result);
    }

    /**
     * Test the normal scenario where all parameters are valid.
     */
    @Test
    public void testFillSessionNormalCase() throws Throwable {
        // arrange
        when(param.getWmPoiId()).thenReturn(12345L);
        when(param.getSessionCategory()).thenReturn(SessionCategoryEnum.CORE);
        // act
        contractRepairEventEntry.fillSession(session, param);
        // assert
        verify(session).setWmPoiId(12345L);
        verify(session).setOpId(-1L);
        verify(session).setOpName(ContractCreateAndUpdateSceneEnum.CONTRACT_REPAIR.name());
        verify(session).setSessionCategory(SessionCategoryEnum.CORE);
    }

    /**
     * Test the boundary scenario where sessionCategory is null.
     */
    @Test
    public void testFillSessionSessionCategoryNull() throws Throwable {
        // arrange
        when(param.getWmPoiId()).thenReturn(12345L);
        when(param.getSessionCategory()).thenReturn(null);
        // act
        contractRepairEventEntry.fillSession(session, param);
        // assert
        verify(session).setWmPoiId(12345L);
        verify(session).setOpId(-1L);
        verify(session).setOpName(ContractCreateAndUpdateSceneEnum.CONTRACT_REPAIR.name());
        verify(session).setSessionCategory(SessionCategoryEnum.CORE);
    }

    /**
     * Test the exception scenario where session is null.
     */
    @Test(expected = NullPointerException.class)
    public void testFillSessionSessionNull() throws Throwable {
        // arrange
        HeronContractGatewaySession nullSession = null;
        when(param.getWmPoiId()).thenReturn(12345L);
        // act
        contractRepairEventEntry.fillSession(nullSession, param);
        // assert
        // Expecting NullPointerException
    }

    /**
     * Test the exception scenario where param is null.
     */
    @Test(expected = NullPointerException.class)
    public void testFillSessionParamNull() throws Throwable {
        // arrange
        ContractRepairEventParam nullParam = null;
        // act
        contractRepairEventEntry.fillSession(session, nullParam);
        // assert
        // Expecting NullPointerException
    }


    @Test
    public void testProcessNormal() throws Throwable {
        // Arrange
        ContractRepairEventParam param = new ContractRepairEventParam();
        // Set a valid wmPoiId to avoid NullPointerException
        param.setWmPoiId(12345L);
        // Mock the CatLogMonitor to avoid NullPointerException
        // Mock the LeafIdGenerator to throw a RuntimeException
        EventDispatchResult success = new EventDispatchResult();
        success.setSuccess(true);
        when(contractRepairReqDispatcher.dispatch(any())).thenReturn(success);
        // Act
        GatewayResult process = contractRepairEventEntry.process(param);
        assertTrue(process.getSuccess());
    }
}