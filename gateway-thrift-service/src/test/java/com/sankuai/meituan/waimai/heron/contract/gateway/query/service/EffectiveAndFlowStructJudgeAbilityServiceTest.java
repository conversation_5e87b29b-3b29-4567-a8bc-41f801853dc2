package com.sankuai.meituan.waimai.heron.contract.gateway.query.service;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.bo.SessionContextBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.bo.WmLogisticsExtTagsBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.WmPoiLogisticsExtPo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.WmPoiLogisticsExtBaseService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query.EffectiveAndFlowStructJudgeResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EffectiveAndFlowStructJudgeAbilityServiceTest {

    @Mock
    private WmPoiLogisticsExtBaseService wmPoiLogisticsExtBaseService;
    @Mock
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;


    @InjectMocks
    private EffectiveAndFlowStructJudgeAbilityService effectiveAndFlowStructJudgeAbilityService;

    @Before
    public void setUp() {
    }

    @Test
    public void testValidAndFlowSplit() throws GatewayAdapterException {
        Long wmPoiId = 123L;
        HeronContractGatewaySession session = new HeronContractGatewaySession();
        session.setStatus(ContractSessionStatusEnum.AUDITING);
        SessionContextBo context = SessionContextBo.newFlowSession();
        context.setPerfSplit(true);
        session.setContext(context);
        when(heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(wmPoiId, SessionCategoryEnum.CORE)).thenReturn(session);

        WmPoiLogisticsExtPo extPo = new WmPoiLogisticsExtPo();
        WmLogisticsExtTagsBo tags = new WmLogisticsExtTagsBo();
        tags.setPerfSplit(true);
        extPo.setTags(tags);
        when(wmPoiLogisticsExtBaseService.getByWmPoiId(wmPoiId)).thenReturn(extPo);

        EffectiveAndFlowStructJudgeResult result = effectiveAndFlowStructJudgeAbilityService.judgeStructHandleSgTransferDr(wmPoiId);

        assertNotNull(result);
        assertTrue(result.getValidPerfSplit());
        assertFalse(result.getValidPerfDR());
        assertTrue(result.getFlowPerfSplit());
        assertFalse(result.getFlowPerfDR());
    }

    @Test
    public void testValidAndFlowDR() throws GatewayAdapterException {
        Long wmPoiId = 123L;
        HeronContractGatewaySession session = new HeronContractGatewaySession();
        session.setStatus(ContractSessionStatusEnum.AUDITING);
        SessionContextBo context = SessionContextBo.newFlowSession();
        context.setPerfDR(true);
        session.setContext(context);
        when(heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(wmPoiId, SessionCategoryEnum.CORE)).thenReturn(session);

        WmPoiLogisticsExtPo extPo = new WmPoiLogisticsExtPo();
        WmLogisticsExtTagsBo tags = new WmLogisticsExtTagsBo();
        tags.setPerfDR(true);
        extPo.setTags(tags);
        when(wmPoiLogisticsExtBaseService.getByWmPoiId(wmPoiId)).thenReturn(extPo);

        EffectiveAndFlowStructJudgeResult result = effectiveAndFlowStructJudgeAbilityService.judgeStructHandleSgTransferDr(wmPoiId);

        assertNotNull(result);
        assertFalse(result.getValidPerfSplit());
        assertFalse(result.getValidPerfDR());
        assertFalse(result.getFlowPerfSplit());
        assertFalse(result.getFlowPerfDR());
    }

}
