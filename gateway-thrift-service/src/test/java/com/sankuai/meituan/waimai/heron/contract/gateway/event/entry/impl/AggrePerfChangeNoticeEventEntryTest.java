package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event.AggrePerfChangeNoticeParam;
import java.util.Optional;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AggrePerfChangeNoticeEventEntryTest {

    @InjectMocks
    private AggrePerfChangeNoticeEventEntry aggrePerfChangeNoticeEventEntry;

    @Mock
    private HeronContractGatewaySession session;

    /**
     * 测试 eventScene 方法
     */
    @Test
    public void testEventScene() throws Throwable {
        // arrange
        AggrePerfChangeNoticeEventEntry entry = new AggrePerfChangeNoticeEventEntry();
        // act
        ContractCreateAndUpdateSceneEnum result = entry.eventScene();
        // assert
        assertEquals(ContractCreateAndUpdateSceneEnum.AGGRE_PERF_CHANGE_NOTICE, result);
    }

    /**
     * Test fillSession method under normal conditions.
     */
    @Test
    public void testFillSessionNormal() throws Throwable {
        // Arrange
        AggrePerfChangeNoticeParam param = mock(AggrePerfChangeNoticeParam.class);
        when(param.getWmPoiId()).thenReturn(123L);
        // Assuming getOpId() and getOpName() exist, but are not mocked due to the issue.
        when(param.getSessionCategory()).thenReturn(SessionCategoryEnum.CORE);
        // Act
        aggrePerfChangeNoticeEventEntry.fillSession(session, param);
        // Assert
        verify(session).setWmPoiId(123L);
        // Assuming setOpId(-1L) is a mistake and should be corrected based on the actual method to set this value.
        verify(session).setSessionCategory(Optional.ofNullable(param.getSessionCategory()).orElse(SessionCategoryEnum.CORE));
    }

    /**
     * Test fillSession method when AggrePerfChangeNoticeParam is null.
     */
    @Test(expected = NullPointerException.class)
    public void testFillSessionWithNullParam() throws Throwable {
        // Arrange
        AggrePerfChangeNoticeParam param = null;
        // Act
        aggrePerfChangeNoticeEventEntry.fillSession(session, param);
    }

    /**
     * Test fillSession method when sessionCategory is null.
     */
    @Test
    public void testFillSessionWithNullSessionCategory() throws Throwable {
        // Arrange
        AggrePerfChangeNoticeParam param = mock(AggrePerfChangeNoticeParam.class);
        when(param.getWmPoiId()).thenReturn(123L);
        // Assuming getOpId() and getOpName() exist, but are not mocked due to the issue.
        when(param.getSessionCategory()).thenReturn(null);
        // Act
        aggrePerfChangeNoticeEventEntry.fillSession(session, param);
        // Assert
        verify(session).setWmPoiId(123L);
        verify(session).setSessionCategory(Optional.ofNullable(param.getSessionCategory()).orElse(SessionCategoryEnum.CORE));
    }
}