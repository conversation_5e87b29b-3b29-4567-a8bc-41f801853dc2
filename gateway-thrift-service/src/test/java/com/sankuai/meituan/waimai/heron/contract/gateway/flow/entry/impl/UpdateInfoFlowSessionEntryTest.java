package com.sankuai.meituan.waimai.heron.contract.gateway.flow.entry.impl;

import com.google.common.collect.Lists;
import com.sankuai.meituan.banma.business.poi.sparea.client.common.response.SpAreaProcessControlResponse;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.NeedCreatePerfFlowSessionData;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfSettleFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmSpAreaQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.WmPoiQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.WmPoiLogisticsRelPo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.WmPoiLogisticsBaseService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.config.MccConfig;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.gray.PerfSplitSaveGrayDecider;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.entry.AbstractLogisticsSaveFlowBaseEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ApplySignActionEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.DrOriginFlowParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractUpdateAndSceneParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractUpdateSceneExtParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.save.ContractSaveGrayResult;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2024-09-27 10:52
 */
@RunWith(MockitoJUnitRunner.class)
public class UpdateInfoFlowSessionEntryTest {

    @InjectMocks
    private UpdateInfoFlowSessionEntry updateInfoFlowSessionEntry;

    @Mock
    private WmPoiLogisticsBaseService wmPoiLogisticsBaseService;

    @Mock
    private WmPoiQueryThriftServiceAdapter wmPoiQueryThriftServiceAdapter;

    @Mock
    private PerfSplitSaveGrayDecider perfSplitSaveGrayDecider;

    @Mock
    private BmSpAreaQueryThriftServiceAdapter bmSpAreaQueryThriftServiceAdapter;

    @Mock
    private BmPerfSettleFlowThriftServiceAdapter bmPerfSettleFlowThriftServiceAdapter;

    SpAreaProcessControlResponse areaResp;

    BmContractPlatformProcessResp<NeedCreatePerfFlowSessionData> perfResp;

    MockedStatic<MccConfig> mockedStatic;

    HeronContractGatewaySession session;

    @Before
    public void setUp() {
        areaResp = new SpAreaProcessControlResponse();
        areaResp.setIsNotSupportAbility(false);

        perfResp = new BmContractPlatformProcessResp<>();
        NeedCreatePerfFlowSessionData data = new NeedCreatePerfFlowSessionData();
        data.setNeedCreatePerfFlowSession(true);
        perfResp.setData(data);

        session = new HeronContractGatewaySession();
        session.setStatus(ContractSessionStatusEnum.AUDITING);
        session.setSessionLeafId(123123L);

        mockedStatic = Mockito.mockStatic(MccConfig.class);
        mockedStatic.when(MccConfig::getAssistLogisticsCodeList).thenReturn(Lists.newArrayList("5001", "2010", "4015", "4018"));
        mockedStatic.when(MccConfig::getUseSgTransferNewMethodSwitch).thenReturn(true);
    }

    @After
    public void tearDown() {
        mockedStatic.close();
    }

    @Test
    public void testAutoBatchApplySignAction() throws Throwable {
        HeronContractUpdateAndSceneParam saveRequestParam = mock(HeronContractUpdateAndSceneParam.class);
        HeronContractUpdateSceneExtParam updateSceneExtParam = mock(HeronContractUpdateSceneExtParam.class);
        DrOriginFlowParam drOriginFlowParam = mock(DrOriginFlowParam.class);

        when(saveRequestParam.getUpdateSceneExtParam()).thenReturn(updateSceneExtParam);
        when(updateSceneExtParam.getDrOriginFlowParam()).thenReturn(drOriginFlowParam);
        when(drOriginFlowParam.getSgTransferOriginContractVersionId()).thenReturn(123L);
        when(saveRequestParam.getUpdateScene()).thenReturn(ContractCreateAndUpdateSceneEnum.AUTO_CHANGE_CONTRACT);
        when(saveRequestParam.getSessionCategory()).thenReturn(SessionCategoryEnum.CORE);
        when(saveRequestParam.getOperator()).thenReturn(HeronContractOperator.builder().opId(-1L).opName("TEST").build());
        when(wmPoiLogisticsBaseService.getByWmPoiIdWithSessionCategory(any(),any())).thenReturn(new ArrayList<>());
        when(wmPoiQueryThriftServiceAdapter.getWmPoiAggreByWmPoiId(any(),any())).thenReturn(new WmPoiAggre());
        when(perfSplitSaveGrayDecider.judgeGrayForWm(any())).thenReturn(new ContractSaveGrayResult());
        when(bmSpAreaQueryThriftServiceAdapter.eventApiNeedGray(anyLong(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.areaNewGrayV3Result(anyLong(), any(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.judgeProcessControlSpAreaAbility(any(), any())).thenReturn(areaResp);
        when(bmPerfSettleFlowThriftServiceAdapter.isNeedCreatePerfFlowSession(any())).thenReturn(perfResp);

        // act
        updateInfoFlowSessionEntry.fillSession(session, saveRequestParam);

        // assert
        assertEquals(ApplySignActionEnum.AUTO_BATCH, session.getContext().getApplySignAction());
    }


    @Test
    public void testAutoPackageApplySignAction() throws Throwable {
        HeronContractUpdateAndSceneParam saveRequestParam = mock(HeronContractUpdateAndSceneParam.class);
        HeronContractUpdateSceneExtParam updateSceneExtParam = mock(HeronContractUpdateSceneExtParam.class);
        DrOriginFlowParam drOriginFlowParam = mock(DrOriginFlowParam.class);

        when(saveRequestParam.getUpdateSceneExtParam()).thenReturn(updateSceneExtParam);
        when(updateSceneExtParam.getDrOriginFlowParam()).thenReturn(drOriginFlowParam);
        when(drOriginFlowParam.getSgTransferOriginContractVersionId()).thenReturn(123L);
        when(saveRequestParam.getUpdateScene()).thenReturn(ContractCreateAndUpdateSceneEnum.C1_EXPIRE_AUTO_SIGN);
        when(saveRequestParam.getSessionCategory()).thenReturn(SessionCategoryEnum.CORE);
        when(saveRequestParam.getOperator()).thenReturn(HeronContractOperator.builder().opId(-1L).opName("TEST").build());
        when(wmPoiLogisticsBaseService.getByWmPoiIdWithSessionCategory(any(),any())).thenReturn(new ArrayList<>());
        when(wmPoiQueryThriftServiceAdapter.getWmPoiAggreByWmPoiId(any(),any())).thenReturn(new WmPoiAggre());
        when(perfSplitSaveGrayDecider.judgeGrayForWm(any())).thenReturn(new ContractSaveGrayResult());
        when(bmSpAreaQueryThriftServiceAdapter.eventApiNeedGray(anyLong(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.judgeProcessControlSpAreaAbility(any(), any())).thenReturn(areaResp);
        when(bmPerfSettleFlowThriftServiceAdapter.isNeedCreatePerfFlowSession(any())).thenReturn(perfResp);

        // act
        updateInfoFlowSessionEntry.fillSession(session, saveRequestParam);

        // assert
        assertEquals(ApplySignActionEnum.SINGLE_LATER_AUTO_PACKAGE, session.getContext().getApplySignAction());
    }

    @Test
    public void testDirectApplySignAction() throws Throwable {
        HeronContractUpdateAndSceneParam saveRequestParam = mock(HeronContractUpdateAndSceneParam.class);
        HeronContractUpdateSceneExtParam updateSceneExtParam = mock(HeronContractUpdateSceneExtParam.class);
        DrOriginFlowParam drOriginFlowParam = mock(DrOriginFlowParam.class);

        when(saveRequestParam.getUpdateSceneExtParam()).thenReturn(updateSceneExtParam);
        when(updateSceneExtParam.getDrOriginFlowParam()).thenReturn(drOriginFlowParam);
        when(drOriginFlowParam.getSgTransferOriginContractVersionId()).thenReturn(123L);
        when(saveRequestParam.getUpdateScene()).thenReturn(ContractCreateAndUpdateSceneEnum.C1_EXPIRED_AUTO_EFFECT);
        when(saveRequestParam.getSessionCategory()).thenReturn(SessionCategoryEnum.CORE);
        when(saveRequestParam.getOperator()).thenReturn(HeronContractOperator.builder().opId(-1L).opName("TEST").build());
        when(wmPoiLogisticsBaseService.getByWmPoiIdWithSessionCategory(any(),any())).thenReturn(new ArrayList<>());
        when(wmPoiQueryThriftServiceAdapter.getWmPoiAggreByWmPoiId(any(),any())).thenReturn(new WmPoiAggre());
        when(perfSplitSaveGrayDecider.judgeGrayForWm(any())).thenReturn(new ContractSaveGrayResult());
        when(bmSpAreaQueryThriftServiceAdapter.eventApiNeedGray(anyLong(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.judgeProcessControlSpAreaAbility(any(), any())).thenReturn(areaResp);
        when(bmPerfSettleFlowThriftServiceAdapter.isNeedCreatePerfFlowSession(any())).thenReturn(perfResp);

        // act
        updateInfoFlowSessionEntry.fillSession(session, saveRequestParam);

        // assert
        assertEquals(ApplySignActionEnum.DIRECT, session.getContext().getApplySignAction());
    }

    @Test
    public void testLaterApplySignAction() throws Throwable {
        HeronContractUpdateAndSceneParam saveRequestParam = mock(HeronContractUpdateAndSceneParam.class);
        HeronContractUpdateSceneExtParam updateSceneExtParam = mock(HeronContractUpdateSceneExtParam.class);
        DrOriginFlowParam drOriginFlowParam = mock(DrOriginFlowParam.class);

        when(saveRequestParam.getUpdateSceneExtParam()).thenReturn(updateSceneExtParam);
        when(updateSceneExtParam.getDrOriginFlowParam()).thenReturn(drOriginFlowParam);
        when(drOriginFlowParam.getSgTransferOriginContractVersionId()).thenReturn(123L);
        when(saveRequestParam.getUpdateScene()).thenReturn(ContractCreateAndUpdateSceneEnum.BATCH_PLATFORM_UPDATE);
        when(saveRequestParam.getSessionCategory()).thenReturn(SessionCategoryEnum.CORE);
        when(saveRequestParam.getOperator()).thenReturn(HeronContractOperator.builder().opId(-1L).opName("TEST").build());
        when(wmPoiLogisticsBaseService.getByWmPoiIdWithSessionCategory(any(),any())).thenReturn(new ArrayList<>());
        when(wmPoiQueryThriftServiceAdapter.getWmPoiAggreByWmPoiId(any(),any())).thenReturn(new WmPoiAggre());
        when(perfSplitSaveGrayDecider.judgeGrayForWm(any())).thenReturn(new ContractSaveGrayResult());
        when(bmSpAreaQueryThriftServiceAdapter.eventApiNeedGray(anyLong(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.areaNewGrayV3Result(anyLong(), any(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.judgeProcessControlSpAreaAbility(any(), any())).thenReturn(areaResp);
        when(bmPerfSettleFlowThriftServiceAdapter.isNeedCreatePerfFlowSession(any())).thenReturn(perfResp);

        // act
        updateInfoFlowSessionEntry.fillSession(session, saveRequestParam);

        // assert
        assertEquals(ApplySignActionEnum.LATER, session.getContext().getApplySignAction());
    }


    @Test
    public void testPerfSplitAreaSplit() throws Throwable {
        HeronContractUpdateAndSceneParam saveRequestParam = mock(HeronContractUpdateAndSceneParam.class);
        HeronContractUpdateSceneExtParam updateSceneExtParam = mock(HeronContractUpdateSceneExtParam.class);
        DrOriginFlowParam drOriginFlowParam = mock(DrOriginFlowParam.class);
        when(saveRequestParam.getUpdateScene()).thenReturn(ContractCreateAndUpdateSceneEnum.AUTO_CHANGE_CONTRACT);
        when(saveRequestParam.getUpdateSceneExtParam()).thenReturn(updateSceneExtParam);
        when(updateSceneExtParam.getDrOriginFlowParam()).thenReturn(drOriginFlowParam);
        when(drOriginFlowParam.getSgTransferOriginContractVersionId()).thenReturn(123L);
        when(saveRequestParam.getOperator()).thenReturn(HeronContractOperator.builder().opId(-1L).opName("TEST").build());
        ArrayList<WmPoiLogisticsRelPo> list = new ArrayList<>();
        WmPoiLogisticsRelPo logisticsRelPo = new WmPoiLogisticsRelPo();
        logisticsRelPo.setLogisticsCode("5001");
        list.add(logisticsRelPo);
        when(wmPoiLogisticsBaseService.getByWmPoiIdWithSessionCategory(any(),any())).thenReturn(list);
        WmPoiAggre poiAggre = new WmPoiAggre();
        poiAggre.setBiz_org_code(14010);
        when(wmPoiQueryThriftServiceAdapter.getWmPoiAggreByWmPoiId(any(),any())).thenReturn(poiAggre);
        ContractSaveGrayResult grayResult = new ContractSaveGrayResult();
        grayResult.setAreaSplit(true);
        grayResult.setPerfSplit(true);
        when(perfSplitSaveGrayDecider.judgeGrayForWm(any())).thenReturn(grayResult);
        when(bmSpAreaQueryThriftServiceAdapter.eventApiNeedGray(anyLong(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.areaNewGrayV3Result(anyLong(), any(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.judgeProcessControlSpAreaAbility(any(), any())).thenReturn(areaResp);
        when(bmPerfSettleFlowThriftServiceAdapter.isNeedCreatePerfFlowSession(any())).thenReturn(perfResp);

        // act
        updateInfoFlowSessionEntry.fillSession(session, saveRequestParam);

        // assert
        assertEquals(true, session.getContext().getAreaSplit());
    }


    @Test
    public void testWmAreaSplit() throws Throwable {
        HeronContractUpdateAndSceneParam saveRequestParam = mock(HeronContractUpdateAndSceneParam.class);
        HeronContractUpdateSceneExtParam updateSceneExtParam = mock(HeronContractUpdateSceneExtParam.class);
        DrOriginFlowParam drOriginFlowParam = mock(DrOriginFlowParam.class);
        when(saveRequestParam.getUpdateScene()).thenReturn(ContractCreateAndUpdateSceneEnum.AUTO_CHANGE_CONTRACT);
        when(saveRequestParam.getUpdateSceneExtParam()).thenReturn(updateSceneExtParam);
        when(updateSceneExtParam.getDrOriginFlowParam()).thenReturn(drOriginFlowParam);
        when(drOriginFlowParam.getSgTransferOriginContractVersionId()).thenReturn(123L);
        when(saveRequestParam.getOperator()).thenReturn(HeronContractOperator.builder().opId(-1L).opName("TEST").build());
        ArrayList<WmPoiLogisticsRelPo> list = new ArrayList<>();
        WmPoiLogisticsRelPo logisticsRelPo = new WmPoiLogisticsRelPo();
        logisticsRelPo.setLogisticsCode("5001");
        list.add(logisticsRelPo);
        when(wmPoiLogisticsBaseService.getByWmPoiIdWithSessionCategory(any(),any())).thenReturn(list);
        WmPoiAggre poiAggre = new WmPoiAggre();
        poiAggre.setBiz_org_code(14010);
        when(wmPoiQueryThriftServiceAdapter.getWmPoiAggreByWmPoiId(any(),any())).thenReturn(poiAggre);
        ContractSaveGrayResult grayResult = new ContractSaveGrayResult();
        grayResult.setAreaSplit(true);
        when(perfSplitSaveGrayDecider.judgeGrayForWm(any())).thenReturn(grayResult);
        when(bmSpAreaQueryThriftServiceAdapter.eventApiNeedGray(anyLong(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.areaNewGrayV3Result(anyLong(), any(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.judgeProcessControlSpAreaAbility(any(), any())).thenReturn(areaResp);
        when(bmPerfSettleFlowThriftServiceAdapter.isNeedCreatePerfFlowSession(any())).thenReturn(perfResp);

        // act
        updateInfoFlowSessionEntry.fillSession(session, saveRequestParam);

        // assert
        assertEquals(false, session.getContext().getAreaSplit());
    }


    @Test
    public void testSgAreaSplit() throws Throwable {

        HeronContractUpdateAndSceneParam saveRequestParam = mock(HeronContractUpdateAndSceneParam.class);
        HeronContractUpdateSceneExtParam updateSceneExtParam = mock(HeronContractUpdateSceneExtParam.class);
        DrOriginFlowParam drOriginFlowParam = mock(DrOriginFlowParam.class);
        when(saveRequestParam.getUpdateScene()).thenReturn(ContractCreateAndUpdateSceneEnum.AUTO_CHANGE_CONTRACT);
        when(saveRequestParam.getUpdateSceneExtParam()).thenReturn(updateSceneExtParam);
        when(updateSceneExtParam.getDrOriginFlowParam()).thenReturn(drOriginFlowParam);
        when(drOriginFlowParam.getSgTransferOriginContractVersionId()).thenReturn(123L);
        when(saveRequestParam.getOperator()).thenReturn(HeronContractOperator.builder().opId(-1L).opName("TEST").build());
        ArrayList<WmPoiLogisticsRelPo> list = new ArrayList<>();
        WmPoiLogisticsRelPo logisticsRelPo = new WmPoiLogisticsRelPo();
        logisticsRelPo.setLogisticsCode("5001");
        list.add(logisticsRelPo);
        when(wmPoiLogisticsBaseService.getByWmPoiIdWithSessionCategory(any(),any())).thenReturn(list);
        WmPoiAggre poiAggre = new WmPoiAggre();
        poiAggre.setBiz_org_code(14060);
        when(wmPoiQueryThriftServiceAdapter.getWmPoiAggreByWmPoiId(any(),any())).thenReturn(poiAggre);
        ContractSaveGrayResult grayResult = new ContractSaveGrayResult();
        grayResult.setAreaSplit(true);
        when(perfSplitSaveGrayDecider.judgeGrayForWm(any())).thenReturn(grayResult);
        when(bmSpAreaQueryThriftServiceAdapter.eventApiNeedGray(anyLong(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.areaNewGrayV3Result(anyLong(), any(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.judgeProcessControlSpAreaAbility(any(), any())).thenReturn(areaResp);
        when(bmPerfSettleFlowThriftServiceAdapter.isNeedCreatePerfFlowSession(any())).thenReturn(perfResp);

        // act
        updateInfoFlowSessionEntry.fillSession(session, saveRequestParam);

        // assert
        assertEquals(true, session.getContext().getAreaSplit());
    }

    @Test
    public void testArriveShopHasAreaAndHasPerf() throws Throwable {

        HeronContractUpdateAndSceneParam saveRequestParam = mock(HeronContractUpdateAndSceneParam.class);
        HeronContractUpdateSceneExtParam updateSceneExtParam = mock(HeronContractUpdateSceneExtParam.class);
        DrOriginFlowParam drOriginFlowParam = mock(DrOriginFlowParam.class);
        when(saveRequestParam.getUpdateScene()).thenReturn(ContractCreateAndUpdateSceneEnum.AUTO_CHANGE_CONTRACT);
        when(saveRequestParam.getUpdateSceneExtParam()).thenReturn(updateSceneExtParam);
        when(saveRequestParam.getSessionCategory()).thenReturn(SessionCategoryEnum.ARRIVE_SHOP);
        when(updateSceneExtParam.getDrOriginFlowParam()).thenReturn(drOriginFlowParam);
        when(drOriginFlowParam.getSgTransferOriginContractVersionId()).thenReturn(123L);
        when(saveRequestParam.getOperator()).thenReturn(HeronContractOperator.builder().opId(-1L).opName("TEST").build());
        ArrayList<WmPoiLogisticsRelPo> list = new ArrayList<>();
        when(wmPoiLogisticsBaseService.getByWmPoiIdWithSessionCategory(any(),any())).thenReturn(list);
        WmPoiAggre poiAggre = new WmPoiAggre();
        when(wmPoiQueryThriftServiceAdapter.getWmPoiAggreByWmPoiId(any(),any())).thenReturn(poiAggre);
        ContractSaveGrayResult grayResult = new ContractSaveGrayResult();
        grayResult.setAreaSplit(true);
        when(perfSplitSaveGrayDecider.judgeGrayForWm(any())).thenReturn(grayResult);
        when(bmSpAreaQueryThriftServiceAdapter.eventApiNeedGray(anyLong(), any())).thenReturn(true);
        when(bmSpAreaQueryThriftServiceAdapter.areaNewGrayV3Result(anyLong(), any(), any())).thenReturn(true);

        // act
        updateInfoFlowSessionEntry.fillSession(session, saveRequestParam);

        // assert
        assertEquals(false, session.getContext().getHasArea());
        assertEquals(false, session.getContext().getHasPerf());
    }

}