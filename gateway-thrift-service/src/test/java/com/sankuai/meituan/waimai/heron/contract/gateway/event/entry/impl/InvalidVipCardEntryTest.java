package com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.LogisticsExtTagsHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.monitor.CatLogMonitor;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.dispatcher.UnbindLogisticsInfoReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.UnbindLogisticsInfoParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.AllStructFlowTerminateFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class InvalidVipCardEntryTest {

    @Mock
    private UnbindLogisticsInfoReqDispatcher unbindLogisticsInfoReqDispatcher;

    @Mock
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Mock
    private AllStructFlowTerminateFlowStep allStructFlowTerminateFlowStep;

    @Mock
    private LogisticsExtTagsHelper logisticsExtTagsHelper;

    @Mock
    private CatLogMonitor catLogMonitor;

    @Mock
    private PlatContractFlowThriftServiceAdapter platContractFlowThriftServiceAdapter;

    @Spy
    @InjectMocks
    private TestableInvalidVipCardEntry invalidVipCardEntry;

    private UnbindLogisticsInfoParam validParam;

    private HeronContractGatewaySession testSession;

    @Before
    public void setUp() {
        HeronContractOperator operator = new HeronContractOperator();
        operator.setOpId(1L);
        operator.setOpName("testOperator");
        validParam = new UnbindLogisticsInfoParam();
        validParam.setWmPoiId(123L);
        validParam.setSessionCategory(SessionCategoryEnum.VIP_CARD);
        validParam.setOperator(operator);
        testSession = new HeronContractGatewaySession();
        testSession.setSessionLeafId(1L);
        testSession.setWmPoiId(123L);
        testSession.setSessionCategory(SessionCategoryEnum.VIP_CARD);
        testSession.setStatus(ContractSessionStatusEnum.EVENT_CREATE);
        doNothing().when(catLogMonitor).catMonitor(anyString(), anyLong());
    }

    /**
     * Test normal scenario where all fields in UnbindLogisticsInfoParam are correctly populated.
     */
    @Test
    public void testFillSessionNormalScenario() throws Throwable {
        // arrange
        InvalidVipCardEntry entry = new InvalidVipCardEntry();
        HeronContractGatewaySession session = new HeronContractGatewaySession();
        UnbindLogisticsInfoParam param = new UnbindLogisticsInfoParam();
        param.setWmPoiId(12345L);
        HeronContractOperator operator = new HeronContractOperator();
        operator.setOpId(67890L);
        operator.setOpName("John Doe");
        param.setOperator(operator);
        // act
        entry.fillSession(session, param);
        // assert
        assertEquals(12345L, session.getWmPoiId().longValue());
        assertEquals(67890L, session.getOpId().longValue());
        assertEquals("John Doe", session.getOpName());
        assertEquals(SessionCategoryEnum.VIP_CARD, session.getSessionCategory());
    }

    /**
     * Test scenario where the param object is null.
     */
    @Test(expected = NullPointerException.class)
    public void testFillSessionNullParam() throws Throwable {
        // arrange
        InvalidVipCardEntry entry = new InvalidVipCardEntry();
        HeronContractGatewaySession session = new HeronContractGatewaySession();
        // act
        entry.fillSession(session, null);
        // assert
        // Expecting NullPointerException
    }

    /**
     * Test scenario where the operator field in param is null.
     */
    @Test(expected = NullPointerException.class)
    public void testFillSessionNullOperator() throws Throwable {
        // arrange
        InvalidVipCardEntry entry = new InvalidVipCardEntry();
        HeronContractGatewaySession session = new HeronContractGatewaySession();
        UnbindLogisticsInfoParam param = new UnbindLogisticsInfoParam();
        param.setWmPoiId(12345L);
        param.setOperator(null);
        // act
        entry.fillSession(session, param);
        // assert
        // Expecting NullPointerException
    }

    /**
     * Test scenario where the wmPoiId field in param is null.
     */
    @Test
    public void testFillSessionNullWmPoiId() throws Throwable {
        // arrange
        InvalidVipCardEntry entry = new InvalidVipCardEntry();
        HeronContractGatewaySession session = new HeronContractGatewaySession();
        UnbindLogisticsInfoParam param = new UnbindLogisticsInfoParam();
        param.setWmPoiId(null);
        HeronContractOperator operator = new HeronContractOperator();
        operator.setOpId(67890L);
        operator.setOpName("John Doe");
        param.setOperator(operator);
        // act
        entry.fillSession(session, param);
        // assert
        assertNull(session.getWmPoiId());
        assertEquals(67890L, session.getOpId().longValue());
        assertEquals("John Doe", session.getOpName());
        assertEquals(SessionCategoryEnum.VIP_CARD, session.getSessionCategory());
    }

    /**
     * Test scenario where the opId field in operator is null.
     */
    @Test
    public void testFillSessionNullOpId() throws Throwable {
        // arrange
        InvalidVipCardEntry entry = new InvalidVipCardEntry();
        HeronContractGatewaySession session = new HeronContractGatewaySession();
        UnbindLogisticsInfoParam param = new UnbindLogisticsInfoParam();
        param.setWmPoiId(12345L);
        HeronContractOperator operator = new HeronContractOperator();
        operator.setOpId(null);
        operator.setOpName("John Doe");
        param.setOperator(operator);
        // act
        entry.fillSession(session, param);
        // assert
        assertEquals(12345L, session.getWmPoiId().longValue());
        assertNull(session.getOpId());
        assertEquals("John Doe", session.getOpName());
        assertEquals(SessionCategoryEnum.VIP_CARD, session.getSessionCategory());
    }

    /**
     * Test scenario where the opName field in operator is null.
     */
    @Test
    public void testFillSessionNullOpName() throws Throwable {
        // arrange
        InvalidVipCardEntry entry = new InvalidVipCardEntry();
        HeronContractGatewaySession session = new HeronContractGatewaySession();
        UnbindLogisticsInfoParam param = new UnbindLogisticsInfoParam();
        param.setWmPoiId(12345L);
        HeronContractOperator operator = new HeronContractOperator();
        operator.setOpId(67890L);
        operator.setOpName(null);
        param.setOperator(operator);
        // act
        entry.fillSession(session, param);
        // assert
        assertEquals(12345L, session.getWmPoiId().longValue());
        assertEquals(67890L, session.getOpId().longValue());
        assertNull(session.getOpName());
        assertEquals(SessionCategoryEnum.VIP_CARD, session.getSessionCategory());
    }

    /**
     * 测试 eventScene 方法
     */
    @Test
    public void testEventScene() throws Throwable {
        // arrange
        InvalidVipCardEntry invalidVipCardEntry = new InvalidVipCardEntry();
        // act
        ContractCreateAndUpdateSceneEnum result = invalidVipCardEntry.eventScene();
        // assert
        assertEquals(ContractCreateAndUpdateSceneEnum.INVALID_VIP_CARD_CONTRACT, result);
    }

    private static class TestableInvalidVipCardEntry extends InvalidVipCardEntry {

        @Override
        protected HeronContractGatewaySession buildSession(UnbindLogisticsInfoParam param) {
            HeronContractGatewaySession session = new HeronContractGatewaySession();
            session.setSessionLeafId(1L);
            session.setBatchRelationId("batch123");
            session.setWmPoiId(param.getWmPoiId());
            session.setSessionCategory(param.getSessionCategory());
            session.setStatus(ContractSessionStatusEnum.EVENT_CREATE);
            return session;
        }

        @Override
        protected ContractCreateAndUpdateSceneEnum eventScene() {
            return ContractCreateAndUpdateSceneEnum.INVALID_VIP_CARD_CONTRACT;
        }

        @Override
        protected void fillSession(HeronContractGatewaySession session, UnbindLogisticsInfoParam param) {
            session.setWmPoiId(param.getWmPoiId());
            session.setSessionCategory(param.getSessionCategory());
        }
    }

    @Test(expected = IllegalArgumentException.class)
    public void testProcess_InvalidSessionCategory() throws Throwable {
        // arrange
        validParam.setSessionCategory(SessionCategoryEnum.CORE);
        // act
        invalidVipCardEntry.process(validParam);
    }
}