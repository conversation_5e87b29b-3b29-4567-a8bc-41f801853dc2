package com.sankuai.meituan.waimai.heron.contract.gateway.core.helper;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.WmPoiLogisticsContractBatchPo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.WmPoiLogisticsContractBatchBaseService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SessionSceneGroupEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class SessionCategoryHelperTest {

    @InjectMocks
    private SessionCategoryBuildHelper mockSessionCategoryBuildHelper;

    @Mock
    private WmPoiLogisticsContractBatchBaseService wmPoiLogisticsContractBatchBaseService;

    @Mock
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Test
    public void testOnlyCore() throws Exception {
        Long wmPoiId = 123L;
        List<WmPoiLogisticsContractBatchPo> wmPoiLogisticsContractBatchPos = new ArrayList<>();

        when(wmPoiLogisticsContractBatchBaseService.getEffectiveByWmPoiId(wmPoiId)).thenReturn(wmPoiLogisticsContractBatchPos);
        when(heronContractGatewaySessionBasicService.getLastByWmPoiIdAndGroup(wmPoiId, SessionSceneGroupEnum.FLOW, SessionCategoryEnum.DRONE)).thenReturn(null);

        List<SessionCategoryEnum> allNeedHandledSessionCategory = mockSessionCategoryBuildHelper.getAllNeedHandledWithoutPhfSessionCategory(wmPoiId);

        Assert.assertEquals(1, allNeedHandledSessionCategory.size());
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.CORE));
    }

    @Test
    public void testWithFlowDrone() throws Exception {
        Long wmPoiId = 123L;
        List<WmPoiLogisticsContractBatchPo> wmPoiLogisticsContractBatchPos = new ArrayList<>();
        HeronContractGatewaySession heronContractGatewaySession = new HeronContractGatewaySession();
        heronContractGatewaySession.setStatus(ContractSessionStatusEnum.SAVE);

        when(wmPoiLogisticsContractBatchBaseService.getEffectiveByWmPoiId(wmPoiId)).thenReturn(wmPoiLogisticsContractBatchPos);
        when(heronContractGatewaySessionBasicService.getLastByWmPoiIdAndGroup(wmPoiId, SessionSceneGroupEnum.FLOW, SessionCategoryEnum.DRONE)).thenReturn(heronContractGatewaySession);

        List<SessionCategoryEnum> allNeedHandledSessionCategory = mockSessionCategoryBuildHelper.getAllNeedHandledWithoutPhfSessionCategory(wmPoiId);

        Assert.assertEquals(2, allNeedHandledSessionCategory.size());
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.DRONE));
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.CORE));
    }
    @Test
    public void testWithValidDrone() throws Exception {
        Long wmPoiId = 123L;
        List<WmPoiLogisticsContractBatchPo> wmPoiLogisticsContractBatchPos = new ArrayList<>();
        WmPoiLogisticsContractBatchPo wmPoiLogisticsContractBatchPo = new WmPoiLogisticsContractBatchPo();
        wmPoiLogisticsContractBatchPo.setSessionCategory(SessionCategoryEnum.DRONE.name());
        wmPoiLogisticsContractBatchPos.add(wmPoiLogisticsContractBatchPo);

        when(wmPoiLogisticsContractBatchBaseService.getEffectiveByWmPoiId(wmPoiId)).thenReturn(wmPoiLogisticsContractBatchPos);
        when(heronContractGatewaySessionBasicService.getLastByWmPoiIdAndGroup(wmPoiId, SessionSceneGroupEnum.FLOW, SessionCategoryEnum.DRONE)).thenReturn(null);

        List<SessionCategoryEnum> allNeedHandledSessionCategory = mockSessionCategoryBuildHelper.getAllNeedHandledWithoutPhfSessionCategory(wmPoiId);

        Assert.assertEquals(2, allNeedHandledSessionCategory.size());
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.DRONE));
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.CORE));
    }

    @Test
    public void testWithFlowFruitTogether() throws Exception {
        Long wmPoiId = 123L;
        List<WmPoiLogisticsContractBatchPo> wmPoiLogisticsContractBatchPos = new ArrayList<>();
        HeronContractGatewaySession heronContractGatewaySession = new HeronContractGatewaySession();
        heronContractGatewaySession.setStatus(ContractSessionStatusEnum.SAVE);

        when(wmPoiLogisticsContractBatchBaseService.getEffectiveByWmPoiId(wmPoiId)).thenReturn(wmPoiLogisticsContractBatchPos);
        when(heronContractGatewaySessionBasicService.getLastByWmPoiIdAndGroup(wmPoiId, SessionSceneGroupEnum.FLOW, SessionCategoryEnum.FRUIT_TOGETHER)).thenReturn(heronContractGatewaySession);

        List<SessionCategoryEnum> allNeedHandledSessionCategory = mockSessionCategoryBuildHelper.getAllNeedHandledWithoutPhfSessionCategory(wmPoiId);

        Assert.assertEquals(2, allNeedHandledSessionCategory.size());
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.FRUIT_TOGETHER));
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.CORE));
    }

    @Test
    public void testWithValidFruitTogether() throws Exception {
        Long wmPoiId = 123L;
        List<WmPoiLogisticsContractBatchPo> wmPoiLogisticsContractBatchPos = new ArrayList<>();
        WmPoiLogisticsContractBatchPo wmPoiLogisticsContractBatchPo = new WmPoiLogisticsContractBatchPo();
        wmPoiLogisticsContractBatchPo.setSessionCategory(SessionCategoryEnum.FRUIT_TOGETHER.name());
        wmPoiLogisticsContractBatchPos.add(wmPoiLogisticsContractBatchPo);

        when(wmPoiLogisticsContractBatchBaseService.getEffectiveByWmPoiId(wmPoiId)).thenReturn(wmPoiLogisticsContractBatchPos);
        when(heronContractGatewaySessionBasicService.getLastByWmPoiIdAndGroup(wmPoiId, SessionSceneGroupEnum.FLOW, SessionCategoryEnum.FRUIT_TOGETHER)).thenReturn(null);

        List<SessionCategoryEnum> allNeedHandledSessionCategory = mockSessionCategoryBuildHelper.getAllNeedHandledWithoutPhfSessionCategory(wmPoiId);

        Assert.assertEquals(2, allNeedHandledSessionCategory.size());
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.FRUIT_TOGETHER));
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.CORE));
    }

    @Test
    public void testWithFlowArriveShop() throws Exception {
        Long wmPoiId = 123L;
        List<WmPoiLogisticsContractBatchPo> wmPoiLogisticsContractBatchPos = new ArrayList<>();
        HeronContractGatewaySession heronContractGatewaySession = new HeronContractGatewaySession();
        heronContractGatewaySession.setStatus(ContractSessionStatusEnum.SAVE);

        when(wmPoiLogisticsContractBatchBaseService.getEffectiveByWmPoiId(wmPoiId)).thenReturn(wmPoiLogisticsContractBatchPos);
        when(heronContractGatewaySessionBasicService.getLastByWmPoiIdAndGroup(wmPoiId, SessionSceneGroupEnum.FLOW, SessionCategoryEnum.ARRIVE_SHOP)).thenReturn(heronContractGatewaySession);

        List<SessionCategoryEnum> allNeedHandledSessionCategory = mockSessionCategoryBuildHelper.getAllNeedHandledWithoutPhfSessionCategory(wmPoiId);

        Assert.assertEquals(2, allNeedHandledSessionCategory.size());
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.ARRIVE_SHOP));
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.CORE));
    }

    @Test
    public void testWithValidArriveShop() throws Exception {
        Long wmPoiId = 123L;
        List<WmPoiLogisticsContractBatchPo> wmPoiLogisticsContractBatchPos = new ArrayList<>();
        WmPoiLogisticsContractBatchPo wmPoiLogisticsContractBatchPo = new WmPoiLogisticsContractBatchPo();
        wmPoiLogisticsContractBatchPo.setSessionCategory(SessionCategoryEnum.ARRIVE_SHOP.name());
        wmPoiLogisticsContractBatchPos.add(wmPoiLogisticsContractBatchPo);

        when(wmPoiLogisticsContractBatchBaseService.getEffectiveByWmPoiId(wmPoiId)).thenReturn(wmPoiLogisticsContractBatchPos);
        when(heronContractGatewaySessionBasicService.getLastByWmPoiIdAndGroup(wmPoiId, SessionSceneGroupEnum.FLOW, SessionCategoryEnum.ARRIVE_SHOP)).thenReturn(null);

        List<SessionCategoryEnum> allNeedHandledSessionCategory = mockSessionCategoryBuildHelper.getAllNeedHandledWithoutPhfSessionCategory(wmPoiId);

        Assert.assertEquals(2, allNeedHandledSessionCategory.size());
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.ARRIVE_SHOP));
        Assert.assertTrue(allNeedHandledSessionCategory.contains(SessionCategoryEnum.CORE));
    }

}