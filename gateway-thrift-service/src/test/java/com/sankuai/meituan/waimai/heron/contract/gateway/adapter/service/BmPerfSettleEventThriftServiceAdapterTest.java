package com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.SyncXzsSpecialEndTime2TspEventParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.DealEventData;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.thrift.BmContractSettleEventThriftIface;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.*;
import static org.springframework.test.util.AssertionErrors.assertNotEquals;

/**
 * 测试BmPerfSettleEventThriftServiceAdapter的syncXzsSpecialEndTime2TspEvent方法
 */
public class BmPerfSettleEventThriftServiceAdapterTest {

    @InjectMocks
    private BmPerfSettleEventThriftServiceAdapter adapter;

    @Mock
    private BmContractSettleEventThriftIface bmContractSettleEventThriftIface;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试syncXzsSpecialEndTime2TspEvent方法正常返回情况
     */
    @Test
    public void testSyncXzsSpecialEndTime2TspEventSuccess() throws Exception {
        SyncXzsSpecialEndTime2TspEventParam param = mock(SyncXzsSpecialEndTime2TspEventParam.class);
        BmContractPlatformProcessResp<DealEventData> expectedResp = new BmContractPlatformProcessResp<>();
        when(bmContractSettleEventThriftIface.syncXzsSpecialEndTime2TspEvent(param)).thenReturn(expectedResp);

        BmContractPlatformProcessResp<DealEventData> actualResp = adapter.syncXzsSpecialEndTime2TspEvent(param);

        verify(bmContractSettleEventThriftIface, times(1)).syncXzsSpecialEndTime2TspEvent(param);
        assertSame("应返回预期的响应对象", expectedResp, actualResp);
    }

    /**
     * 测试syncXzsSpecialEndTime2TspEvent方法抛出TException时的处理
     */
    @Test(expected = GatewayAdapterException.class)
    public void testSyncXzsSpecialEndTime2TspEventThrowsTException() throws Exception {
        SyncXzsSpecialEndTime2TspEventParam param = mock(SyncXzsSpecialEndTime2TspEventParam.class);
        when(bmContractSettleEventThriftIface.syncXzsSpecialEndTime2TspEvent(param)).thenThrow(new TException());

        adapter.syncXzsSpecialEndTime2TspEvent(param);
    }

    /**
     * 测试syncXzsSpecialEndTime2TspEvent方法当返回结果的success字段为false时的行为
     */
    @Test
    public void testSyncXzsSpecialEndTime2TspEventWithFailureResponse() throws Exception {
        SyncXzsSpecialEndTime2TspEventParam param = mock(SyncXzsSpecialEndTime2TspEventParam.class);
        BmContractPlatformProcessResp<DealEventData> failureResp = new BmContractPlatformProcessResp<>();
        failureResp.setSuccess(false);
        when(bmContractSettleEventThriftIface.syncXzsSpecialEndTime2TspEvent(param)).thenReturn(failureResp);

        BmContractPlatformProcessResp<DealEventData> actualResp = adapter.syncXzsSpecialEndTime2TspEvent(param);

        assertFalse("返回结果的success字段应为false", actualResp.isSuccess());
    }

    /**
     * 测试syncXzsSpecialEndTime2TspEvent方法当返回结果的code不为预期值时的行为
     */
    @Test
    public void testSyncXzsSpecialEndTime2TspEventWithUnexpectedCode() throws Exception {
        SyncXzsSpecialEndTime2TspEventParam param = mock(SyncXzsSpecialEndTime2TspEventParam.class);
        BmContractPlatformProcessResp<DealEventData> unexpectedCodeResp = new BmContractPlatformProcessResp<>();
        unexpectedCodeResp.setCode(999);
        when(bmContractSettleEventThriftIface.syncXzsSpecialEndTime2TspEvent(param)).thenReturn(unexpectedCodeResp);

        BmContractPlatformProcessResp<DealEventData> actualResp = adapter.syncXzsSpecialEndTime2TspEvent(param);

        assertNotEquals("返回结果的code应不为预期值", 0, actualResp.getCode());
    }

}
