package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.GatewayPreconditions;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.TimeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.InvalidVipCardEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.UnbindLogisticsInfoParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.OperateSourceEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.exception.HeronContractGatewayThriftException;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event.InvalidVipCardParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayNoAlarmBusinessException;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.LogisticsInfoCleanEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.CleanSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event.CleanLogistcsExtParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event.CleanPoiLogisticsInfoParam;
import org.apache.thrift.TException;
import org.mockito.ArgumentCaptor;

@RunWith(MockitoJUnitRunner.class)
public class HeronContractEventProcessGatewayThriftServiceImplInvalidVipCardContractTest {

    @Mock
    private InvalidVipCardEntry invalidVipCardEntry;

    @InjectMocks
    private HeronContractEventProcessGatewayThriftServiceImpl service;

    private HeronContractOperator mockOperator;

    @Mock
    private LogisticsInfoCleanEntry logisticsInfoCleanEntry;

    @Before
    public void setUp() {
        mockOperator = new HeronContractOperator();
        mockOperator.setOpId(1L);
        mockOperator.setOpName("operatorName");
        mockOperator.setOpMisId("operatorMisId");
    }

    /**
     * Test successful invalidation of VIP card contract
     */
    @Test
    public void testInvalidVipCardContract_ValidInput() throws Throwable {
        // Arrange
        InvalidVipCardParam param = new InvalidVipCardParam();
        param.setWmPoiId(123L);
        param.setOperator(mockOperator);
        GatewayResult expectedResult = new GatewayResult();
        expectedResult.setSuccess(true);
        when(invalidVipCardEntry.process(any(UnbindLogisticsInfoParam.class))).thenReturn(expectedResult);
        // Act
        GatewayResult result = service.invalidVipCardContract(param);
        // Assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(invalidVipCardEntry).process(argThat(unbindParam -> unbindParam.getWmPoiId().equals(123L) && unbindParam.getOperator().equals(mockOperator) && unbindParam.getSessionCategory() == SessionCategoryEnum.VIP_CARD));
    }

    /**
     * Test validation of null operator
     */
    @Test(expected = HeronContractGatewayThriftException.class)
    public void testInvalidVipCardContract_NullOperator() throws Throwable {
        // Arrange
        InvalidVipCardParam param = new InvalidVipCardParam();
        param.setWmPoiId(123L);
        param.setOperator(null);
        // Act
        service.invalidVipCardContract(param);
    }

    /**
     * Test validation of null wmPoiId
     */
    @Test(expected = HeronContractGatewayThriftException.class)
    public void testInvalidVipCardContract_InvalidWmPoiId() throws Throwable {
        // Arrange
        InvalidVipCardParam param = new InvalidVipCardParam();
        param.setWmPoiId(null);
        param.setOperator(mockOperator);
        // Act
        service.invalidVipCardContract(param);
    }

    /**
     * Test validation of zero wmPoiId
     */
    @Test(expected = HeronContractGatewayThriftException.class)
    public void testInvalidVipCardContract_ZeroWmPoiId() throws Throwable {
        // Arrange
        InvalidVipCardParam param = new InvalidVipCardParam();
        param.setWmPoiId(0L);
        param.setOperator(mockOperator);
        // Act
        service.invalidVipCardContract(param);
    }

    /**
     * Test handling of GatewayBaseException
     */
    @Test(expected = HeronContractGatewayThriftException.class)
    public void testInvalidVipCardContract_GatewayBaseException() throws Throwable {
        // Arrange
        InvalidVipCardParam param = new InvalidVipCardParam();
        param.setWmPoiId(123L);
        param.setOperator(mockOperator);
        doThrow(new GatewaySystemException(500, "Error processing")).when(invalidVipCardEntry).process(any(UnbindLogisticsInfoParam.class));
        // Act
        service.invalidVipCardContract(param);
    }

    /**
     * Test validation of negative wmPoiId
     */
    @Test(expected = HeronContractGatewayThriftException.class)
    public void testInvalidVipCardContract_NegativeWmPoiId() throws Throwable {
        // Arrange
        InvalidVipCardParam param = new InvalidVipCardParam();
        param.setWmPoiId(-1L);
        param.setOperator(mockOperator);
        // Act
        service.invalidVipCardContract(param);
    }

    @Test
    public void testCleanLogisticsInfo_Success() throws Throwable {
        // arrange
        HeronContractOperator operator = new HeronContractOperator();
        operator.setOpId(1L);
        CleanPoiLogisticsInfoParam param = new CleanPoiLogisticsInfoParam();
        param.setWmPoiId(123L);
        param.setOperator(operator);
        param.setReason("Test reason");
        // act
        service.cleanLogisticsInfo(param);
        // assert
        ArgumentCaptor<UnbindLogisticsInfoParam> captor = ArgumentCaptor.forClass(UnbindLogisticsInfoParam.class);
        verify(logisticsInfoCleanEntry).process(captor.capture());
        UnbindLogisticsInfoParam captured = captor.getValue();
        assertEquals(123L, captured.getWmPoiId().longValue());
        assertEquals("Test reason", captured.getRemark());
        assertEquals(CleanSceneEnum.DEFAULT, captured.getCleanSceneEnum());
        assertEquals(SessionCategoryEnum.CORE, captured.getSessionCategory());
    }

    @Test
    public void testCleanLogisticsInfo_WithCustomCleanScene() throws Throwable {
        // arrange
        HeronContractOperator operator = new HeronContractOperator();
        operator.setOpId(1L);
        CleanLogistcsExtParam extParam = new CleanLogistcsExtParam();
        extParam.setCleanSceneEnum(CleanSceneEnum.DEALERS_DELETE_SUBSTAY_AGGREMENT);
        CleanPoiLogisticsInfoParam param = new CleanPoiLogisticsInfoParam();
        param.setWmPoiId(123L);
        param.setOperator(operator);
        param.setReason("Test reason");
        param.setExtParam(extParam);
        // act
        service.cleanLogisticsInfo(param);
        // assert
        ArgumentCaptor<UnbindLogisticsInfoParam> captor = ArgumentCaptor.forClass(UnbindLogisticsInfoParam.class);
        verify(logisticsInfoCleanEntry).process(captor.capture());
        UnbindLogisticsInfoParam captured = captor.getValue();
        assertEquals(CleanSceneEnum.DEALERS_DELETE_SUBSTAY_AGGREMENT, captured.getCleanSceneEnum());
    }

    @Test(expected = HeronContractGatewayThriftException.class)
    public void testCleanLogisticsInfo_NullOperator() throws Throwable {
        // arrange
        CleanPoiLogisticsInfoParam param = new CleanPoiLogisticsInfoParam();
        param.setWmPoiId(123L);
        param.setReason("Test reason");
        // act
        service.cleanLogisticsInfo(param);
    }

    @Test(expected = HeronContractGatewayThriftException.class)
    public void testCleanLogisticsInfo_EmptyReason() throws Throwable {
        // arrange
        HeronContractOperator operator = new HeronContractOperator();
        operator.setOpId(1L);
        CleanPoiLogisticsInfoParam param = new CleanPoiLogisticsInfoParam();
        param.setWmPoiId(123L);
        param.setOperator(operator);
        param.setReason("");
        // act
        service.cleanLogisticsInfo(param);
    }

    @Test(expected = HeronContractGatewayThriftException.class)
    public void testCleanLogisticsInfo_InvalidWmPoiId() throws Throwable {
        // arrange
        HeronContractOperator operator = new HeronContractOperator();
        operator.setOpId(1L);
        CleanPoiLogisticsInfoParam param = new CleanPoiLogisticsInfoParam();
        param.setWmPoiId(0L);
        param.setOperator(operator);
        param.setReason("Test reason");
        // act
        service.cleanLogisticsInfo(param);
    }

    @Test(expected = HeronContractGatewayThriftException.class)
    public void testCleanLogisticsInfo_ProcessException() throws Throwable {
        // arrange
        HeronContractOperator operator = new HeronContractOperator();
        operator.setOpId(1L);
        CleanPoiLogisticsInfoParam param = new CleanPoiLogisticsInfoParam();
        param.setWmPoiId(123L);
        param.setOperator(operator);
        param.setReason("Test reason");
        // Mock the process method to throw GatewaySystemException (unchecked exception)
        doThrow(new GatewaySystemException(500, "Test error")).when(logisticsInfoCleanEntry).process(any(UnbindLogisticsInfoParam.class));
        // act
        service.cleanLogisticsInfo(param);
    }
}
