package com.sankuai.meituan.waimai.heron.contract.gateway.test.query;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.WmPoiLogisticsMissionPo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.mapper.WmPoiLogisticsMissionMapper;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.query.model.PoiLogisticsProcessingInfo;
import com.sankuai.meituan.waimai.heron.contract.gateway.query.service.LogisticsDataStructQueryService;
import com.sankuai.meituan.waimai.heron.contract.gateway.test.BaseTest;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.exception.HeronContractGatewayThriftException;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query.EffectiveAggreInfoResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query.EffectiveAndFlowStructJudgeResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query.FlowAggreInfoResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.service.HeronContractQueryGatewayThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/6/30
 */
@Slf4j
public class QueryThriftServiceTest extends BaseTest {


    @Resource
    private HeronContractQueryGatewayThriftService heronContractQueryGatewayThriftService;

    @Resource
    private RouteHelper routeHelper;

    @Resource
    private WmPoiLogisticsMissionMapper wmPoiLogisticsMissionMapper;

    @Resource
    private LogisticsDataStructQueryService logisticsDataStructQueryService;

    @Test
    public void testEffectiveInfo() throws TException, HeronContractGatewayThriftException {
        EffectiveAggreInfoResult effectiveAggreInfoResult = heronContractQueryGatewayThriftService.queryEffectiveAggreInfo(1144420L);
        log.info("effectiveAggreInfoResult: {}", JacksonUtil.writeAsJsonStr(effectiveAggreInfoResult));
        Assert.assertNotNull(effectiveAggreInfoResult);
    }


    @Test
    public void testFlowInfo() throws TException, HeronContractGatewayThriftException {
        FlowAggreInfoResult flowAggreInfoResult = heronContractQueryGatewayThriftService.queryFlowAggreInfo(1144420L);
        log.info("flowAggreInfoResult: {}", JacksonUtil.writeAsJsonStr(flowAggreInfoResult));
        Assert.assertNotNull("");
    }


    @Test
    public void testJudgeStruct() throws TException, HeronContractGatewayThriftException {
        EffectiveAndFlowStructJudgeResult value = heronContractQueryGatewayThriftService.judgeStruct(1225180L);
        System.out.println(JacksonUtil.writeAsJsonStr(value));
        Assert.assertNotNull(value);
    }

    @Test
    public void testFlowQuery() throws TException, HeronContractGatewayThriftException {
        EffectiveAndFlowStructJudgeResult value = heronContractQueryGatewayThriftService.judgeStruct(1223728L);
        System.out.println(JacksonUtil.writeAsJsonStr(value));
        System.out.println(JacksonUtil.writeAsJsonStr(heronContractQueryGatewayThriftService.queryFlowAggreInfo(1223728L)));
        Assert.assertNotNull(value);
    }

    @Test
    public void testFlowQuery2() throws TException, HeronContractGatewayThriftException {
        EffectiveAndFlowStructJudgeResult value = heronContractQueryGatewayThriftService.judgeStruct(1221955L);
        System.out.println(JacksonUtil.writeAsJsonStr(value));
        System.out.println(JacksonUtil.writeAsJsonStr(heronContractQueryGatewayThriftService.queryFlowAggreInfo(1221955L)));
        Assert.assertNotNull(value);
    }


    @Test
    public void testFlowQueryEffective() throws TException, HeronContractGatewayThriftException {
        EffectiveAndFlowStructJudgeResult value = heronContractQueryGatewayThriftService.judgeStruct(1144420L);
        System.out.println(JacksonUtil.writeAsJsonStr(value));
        System.out.println(JacksonUtil.writeAsJsonStr(heronContractQueryGatewayThriftService.queryEffectiveAggreInfo(1144420L)));
        Assert.assertNotNull(value);
    }

    @Test
    public void testMissionQuery(){
        WmPoiLogisticsMissionPo customer_change_online = wmPoiLogisticsMissionMapper.getProcessingCustomerChangeMission(917357 );
        System.out.println(JacksonUtil.writeAsJsonStr(customer_change_online));
        Assert.assertNotNull("");
    }

    @Test
    public void testDataStructQuery() throws GatewayAdapterException {
        PoiLogisticsProcessingInfo processingPoiDataStruct = logisticsDataStructQueryService.getProcessingPoiDataStruct(917357L, SessionCategoryEnum.CORE);
        System.out.println(JacksonUtil.writeAsJsonStr(processingPoiDataStruct));
        PoiLogisticsProcessingInfo processingPoiDataStruct2 = logisticsDataStructQueryService.getProcessingPoiDataStruct(1227454L, SessionCategoryEnum.CORE);
        System.out.println(JacksonUtil.writeAsJsonStr(processingPoiDataStruct2));
        PoiLogisticsProcessingInfo processingPoiDataStruct3 = logisticsDataStructQueryService.getProcessingPoiDataStruct(1116991L, SessionCategoryEnum.CORE);
        System.out.println(JacksonUtil.writeAsJsonStr(processingPoiDataStruct3));
        PoiLogisticsProcessingInfo processingPoiDataStruct4 = logisticsDataStructQueryService.getProcessingPoiDataStruct(1231846L, SessionCategoryEnum.CORE);
        System.out.println(JacksonUtil.writeAsJsonStr(processingPoiDataStruct4));
        PoiLogisticsProcessingInfo processingPoiDataStruct5 = logisticsDataStructQueryService.getProcessingPoiDataStruct(1231844L, SessionCategoryEnum.CORE);
        System.out.println(JacksonUtil.writeAsJsonStr(processingPoiDataStruct5));
        Assert.assertNotNull(processingPoiDataStruct);
    }



}
