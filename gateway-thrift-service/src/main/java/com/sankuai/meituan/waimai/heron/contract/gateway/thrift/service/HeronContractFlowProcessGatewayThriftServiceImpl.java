package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.service;

import com.google.common.base.Splitter;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayNoAlarmBusinessException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.aspect.AroundLog;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.AllStructFlowTerminateFlowProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.AutoBatchApplySignProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.BDCancelSignProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.ConfirmSignProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.DrPoiEffectSyncProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.EContractCancelWaitSignProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.EcontractCancelSignProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.ManualBatchApplySignProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.MultiCommitAuditProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.SessionFlowTerminateProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.query.service.LogisticsDataStructQueryService;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.OperateSourceEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.exception.HeronContractGatewayThriftException;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.audit.HeronContractMultiCommitAuditParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.effect.SgTransferEffectDrParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractAutoBatchSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractBdCancelSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractConfirmSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractEcontractCancelManualSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractEcontractCancelSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractManualBatchSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractSignGrayParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.terminate.AllStructFlowTerminateParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.terminate.HeronContractFlowTerminateParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.SinglePoiResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.service.HeronContractFlowProcessGatewayThriftService;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.ResultDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SinglePoiResendMsgRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/22
 */
@Service
@Slf4j
public class HeronContractFlowProcessGatewayThriftServiceImpl implements HeronContractFlowProcessGatewayThriftService {

    @Resource
    private LogisticsDataStructQueryService logisticsDataStructQueryService;

    @Resource
    private BDCancelSignProcessor bdCancelSignProcessor;

    @Resource
    private EcontractCancelSignProcessor econtractCancelSignProcessor;

    @Resource
    private EContractCancelWaitSignProcessor econtractCancelWaitSignProcessor;

    @Resource
    private ConfirmSignProcessor confirmSignProcessor;

    @Resource
    private ManualBatchApplySignProcessor manualBatchApplySignProcessor;

    @Resource
    private SessionFlowTerminateProcessor flowTerminateProcessor;

    @Resource
    private AllStructFlowTerminateFlowProcessor allStructFlowTerminateFlowProcessor;

    @Resource
    private MultiCommitAuditProcessor multiCommitAuditProcessor;

    @Resource
    private AutoBatchApplySignProcessor autoBatchApplySignProcessor;
    @Resource
    private PlatContractFlowThriftServiceAdapter platContractFlowThriftServiceAdapter;

    @Resource
    private DrPoiEffectSyncProcessor drPoiEffectSyncProcessor;


    @Override
    @AroundLog(description = "多店提审")
    public List<SinglePoiResult> multiCommitAudit(HeronContractMultiCommitAuditParam commitAuditParam) throws HeronContractGatewayThriftException, TException {
        try {
            multiCommitAuditProcessor.multiCommitAudit(commitAuditParam);
            return new ArrayList<>();
        } catch (GatewayBaseException e) {
            log.info("multiCommitAudit 多店提审 异常, 参数 param: {}, e", JacksonUtil.writeAsJsonStr(commitAuditParam), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog(description = "手动打包签约")
    public void manualBatchApplySign(HeronContractManualBatchSignParam batchSignParam) throws HeronContractGatewayThriftException, TException {
        try {
            manualBatchApplySignProcessor.manualApplySign(batchSignParam);
        } catch (GatewayBaseException e) {
            log.info("manualBatchApplySign 手动打包签约 异常, 参数 param: {}, e", JacksonUtil.writeAsJsonStr(batchSignParam), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog(description = "自动批量签约")
    public void autoBatchApplySign(HeronContractAutoBatchSignParam batchSignParam) throws HeronContractGatewayThriftException {
        try {
            autoBatchApplySignProcessor.batchApplySign(batchSignParam);
        } catch (GatewayBaseException e) {
            log.info("autoBatchApplySign 自动批量异常, 参数 param: {}, e", JacksonUtil.writeAsJsonStr(batchSignParam), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog(description = "先富配送信息BD取消签约")
    public void bdCancelSign(HeronContractBdCancelSignParam cancelSignParam) throws HeronContractGatewayThriftException, TException {
        try {
            FlowExecResult flowExecResult = bdCancelSignProcessor.cancelSign(cancelSignParam);
            if (flowExecResult.isFail()) {
                throw new GatewayNoAlarmBusinessException(flowExecResult.getMessage());
            }
        } catch (GatewayBaseException e) {
            log.info("bdCancelSign 先富配送信息BD取消签约异常, 参数 param: {}, e", JacksonUtil.writeAsJsonStr(cancelSignParam), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog(description = "电子合同取消签约")
    public void econtractCancelSign(HeronContractEcontractCancelSignParam cancelSignParam) throws HeronContractGatewayThriftException, TException {
        try {
            econtractCancelSignProcessor.cancelSign(cancelSignParam);
        } catch (GatewayBaseException e) {
            log.info("econtractCancelSign 电子合同取消签约异常, 参数 param: {}, e: ", JacksonUtil.writeAsJsonStr(cancelSignParam), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog(description = "电子合同取消待签约任务")
    public void econtractCancelManualSign(HeronContractEcontractCancelManualSignParam cancelManualSignParam) throws HeronContractGatewayThriftException, TException {
        try {
            econtractCancelWaitSignProcessor.cancelManualSign(cancelManualSignParam);
        } catch (GatewayBaseException e) {
            log.info("econtractCancelManualSign 电子合同取消待签约异常, 参数 param: {}, e: ", JacksonUtil.writeAsJsonStr(cancelManualSignParam), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }
    

    @Override
    @AroundLog(description = "签约确认")
    public void confirmSign(HeronContractConfirmSignParam confirmSignParam) throws HeronContractGatewayThriftException, TException {
        try {
            confirmSignProcessor.confirmSign(confirmSignParam);
        } catch (GatewayBaseException e) {
            log.info("confirmSign 签约确认异常 参数 param: {}, e: ", JacksonUtil.writeAsJsonStr(confirmSignParam), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog(description = "终止指定session的流程")
    public void terminate(HeronContractFlowTerminateParam terminateParam) throws HeronContractGatewayThriftException {
        try {
            flowTerminateProcessor.terminate(terminateParam);
        } catch (GatewayBaseException e) {
            log.info("terminate 终止流程异常, 参数 param: {}, e: ", JacksonUtil.writeAsJsonStr(terminateParam), e);
            throw new HeronContractGatewayThriftException(-1, e);
        }
    }


    @Override
    @AroundLog(description = "终止所有结构[闪购、老结构、新结构]的流程")
    public void terminateAllStruct(AllStructFlowTerminateParam terminateParam) throws HeronContractGatewayThriftException {
        try {
            allStructFlowTerminateFlowProcessor.terminate(terminateParam);
        } catch (GatewayBaseException e) {
            log.info("terminate 终止流程异常, 参数 param: {}, e: ", JacksonUtil.writeAsJsonStr(terminateParam), e);
            throw new HeronContractGatewayThriftException(-1, e);
        }
    }

    @Override
    @AroundLog(description = "批量终止所有结构[闪购、老结构、新结构]的流程")
    public void batchTerminateAllStruct(String wmPoiIds) throws HeronContractGatewayThriftException, TException {
        List<String> poiList = Splitter.on(",").trimResults().splitToList(wmPoiIds);
        for (String wmPoiId : poiList) {
            try {
                AllStructFlowTerminateParam param = new AllStructFlowTerminateParam();
                param.setWmPoiId(Long.parseLong(wmPoiId));
                param.setOperator(HeronContractOperator.builder().opId(-1L).opName("配送合同").opSource(OperateSourceEnum.LOGISTICS_BACKDOOR).build());
                param.setTerminateReason("");
                param.setSessionCategory(SessionCategoryEnum.CORE);
                log.info("batchTerminateAllStruct 终止流程, param: {}", JacksonUtil.writeAsJsonStr(param));
                allStructFlowTerminateFlowProcessor.terminate(param);
            } catch (Exception e) {
                log.info("batchTerminateAllStruct 终止流程异常, 参数 wmPoiId: {}, e: ", JacksonUtil.writeAsJsonStr(wmPoiId), e);
            }
        }
    }

    @Override
    @AroundLog(description = "by sessionCategory重发短信")
    public ResultDTO reSendWmPoiAllFeeConfirmInfo(SinglePoiResendMsgRequestDTO resendParam) throws HeronContractGatewayThriftException, TException {
        try {
            return platContractFlowThriftServiceAdapter.reSendWmPoiAllFeeConfirmInfo(resendParam);
        } catch (GatewayBaseException e) {
            log.info("reSendWmPoiAllFeeConfirmInfo 重发短信异常, 参数 param: {}, e: ", JacksonUtil.writeAsJsonStr(resendParam), e);
            throw new HeronContractGatewayThriftException(-1, e);
        }
    }

    @Override
    @AroundLog(description = "原链路生效同步双写链路")
    public GatewayResult sgTransferSyncDrPoiEffect(SgTransferEffectDrParam param) throws HeronContractGatewayThriftException, TException {
        try {
            return drPoiEffectSyncProcessor.syncDrPoiEffect(param);
        } catch (GatewayBaseException e) {
            log.info("syncDrPoiEffect 原链路生效同步双写链路异常, 参数 param: {}, e: ", JacksonUtil.writeAsJsonStr(param), e);
            throw new HeronContractGatewayThriftException(-1, e);
        }
    }

    /**
     * 满足基本灰度条件，直接返回使用新接口
     * 不满足基本灰度条件，如存在拆分门店，也要返回使用新接口
     */
    @Override
    @AroundLog
    public Boolean getGatewaySignOperateGrayResult(HeronContractSignGrayParam heronContractSignGrayParam) throws HeronContractGatewayThriftException, TException {
        return true;
    }

}
