package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.service;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.config.MccConfig;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.aspect.AroundLog;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.aspect.PhfBusinessService;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.LogisticsExtTagsHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.phf.PhfBatchModifyVariableFeeEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.phf.PhfBdBindSchoolStationEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.phf.PhfContractInvalidEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.phf.PhfMigrateToDrDataEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.phf.PhfMigrateToSplitDataEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.phf.PhfMigrateRollbackProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractMigrateStageEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.exception.HeronContractGatewayThriftException;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfBatchModifyVariableFeeParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfContractDrMigrateCleanParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfContractInvalidParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfPoiBindSchoolStationParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.SinglePoiResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.service.PhfContractEventProcessGatewayThriftService;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.SessionCategoryExtTagsBo;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfContractTechAggreDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/9
 */
@Service
@Slf4j
@PhfBusinessService
public class PhfContractEventProcessGatewayThriftServiceImpl implements PhfContractEventProcessGatewayThriftService {

    @Resource
    private PhfBdBindSchoolStationEntry phfBdBindSchoolStationEntry;

    @Resource
    private PhfContractInvalidEntry phfContractInvalidEntry;

    @Resource
    private PhfBatchModifyVariableFeeEntry phfBatchModifyVariableFeeEntry;

    @Resource
    private PhfMigrateToDrDataEntry phfMigrateToDrDataEntry;

    @Resource
    private PhfMigrateToSplitDataEntry phfMigrateToSplitDataEntry;

    @Resource
    private PhfMigrateRollbackProcessor phfMigrateRollbackProcessor;

    @Resource
    private LogisticsExtTagsHelper logisticsExtTagsHelper;

    @Resource
    private PlatContractQueryThriftServiceAdapter platContractQueryThriftServiceAdapter;


    @Override
    @AroundLog(description = "解除拼好饭合同")
    public void invalidContract(PhfContractInvalidParam param) throws HeronContractGatewayThriftException {
        try {
            if (MccConfig.getInvalidPhfMigrateStageCheck()) {
                SessionCategoryExtTagsBo extTagsBo = logisticsExtTagsHelper.getTagsBySessionCategory(param.getWmPoiId(), SessionCategoryEnum.PHF);
                if (extTagsBo != null && ContractMigrateStageEnum.BOTH_RUN.equals(extTagsBo.getMigrateStage())) {
                    throw new HeronContractGatewayThriftException(-1, "双写链路phf门店需调用invalidDrContract接口");
                }
            }
            phfContractInvalidEntry.process(param);
        } catch (GatewayBaseException e) {
            log.error("拼好饭 invalidContract 异常 param={}", JacksonUtil.writeAsJsonStr(param), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e.getMessage());
        }
    }

    @Override
    @AroundLog(description = "双写阶段解除拼好饭合同")
    public void invalidDrContract(PhfContractInvalidParam param) throws HeronContractGatewayThriftException {
        try {
            if (MccConfig.getInvalidPhfMigrateStageCheck()) {
                SessionCategoryExtTagsBo extTagsBo = logisticsExtTagsHelper.getTagsBySessionCategory(param.getWmPoiId(), SessionCategoryEnum.PHF);
                if (extTagsBo != null && ContractMigrateStageEnum.MIGRATE_NEW.equals(extTagsBo.getMigrateStage())) {
                    throw new HeronContractGatewayThriftException(-1, "拆分链路phf门店需调用invalidContract接口");
                }
                PhfContractTechAggreDataDTO phfContractTechAggreDataDTO = platContractQueryThriftServiceAdapter.queryPhfEffectiveContract(param.getWmPoiId());
                log.info("invalidDrContract wmPoiId={} phfContractTechAggreDataDTO={}", param.getWmPoiId(), JacksonUtil.writeAsJsonStr(phfContractTechAggreDataDTO));
                if (phfContractTechAggreDataDTO != null
                        && phfContractTechAggreDataDTO.getBaseInfoDTO() != null
                        && param.getSessionId() != null
                        && phfContractTechAggreDataDTO.getBaseInfoDTO().getSessionId().equals(param.getSessionId())) {
                    throw new HeronContractGatewayThriftException(-1, "拼好饭原sessionId和一体化sessionId不一致");
                }
            }
            phfContractInvalidEntry.process(param);
        } catch (GatewayBaseException e) {
            log.error("拼好饭 invalidContract 异常 param={}", JacksonUtil.writeAsJsonStr(param), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e.getMessage());
        }
    }

    @Override
    @AroundLog(description = "PHF绑定学校站点")
    public void bindSchoolStation(PhfPoiBindSchoolStationParam param) throws HeronContractGatewayThriftException {
        try {
            phfBdBindSchoolStationEntry.process(param);
        } catch (GatewayBaseException e) {
            log.error("拼好饭 bindSchoolStation 异常 param={}", JacksonUtil.writeAsJsonStr(param), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e.getMessage());
        }
    }

    @Override
    @AroundLog(description = "批量修改可变费率")
    public List<SinglePoiResult> batchModifyVariableFee(PhfBatchModifyVariableFeeParam param) throws HeronContractGatewayThriftException, TException {
        try {
           return phfBatchModifyVariableFeeEntry.process(param);
        } catch (GatewayBaseException e) {
            log.error("拼好饭 batchModifyVariableFee 异常 param={}", JacksonUtil.writeAsJsonStr(param), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e.getMessage());
        } catch (Throwable e) {
            log.error("拼好饭 batchModifyVariableFee 异常 param={}", JacksonUtil.writeAsJsonStr(param), e);
            throw new TException(e);
        }
    }

    @Override
    @AroundLog(description = "双写阶段迁移数据")
    public void migrateToDrData(List<PhfContractDrMigrateCleanParam> poiParamList) throws HeronContractGatewayThriftException, TException {
        try {
            phfMigrateToDrDataEntry.process(poiParamList);
        } catch (GatewayBaseException e) {
            log.error("拼好饭 migrateToDrData 异常 param={}", JacksonUtil.writeAsJsonStr(poiParamList), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e.getMessage());
        }
    }

    @Override
    @AroundLog(description = "迁移到拆分数据")
    public void migrateToSplit(Long wmPoiId) throws TException {
        try {
            phfMigrateToSplitDataEntry.process(wmPoiId);
        } catch (Throwable e) {
            log.error("拼好饭 migrateToSplit 异常 param={}", wmPoiId, e);
            throw new TException(e);
        }
    }

    @Override
    @AroundLog(description = "拼好饭迁移回滚")
    public void migrateRollback(Long wmPoiId) throws TException {
        try {
            phfMigrateRollbackProcessor.migrateRollback(wmPoiId);
        } catch (Throwable e) {
            log.error("拼好饭 migrateRollback 异常 param={}", wmPoiId, e);
            throw new TException(e);
        }
    }

}
