package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.service;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.bo.SessionContextBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SessionSceneGroupEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.aspect.AroundLog;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.gray.GatewaySaveGrayDecider;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.gray.PerfSplitSaveGrayDecider;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.leaf.LeafIdGenerator;
import com.sankuai.meituan.waimai.heron.contract.gateway.query.model.PoiLogisticsProcessingInfo;
import com.sankuai.meituan.waimai.heron.contract.gateway.query.service.EffectiveAndFlowStructJudgeAbilityService;
import com.sankuai.meituan.waimai.heron.contract.gateway.query.service.EffectiveInfoQueryAbilityService;
import com.sankuai.meituan.waimai.heron.contract.gateway.query.service.FlowInfoQueryAbilityService;
import com.sankuai.meituan.waimai.heron.contract.gateway.query.service.LogisticsDataStructQueryService;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.exception.HeronContractGatewayThriftException;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.query.LogisticsAggreInfoQueryParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.query.LogisticsExtTagsQueryParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.query.SessionQueryParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractSaveGrayParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query.*;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.save.ContractSaveGrayResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.service.HeronContractQueryGatewayThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/5/22
 */
@Service
@Slf4j
public class HeronContractQueryGatewayThriftServiceImpl implements HeronContractQueryGatewayThriftService {

    @Resource
    private EffectiveInfoQueryAbilityService effectiveInfoQueryAbilityService;

    @Resource
    private FlowInfoQueryAbilityService flowInfoQueryAbilityService;

    @Resource
    private PerfSplitSaveGrayDecider perfSplitSaveGrayDecider;

    @Resource
    private GatewaySaveGrayDecider gatewaySaveGrayDecider;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private EffectiveAndFlowStructJudgeAbilityService effectiveAndFlowStructJudgeAbilityService;

    @Resource
    private LogisticsDataStructQueryService logisticsDataStructQueryService;

    @Override
    @AroundLog
    public HeronSessionResult getLastFlowSessionByWmPoiId(SessionQueryParam sessionQueryParam) {
        HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getLastByWmPoiIdAndGroupRT(sessionQueryParam.getWmPoiId(), SessionSceneGroupEnum.FLOW, Optional.ofNullable(sessionQueryParam.getSessionCategory()).orElse(SessionCategoryEnum.CORE));
        if (Objects.isNull(session)) {
            log.info("HeronContractQueryGatewayThriftServiceImpl getLastFlowSessionByWmPoiId 未查到session");
            return null;
        }
        HeronSessionResult heronSessionResult = convertToSessionResult(session);
        log.info("HeronContractQueryGatewayThriftServiceImpl getLastFlowSessionByWmPoiId 结果 result: {}", JacksonUtil.writeAsJsonStr(heronSessionResult));
        return heronSessionResult;
    }

    @Override
    @AroundLog
    public HeronSessionResult getSessionById(long sessionId) {
        HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getByLeafId(sessionId);
        if (Objects.isNull(session)) {
            log.info("HeronContractQueryGatewayThriftServiceImpl getLastFlowSessionByWmPoiId 未查到session");
            return null;
        }
        return convertToSessionResult(session);
    }

    @Override
    public HeronSessionResult getSessionByIdRT(long sessionId) throws HeronContractGatewayThriftException, TException {
        HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getByLeafIdRT(sessionId);
        if (Objects.isNull(session)) {
            log.info("HeronContractQueryGatewayThriftServiceImpl getSessionByIdRT 未查到session");
            return null;
        }
        return convertToSessionResult(session);
    }

    @Override
    @AroundLog
    public EffectiveAggreInfoResult queryEffectiveAggreInfo(long wmPoiId) throws HeronContractGatewayThriftException {
        try {
            return effectiveInfoQueryAbilityService.queryEffectiveInfo(wmPoiId, SessionCategoryEnum.CORE);
        } catch (GatewayBaseException e) {
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog(needResponseLog = false)
    public EffectiveAggreInfoResult queryEffectiveAggreInfoWithSessionCategory(LogisticsAggreInfoQueryParam param) throws HeronContractGatewayThriftException, TException {
        try {
            return effectiveInfoQueryAbilityService.queryEffectiveInfo(param.getWmPoiId(), param.getSessionCategory());
        } catch (GatewayBaseException e) {
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog
    public FlowAggreInfoResult queryFlowAggreInfo(long wmPoiId) throws HeronContractGatewayThriftException {
        try {
            return flowInfoQueryAbilityService.getFlowAggreInfo(wmPoiId, SessionCategoryEnum.CORE);
        } catch (GatewayBaseException e) {
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog
    public FlowAggreInfoResult queryFlowAggreInfoWithSessionCategory(LogisticsAggreInfoQueryParam param) throws HeronContractGatewayThriftException, TException {
        try {
            return flowInfoQueryAbilityService.getFlowAggreInfo(param.getWmPoiId(), param.getSessionCategory());
        } catch (GatewayBaseException e) {
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog(needResponseLog = false)
    public EffectiveAndFlowStructJudgeResult judgeStruct(long wmPoiId) throws HeronContractGatewayThriftException {
        try {
            return effectiveAndFlowStructJudgeAbilityService.judgeStructHandleSgTransferDr(wmPoiId);
        } catch (GatewayBaseException e) {
            log.error("judgeStruct error wmPoiId: {}", wmPoiId, e);
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog
    public ContractSaveGrayResult judgeGrayForWm(HeronContractSaveGrayParam grayParam) throws HeronContractGatewayThriftException {
        try {
            return perfSplitSaveGrayDecider.judgeGrayForWm(grayParam);
        } catch (GatewayBaseException e) {
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog
    @Deprecated
    public ContractSaveGrayResult judgeGrayForSgAndYy(HeronContractSaveGrayParam grayParam) throws HeronContractGatewayThriftException {
        try {
            return perfSplitSaveGrayDecider.judgeGrayForSgAndYy(grayParam);
        } catch (GatewayBaseException e) {
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    public Long generateSessionId() throws HeronContractGatewayThriftException, TException {
        return LeafIdGenerator.generateSessionId();
    }

    @Override
    @AroundLog
    public Boolean judgeGrayForGatewaySettle(HeronContractSaveGrayParam grayParam) throws HeronContractGatewayThriftException, TException {
        try {
            log.info("judgeGrayForGatewaySettle param: {}", JacksonUtil.writeAsJsonStr(grayParam));
            Boolean grayResult = gatewaySaveGrayDecider.settleGray(grayParam);
            log.info("judgeGrayForGatewaySettle param: {}, result: {}", JacksonUtil.writeAsJsonStr(grayParam), JacksonUtil.writeAsJsonStr(grayResult));
            return grayResult;
        } catch (GatewayBaseException e) {
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog
    public Boolean hasProcessingInfo(SessionQueryParam queryParam) throws HeronContractGatewayThriftException, TException {
        try {
            PoiLogisticsProcessingInfo processingPoiDataStruct = logisticsDataStructQueryService.getProcessingPoiDataStruct(queryParam.getWmPoiId(), Optional.ofNullable(queryParam.getSessionCategory()).orElse(SessionCategoryEnum.CORE));
            return Objects.nonNull(processingPoiDataStruct) && BooleanUtils.isTrue(processingPoiDataStruct.getProcessing());
        } catch (GatewayAdapterException e) {
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }

    @Override
    @AroundLog
    public LogisticsExtTagsResult queryPoiEffectExtTags(LogisticsExtTagsQueryParam param) throws HeronContractGatewayThriftException, TException {
        return effectiveInfoQueryAbilityService.queryPoiEffectExtTags(param);
    }

    private HeronSessionResult convertToSessionResult(HeronContractGatewaySession session) {
        SessionContextBo sessionContextBo = session.getContext();
        HeronSessionResult heronSessionResult = new HeronSessionResult();
        heronSessionResult.setWmPoiId(session.getWmPoiId());
        heronSessionResult.setSessionId(session.getSessionLeafId());
        heronSessionResult.setAreaSplit(BooleanUtils.isTrue(sessionContextBo.getAreaSplit()));
        heronSessionResult.setPerfSplit(BooleanUtils.isTrue(sessionContextBo.getPerfSplit()));
        heronSessionResult.setPerfDR(BooleanUtils.isTrue(sessionContextBo.getPerfDR()));
        heronSessionResult.setTechDR(BooleanUtils.isTrue(sessionContextBo.getTechDR()));
        heronSessionResult.setAreaDR(BooleanUtils.isTrue(sessionContextBo.getAreaDR()));
        heronSessionResult.setHasPerf(BooleanUtils.isNotFalse(sessionContextBo.getHasPerf()));
        heronSessionResult.setHasArea(BooleanUtils.isNotFalse(sessionContextBo.getHasArea()));
        heronSessionResult.setBatchAuditId(sessionContextBo.getContractVersionId());
        heronSessionResult.setSessionCategory(session.getSessionCategory().name());

        heronSessionResult.setContext(JacksonUtil.writeAsJsonStr(session.getContext()));
        heronSessionResult.setStatus(session.getStatus().name());
        //场景迭代概率较高，避免备机上线后，prod环境无法识别新场景导致异常，返回空
        heronSessionResult.setSessionScene(Optional.ofNullable(session.getSessionScene()).map(ContractCreateAndUpdateSceneEnum::name).orElse(null));
        heronSessionResult.setMigrateStage(sessionContextBo.getMigrateStage());
        return heronSessionResult;
    }
}
