package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.service;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.aspect.AroundLog;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.entry.impl.LogisticsContractMigrateSaveSessionEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.entry.impl.LogisticsContractMigrateTempSaveSessionEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.exception.HeronContractGatewayThriftException;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractMigrateSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.save.ContractMigrateSaveResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.service.LogisticsContractSaveMigrateGatewayThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/5/22
 */
@Service
@Slf4j
public class LogisticsContractSaveMigrateGatewayThriftServiceImpl implements LogisticsContractSaveMigrateGatewayThriftService {

    @Resource
    private LogisticsContractMigrateSaveSessionEntry logisticsContractMigrateSaveSessionEntry;

    @Resource
    private LogisticsContractMigrateTempSaveSessionEntry logisticsContractMigrateTempSaveSessionEntry;

    @Override
    @AroundLog(description = "保存接口迁移")
    public ContractMigrateSaveResult migrateSave(HeronContractMigrateSaveParam saveParam) throws HeronContractGatewayThriftException, TException {
        try {
            return logisticsContractMigrateSaveSessionEntry.saveLogistics(saveParam);
        } catch (Exception e) {
            log.error("配送信息迁移保存接口处理异常 save参数 param: {}", JacksonUtil.writeAsJsonStr(saveParam), e);
            return ContractMigrateSaveResult.builder()
                    .success(false)
                    .contractVersionId(-1L)
                    .code(1)
                    .message(e.getMessage())
                    .build();
        }
    }

    @Override
    @AroundLog(description = "暂存接口迁移")
    public ContractMigrateSaveResult migrateTempSave(HeronContractMigrateSaveParam saveParam) throws HeronContractGatewayThriftException, TException {
        try {
            return logisticsContractMigrateTempSaveSessionEntry.saveLogistics(saveParam);
        } catch (GatewayBaseException e) {
            log.error("配送信息迁移暂存接口处理异常 save参数 param: {}", JacksonUtil.writeAsJsonStr(saveParam), e);
            return ContractMigrateSaveResult.builder()
                    .success(false)
                    .contractVersionId(-1L)
                    .code(-1).message(e.getMessage())
                    .build();
        }
    }
}
