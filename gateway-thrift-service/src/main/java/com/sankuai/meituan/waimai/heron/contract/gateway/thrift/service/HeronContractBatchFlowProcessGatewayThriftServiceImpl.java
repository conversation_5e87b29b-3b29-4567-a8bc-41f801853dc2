package com.sankuai.meituan.waimai.heron.contract.gateway.thrift.service;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.aspect.AroundLog;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.BatchFlowCompleteProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.BatchFlowTerminateProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.exception.HeronContractGatewayThriftException;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.batch.HeronContractBatchFlowCompleteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.batch.HeronContractBatchFlowTerminateParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.service.HeronContractBatchFlowProcessGatewayThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/5/22
 */
@Service
@Slf4j
public class HeronContractBatchFlowProcessGatewayThriftServiceImpl implements HeronContractBatchFlowProcessGatewayThriftService {

    @Resource
    private BatchFlowCompleteProcessor batchFlowCompleteProcessor;

    @Resource
    private BatchFlowTerminateProcessor batchFlowTerminateProcessor;

    @Override
    @AroundLog(description = "批量任务完成")
    public void batchComplete(HeronContractBatchFlowCompleteParam completeParam) throws HeronContractGatewayThriftException, TException {
      try {
          batchFlowCompleteProcessor.batchComplete(completeParam);
      } catch (GatewayBaseException e) {
        log.error("批量任务完成异常 batchComplete error completeParam:{}", JacksonUtil.writeAsJsonStr(completeParam), e);
        throw new HeronContractGatewayThriftException(e.getCode(), e);
      }
    }

    @Override
    @AroundLog(description = "批量任务终止")
    public void batchTerminate(HeronContractBatchFlowTerminateParam terminateParam) throws HeronContractGatewayThriftException, TException {
        try {
            batchFlowTerminateProcessor.batchTerminate(terminateParam);
        } catch (GatewayBaseException e) {
            log.error("批量任务终止异常 batchTerminate error terminateParam: {}", JacksonUtil.writeAsJsonStr(terminateParam), e);
            throw new HeronContractGatewayThriftException(e.getCode(), e);
        }
    }
}
