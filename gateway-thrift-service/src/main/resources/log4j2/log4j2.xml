<?xml version="1.0" encoding="UTF-8"?>
<configuration status="debug">
    <appenders>


        <XMDFile name="gatewayLog" fileName="com.sankuai.heron.contract.gateway.log" xmdFilePath="/var/sankuai/logs"
                 sizeBasedTriggeringSize="20GB" includeLocation="true" rolloverMax="10">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p %t %c(%F:%L) \#\# %XMDT %msg%n%ex"/>
        </XMDFile>

        <!--ERROR日志、WARN日志单独输出到一个文件-->
        <XMDFile name="errorLog" fileName="error.log">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>
        <XMDFile name="warnLog" fileName="warn.log">
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>

        <!--日志远程上报-->
        <Scribe name="ScribeAppender">
            <!-- 在指定日志名方面，scribeCategory 和 appkey 两者至少存在一种，且 scribeCategory 高于 appkey。-->
            <Property name="scribeCategory">com.sankuai.heron.contract.gateway</Property>
            <LcLayout/>
        </Scribe>
        <Async name="ScribeAsyncAppender" blocking="false">
            <AppenderRef ref="ScribeAppender"/>
        </Async>


        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{DEFAULT} [%t] %-5p (%C{1}:%L) - %m%n%ex"/>
        </Console>


    </appenders>

    <loggers>
        <logger name="com.meituan.waimai" level="info"/>
        <logger name="org.springframework" level="info"/>
        <Logger name="com.meituan.kafka" level="error" additivity="false"/>
        <Logger name="com.meituan.mafka" level="error" additivity="false"/>
        <root level="info">
<!--            <appender-ref ref="Console"/>-->
            <appender-ref ref="gatewayLog"/>
            <appender-ref ref="warnLog"/>
            <appender-ref ref="errorLog"/>
            <appender-ref ref="ScribeAsyncAppender"/>
        </root>
    </loggers>
</configuration>
