<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.waimai.heron.contract.gateway.basic.mapper.WmPoiLogisticsMissionMapper">


    <resultMap type="com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.WmPoiLogisticsMissionPo" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="missionId" column="mission_id"/>
        <result property="wmPoiId" column="wm_poi_id"/>
        <result property="batchAuditId" column="batch_audit_id"/>
        <result property="missionType" column="mission_type"/>
        <result property="source" column="source"/>
        <result property="status" column="status"/>
        <result property="context" column="context"/>
        <result property="opId" column="op_id"/>
        <result property="opName" column="op_name"/>
        <result property="version" column="version"/>
        <result property="ctime" column="ctime"/>
        <result property="utime" column="utime"/>
        <result property="valid" column="valid"/>
        <result property="appointment" column="appointment"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,wm_poi_id,mission_id,batch_audit_id,mission_type,source,status,context,op_id,op_name,ctime,utime,valid,version,appointment
    </sql>


    <select id="getProcessingCustomerChangeMission" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from wm_poi_logistics_mission
        where wm_poi_id=#{wmPoiId}
        and valid=1
        and mission_type = 'CUSTOMER_CHANGE_ONLINE'
        and status = 10
        and session_category = 'CORE'
    </select>


    <select id="batchGetProcessingCustomerChangeMission" resultMap="BaseResultMap">

        select <include refid="Base_Column_List"/> from wm_poi_logistics_mission
        where wm_poi_id in
        <foreach collection="wmPoiIdList" open="(" separator="," close=")" item="wmPoiId" index="index">
            #{wmPoiId}
        </foreach>
        and valid=1
        and mission_type = 'CUSTOMER_CHANGE_ONLINE'
        and status = 10
        and session_category = 'CORE'
    </select>

    <select id="getLastLogisticsMission" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from wm_poi_logistics_mission
        where wm_poi_id=#{wmPoiId}
        and valid=1
        and session_category = 'CORE'
        order by id desc
        limit 1
    </select>

</mapper>