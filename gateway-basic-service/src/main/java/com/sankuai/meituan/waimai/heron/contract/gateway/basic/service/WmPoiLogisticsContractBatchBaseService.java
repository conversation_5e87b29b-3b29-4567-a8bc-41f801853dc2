package com.sankuai.meituan.waimai.heron.contract.gateway.basic.service;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.mapper.WmPoiLogisticsContractBatchMapper;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.WmPoiLogisticsContractBatchPo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2024-09-12 11:45
 */
@Service
public class WmPoiLogisticsContractBatchBaseService {

    @Resource
    private WmPoiLogisticsContractBatchMapper wmPoiLogisticsContractBatchMapper;

    public List<WmPoiLogisticsContractBatchPo> getEffectiveByWmPoiId(Long wmPoiId) {
        return wmPoiLogisticsContractBatchMapper.getEffectiveByWmPoiId(wmPoiId);
    }
}