package com.sankuai.meituan.waimai.heron.contract.gateway.basic.service;


import com.sankuai.meituan.waimai.heron.contract.gateway.basic.mapper.WmPoiLogisticsExtMapper;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.WmPoiLogisticsExtPo;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.zebra.ForceMaster;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class WmPoiLogisticsExtBaseService {

    @Resource
    private WmPoiLogisticsExtMapper wmPoiLogisticsExtMapper;


    public int insert(WmPoiLogisticsExtPo extPo) {
        return wmPoiLogisticsExtMapper.insert(extPo);
    }

    public WmPoiLogisticsExtPo getByWmPoiId(Long wmPoiId) {
        return wmPoiLogisticsExtMapper.getByWmPoiId(wmPoiId);
    }

    @ForceMaster
    public WmPoiLogisticsExtPo getByWmPoiIdRT(Long wmPoiId) {
        return this.getByWmPoiId(wmPoiId);
    }


    public int updateTags(Long wmPoiId, String tags) {
        return wmPoiLogisticsExtMapper.updateTags(wmPoiId, tags);
    }


    public List<WmPoiLogisticsExtPo> batchGetByWmPoiIds(List<Long> wmPoiIds) {
        return wmPoiLogisticsExtMapper.batchGetByWmPoiIds(wmPoiIds);
    }
}
