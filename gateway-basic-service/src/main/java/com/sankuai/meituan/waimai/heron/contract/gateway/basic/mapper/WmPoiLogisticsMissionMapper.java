package com.sankuai.meituan.waimai.heron.contract.gateway.basic.mapper;

import com.dianping.zebra.dao.datasource.ZebraRouting;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.WmPoiLogisticsMissionPo;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.DatabaseNameConstant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ZebraRouting(DatabaseNameConstant.DATA_SOURCE_POI)
public interface WmPoiLogisticsMissionMapper {


    WmPoiLogisticsMissionPo getProcessingCustomerChangeMission(@Param("wmPoiId") long wmPoiId);

    List<WmPoiLogisticsMissionPo> batchGetProcessingCustomerChangeMission(@Param("wmPoiIdList") List<Long> wmPoiIdList);

    WmPoiLogisticsMissionPo getLastLogisticsMission(@Param("wmPoiId") long wmPoiId);
}
