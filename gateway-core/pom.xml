<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.sankuai.meituan.waimai.heron</groupId>
        <artifactId>waimai_e_heron_contract_gateway</artifactId>
        <version>1.0.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <description>基础核心模块</description>
    <artifactId>gateway-core</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.meituan.waimai.heron</groupId>
            <artifactId>gateway-basic-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimai.heron</groupId>
            <artifactId>gateway-adapter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimai.heron</groupId>
            <artifactId>gateway-query</artifactId>
        </dependency>
    </dependencies>

</project>