package com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import lombok.Data;
import org.apache.commons.lang.BooleanUtils;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-05-05 15:46
 */
@Data
public class FlowExecResult<T> {

    private Boolean success;

    private String message;

    private SubDomainEnum failCauseDomain;

    private DispatchResult dispatchResult;

    private T data;

    private Boolean skipProcess;


    public static FlowExecResult failInGateway(String message) {
        FlowExecResult result = new FlowExecResult();
        result.setSuccess(false);
        result.setFailCauseDomain(SubDomainEnum.GATEWAY);
        result.setMessage(message);
        return result;
    }

    public static FlowExecResult skipProcess(String message) {
        FlowExecResult result = new FlowExecResult();
        result.setSuccess(false);
        result.setSkipProcess(true);
        result.setFailCauseDomain(SubDomainEnum.GATEWAY);
        result.setMessage(message);
        return result;
    }


    public static FlowExecResult failBySubDomain(SubDomainEnum subDomainEnum, String message) {
        FlowExecResult result = new FlowExecResult();
        result.setSuccess(false);
        result.setFailCauseDomain(subDomainEnum);
        result.setMessage(message);
        return result;
    }

    public static <T> FlowExecResult<T> success() {
        FlowExecResult<T> result = new FlowExecResult<T>();
        result.setSuccess(true);
        return result;
    }

    /**
     * 由分发结果构建
     * @param flowDispatchResult
     * @return
     */
    public static FlowExecResult build(DispatchResult flowDispatchResult) {
        FlowExecResult result = new FlowExecResult();
        result.setSuccess(flowDispatchResult.getSuccess());
        result.setMessage(flowDispatchResult.getMessage());
        result.setDispatchResult(flowDispatchResult);
        result.setFailCauseDomain(flowDispatchResult.getSendFailDomain());
        return result;
    }

    /**
     * 由发送结果构建
     * @param sendResult
     * @param sendDomain
     * @return
     */
    public static FlowExecResult build(SendResult sendResult, SubDomainEnum sendDomain) {
        FlowExecResult result = new FlowExecResult();
        result.setSuccess(sendResult.getSuccess());
        result.setMessage(sendResult.getMessage());
        if (sendResult.isFail()) {
            result.setFailCauseDomain(sendDomain);
        }
        return result;
    }

    public boolean isFail() {
        return !success && BooleanUtils.isNotTrue(skipProcess);
    }
}
