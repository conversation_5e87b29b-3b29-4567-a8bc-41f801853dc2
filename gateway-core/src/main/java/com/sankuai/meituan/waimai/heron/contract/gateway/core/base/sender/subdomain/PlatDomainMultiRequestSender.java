package com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestErrorTypeEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.BaseMultiRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.ResultDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-18 11:45
 */
@Slf4j
public abstract class PlatDomainMultiRequestSender<O, T> extends BaseMultiRequestSender<O, T> {

    @Override
    public SubDomainEnum getSubDomain() {
        return SubDomainEnum.PLAT;
    }


    protected SendResult convertResult(ResultDTO resultDTO) {
        if (Objects.nonNull(resultDTO) && resultDTO.isSuccess()) {
            return SendResult.success();
        } else {
            if (Objects.isNull(resultDTO)) {
                return SendResult.fail(-1, "平台返回值为null", RequestErrorTypeEnum.DOMAIN_TEXCEPTION);
            }
            return SendResult.fail(resultDTO.getCode(), resultDTO.getMessage(), RequestErrorTypeEnum.RETURN_ERROR_CODE);
        }
    }

}