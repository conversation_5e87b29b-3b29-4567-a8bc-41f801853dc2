package com.sankuai.meituan.waimai.heron.contract.gateway.core.api;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.MultiSendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.MultiSendResult;

public interface MultiRequestSender<O> {


    MultiSendResult sendRequest(MultiSendParam<O> sendParam);


    /**
     * 获取对应sender的场景
     */
    RequestSceneEnum getRequestScene();

    /**
     * 获取对应的子领域
     */
    SubDomainEnum getSubDomain();
}
