package com.sankuai.meituan.waimai.heron.contract.gateway.core.utils;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.BusinessIdentity;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayRuntimeException;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.PerfBusinessIdentityHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2024-03-29 10:24
 */
public class SessionCategoryHandler {


    public static <T, R> Map<T, R> applyWithSessionCategory(List<T> param, Function<T, R> function) {
        Map<T, R> resultMap = new HashMap<>();
        for (T t : param) {
            try {
                resultMap.put(t, function.apply(t));
            } catch (Exception e) {
                throw new GatewayRuntimeException(e);
            }
        }
        return resultMap;
    }

    public static BusinessIdentity convertFromSessionCategory(SessionCategoryEnum sessionCategory, boolean bothRun) {
        return PerfBusinessIdentityHelper.convertFromSessionCategory(sessionCategory, bothRun);
    }



}