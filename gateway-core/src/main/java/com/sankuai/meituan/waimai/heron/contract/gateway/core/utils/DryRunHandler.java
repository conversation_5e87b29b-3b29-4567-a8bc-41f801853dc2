package com.sankuai.meituan.waimai.heron.contract.gateway.core.utils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/26
 */
public class DryRunHandler {

    private final static ThreadLocal<Boolean> LOCAL_CONTEXT = new InheritableThreadLocal<>();


    public static void setDryRun(Boolean dryRun) {
        LOCAL_CONTEXT.set(dryRun);
    }

    public static boolean isDryRun() {
        return Optional.ofNullable(LOCAL_CONTEXT.get()).orElse(false);
    }

    public static boolean isNotDryRun() {
        return !isDryRun();
    }

    public static void clear() {
        LOCAL_CONTEXT.remove();
    }
}
