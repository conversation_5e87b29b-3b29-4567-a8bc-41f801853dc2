package com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-17 17:31
 */
@Data
public class MultiPoiFlowRouteParam extends RouteParam {

    private Map<Long, SinglePoiFlowRouteParam> poiRouteMap;

    private Map<Long, HeronContractGatewaySession> poiSessionMap;

    private List<Long> wmPoiIdList;

}