package com.sankuai.meituan.waimai.heron.contract.gateway.query.helper;

import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.logistics.contract.client.constants.LogisticsContractMissionStatusEnum;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.WmPoiFeeAuditStatus;
import com.sankuai.shangou.merchant.logistics.thrift.dto.router.SignRouterResultDTO;
import org.apache.commons.lang3.BooleanUtils;

import javax.annotation.Nullable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/2/2
 */
public class JudgeSupportCancelSignHelper {

    public static boolean sessionSupportCancelSign(ContractSessionStatusEnum statusEnum) {
        return Objects.equals(ContractSessionStatusEnum.SIGNING, statusEnum);
    }

    public static boolean sessionSupportCancelManualSign(ContractSessionStatusEnum statusEnum) {
        return Objects.equals(ContractSessionStatusEnum.WAIT_MANUAL_SIGN, statusEnum) || Objects.equals(ContractSessionStatusEnum.APPLY_SIGN, statusEnum);
    }

    public static boolean sgSupportCancelSign(SignRouterResultDTO signRouterResultDTO) {
        return signRouterResultDTO != null && (BooleanUtils.isTrue(signRouterResultDTO.getCanCancelSignInfoByConfirmInfo()) || isPositive(signRouterResultDTO.getConfirmId()));
    }

    /**
     * 闪购待签约中返回的几个字段无法判断，不支持取消签约则支持取消待签约
     * @param signRouterResultDTO
     * @return
     */
    public static boolean sgSupportCancelManualSign(SignRouterResultDTO signRouterResultDTO) {
        return !sgSupportCancelSign(signRouterResultDTO);
//        return signRouterResultDTO != null && (BooleanUtils.isTrue(signRouterResultDTO.getCanCancelSignInfoByManualInfo()) || (!isPositive(signRouterResultDTO.getConfirmId()) && isPositive(signRouterResultDTO.getManualId())));
    }



    public static boolean newContractSupportCancelSign(LogisticsContractMissionStatusEnum statusEnum) {
        return LogisticsContractMissionStatusEnum.SIGNING.equals(statusEnum);
    }

    public static boolean newContractSupportCancelManualSign(LogisticsContractMissionStatusEnum statusEnum) {
        return LogisticsContractMissionStatusEnum.WAIT_APPLY_SIGN.equals(statusEnum);
    }


    public static boolean oldStructSupportCancelSign(int status) {
        return WmPoiFeeAuditStatus.POI_CONFIRM.getValue() == status;
    }

    public static boolean oldStructSupportCancelManualSign(int status) {
        return WmPoiFeeAuditStatus.AUDIT_PASS.getValue() == status;
    }

    private static boolean isPositive(@Nullable Long i) {
        return i != null && i > 0;
    }

}
