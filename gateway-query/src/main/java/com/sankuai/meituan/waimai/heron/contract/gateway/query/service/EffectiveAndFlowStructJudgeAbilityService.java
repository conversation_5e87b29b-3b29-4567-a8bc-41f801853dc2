package com.sankuai.meituan.waimai.heron.contract.gateway.query.service;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.HeronLogisticsThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.SgProcessThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.bo.WmLogisticsExtTagsBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.WmPoiLogisticsExtPo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.WmPoiLogisticsExtBaseService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.query.helper.LogisticsQueryHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ProcessingContractStructEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query.EffectiveAndFlowStructJudgeResult;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.WmPoiLogisticsOfflineDetailDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.constants.LogisticsContractMissionStatusEnum;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.mission.query.WmLogisticsContractMissionDTO;
import com.sankuai.shangou.merchant.logistics.thrift.dto.SgPoiLogisticsOfflineDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/6/26
 */
@Service
@Slf4j
public class EffectiveAndFlowStructJudgeAbilityService {

    @Resource
    private WmPoiLogisticsExtBaseService wmPoiLogisticsExtBaseService;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;


    @Resource
    private SgProcessThriftServiceAdapter sgProcessThriftServiceAdapter;

    @Resource
    private HeronLogisticsThriftServiceAdapter heronLogisticsThriftServiceAdapter;

    @Resource
    private PlatContractQueryThriftServiceAdapter platContractQueryThriftServiceAdapter;


    public EffectiveAndFlowStructJudgeResult judgeStruct(Long wmPoiId) throws GatewayAdapterException {

        WmPoiLogisticsExtPo extPo = wmPoiLogisticsExtBaseService.getByWmPoiId(wmPoiId);
        if (extPo == null) {
            return EffectiveAndFlowStructJudgeResult.builder()
                    .wmPoiId(wmPoiId)
                    .effectiveNeedAggre(false)
                    .latestFlowNeedAggre(false)
                    .flowPerfSplit(false)
                    .validPerfSplit(false)
                    .flowPerfDR(false)
                    .validPerfDR(false)
                    .build();
        }
        WmLogisticsExtTagsBo tags = extPo.getTags();
        boolean validPerfSplit = Objects.nonNull(tags) && BooleanUtils.isTrue(tags.getPerfSplit());
        boolean validPerfDR = Objects.nonNull(tags) && BooleanUtils.isTrue(tags.getPerfDR());
        boolean effectiveNeedAggre = validPerfDR || validPerfSplit;
        HeronContractGatewaySession flowSession = heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(wmPoiId, SessionCategoryEnum.CORE);
        if (flowSession != null && flowSession.getStatus().isProcessing()) {

            return EffectiveAndFlowStructJudgeResult.builder()
                    .wmPoiId(wmPoiId)
                    .effectiveNeedAggre(effectiveNeedAggre)
                    .validPerfSplit(validPerfSplit)
                    .validPerfDR(validPerfDR)
                    .flowPerfSplit(Objects.nonNull(flowSession.getContext()) && BooleanUtils.isTrue(flowSession.getContext().getPerfSplit()))
                    .flowPerfDR(Objects.nonNull(flowSession.getContext()) && BooleanUtils.isTrue(flowSession.getContext().getPerfDR()))
                    .latestFlowNeedAggre(true)
                    .processingContractStruct(ProcessingContractStructEnum.CONTRACT_STRUCT.getCode())
                    .build();
        } else {
            WmLogisticsContractMissionDTO missionDTO = platContractQueryThriftServiceAdapter.getLastContractMissionByWmPoiId(wmPoiId);
            ProcessingContractStructEnum latestProcessingStruct = null;
            boolean latestIsSessionFlow = false;
            boolean flowPerfSplit = false;
            boolean flowPerfDR = false;
            long latestBatchAuditId = 0;
            if (Objects.nonNull(missionDTO)) {
                latestIsSessionFlow = Optional.ofNullable(missionDTO.getContext().getSessionId()).orElse(0L) > 0;
                if (latestIsSessionFlow) {
                    HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getByLeafId(missionDTO.getContext().getSessionId());
                    flowPerfSplit = Objects.nonNull(session) && Objects.nonNull(session.getContext()) && BooleanUtils.isTrue(session.getContext().getPerfSplit());
                    flowPerfDR = Objects.nonNull(session) && Objects.nonNull(session.getContext()) && BooleanUtils.isTrue(session.getContext().getPerfDR());
                }
                latestBatchAuditId = missionDTO.getContractVersionId();
                LogisticsContractMissionStatusEnum contractMissionStatus = LogisticsContractMissionStatusEnum.valueOf(missionDTO.getStatus());
                if (LogisticsContractMissionStatusEnum.isInProcessing(contractMissionStatus)) {
                    latestProcessingStruct = ProcessingContractStructEnum.CONTRACT_STRUCT;
                }
            }
            if (latestProcessingStruct == null) {
                SgPoiLogisticsOfflineDetailDTO sgOfflineData = sgProcessThriftServiceAdapter.getLatestOfflineData(wmPoiId);
                long latestSgBatchAuditId = LogisticsQueryHelper.extractSgBatchAuditId(sgOfflineData);
                if (latestSgBatchAuditId > latestBatchAuditId) {
                    latestBatchAuditId = latestSgBatchAuditId;
                    latestIsSessionFlow = false;
                }
                if (LogisticsQueryHelper.isSgProcessing(sgOfflineData)) {
                    latestProcessingStruct = ProcessingContractStructEnum.SG_STRUCT;
                }
            }
            if (latestProcessingStruct == null) {
                WmPoiLogisticsOfflineDetailDTO oldStructOfflineDetail = heronLogisticsThriftServiceAdapter.getLatestOfflineData(wmPoiId);
                long latestOldBatchAuditId = LogisticsQueryHelper.extractOldBatchAuditId(oldStructOfflineDetail);
                if (latestOldBatchAuditId > latestBatchAuditId) {
                    latestBatchAuditId = latestOldBatchAuditId;
                    latestIsSessionFlow = false;
                }
                if (LogisticsQueryHelper.isOldProcessing(oldStructOfflineDetail)) {
                    latestProcessingStruct = ProcessingContractStructEnum.OLD_STRUCT;
                }
            }
            return EffectiveAndFlowStructJudgeResult.builder()
                    .wmPoiId(wmPoiId)
                    .effectiveNeedAggre(effectiveNeedAggre)
                    .validPerfSplit(validPerfSplit)
                    .validPerfDR(validPerfDR)
                    .flowPerfSplit(flowPerfSplit)
                    .flowPerfDR(flowPerfDR)
                    .latestFlowNeedAggre(latestIsSessionFlow)
                    .processingContractStruct(Optional.ofNullable(latestProcessingStruct).map(ProcessingContractStructEnum::getCode).orElse(null))
                    .build();

        }

    }

    /**
     * 闪购迁移注释：
     * 闪购迁移上线时，企客解耦已全量，且全量门店没有双写门店，因此无需兼容历史的双写门店
     * 闪购迁移双写阶段，流程&生效数据都不认为是双写，数据也无需从网关聚合查询
     * processingContractStruct闪购侧不使用，可以简单处理
     */
    public EffectiveAndFlowStructJudgeResult judgeStructHandleSgTransferDr(Long wmPoiId) throws GatewayAdapterException {
        EffectiveAndFlowStructJudgeResult result = judgeStruct(wmPoiId);
        if (result.getFlowPerfDR()) {
            result.setFlowPerfDR(false);
            result.setLatestFlowNeedAggre(result.getFlowPerfSplit());
        }
        if (result.getValidPerfDR()) {
            result.setValidPerfDR(false);
            result.setEffectiveNeedAggre(result.getValidPerfSplit());
        }
        return result;
    }


}
