package com.sankuai.meituan.waimai.heron.contract.gateway.query.service;

import com.google.common.collect.Lists;
import com.sankuai.meituan.banma.business.poi.sparea.client.core.ultimatesparea.dto.PoiSpareaDTO;
import com.sankuai.meituan.banma.business.poi.sparea.client.core.ultimatesparea.enums.BmPoiSpAreaEnum;
import com.sankuai.meituan.banma.business.poi.sparea.client.core.ultimatesparea.request.SpAreaQueryRequest;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.QueryEffectContractInfoParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.EffectContractInfoData;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmSpAreaQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.bo.WmLogisticsExtTagsBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.WmPoiLogisticsExtPo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.WmPoiLogisticsExtBaseService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.AreaIdentityHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.LogisticsExtTagQueryHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.PerfBusinessIdentityHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.query.helper.LogisticsExtTagExtractHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.query.LogisticsExtTagsQueryParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.phf.query.PhfContractAggreInfoResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query.EffectiveAggreInfoResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query.LogisticsAreaView;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query.LogisticsExtTagsResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.query.PoiLogisticsBrandAndProduct;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.SessionCategoryExtTagsBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.WmLogisticsExtTagsStruct;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsCodeEnum;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.area.station.LogisticsBussinessStationDTO;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.sla.WmPoiSlaPackageDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.QkPoiLogisticsProductDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.WmLogisticsContractItemDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.WmLogisticsContractItemPackageDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.WmLogisticsEffectiveDetailDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.WmLogisticsServicePackageDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.EffectDetailRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.ServiceBrandProductResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description: 门店生效数据查询能力
 * @author: chenyihao04
 * @create: 2023-06-05 10:44
 */
@Service
@Slf4j
public class EffectiveInfoQueryAbilityService {

    @Resource
    private WmPoiLogisticsExtBaseService wmPoiLogisticsExtBaseService;

    @Resource
    private BmPerfQueryThriftServiceAdapter bmPerfQueryThriftServiceAdapter;

    @Resource
    private PlatContractQueryThriftServiceAdapter platContractQueryThriftServiceAdapter;

    @Resource
    private BmSpAreaQueryThriftServiceAdapter bmSpAreaQueryThriftServiceAdapter;

    @Resource
    private PhfContractQueryAbilityService phfContractQueryAbilityService;

    public EffectiveAggreInfoResult queryEffectiveInfo(Long wmPoiId, SessionCategoryEnum sessionCategory) throws GatewayBaseException {
        WmPoiLogisticsExtPo extPo = wmPoiLogisticsExtBaseService.getByWmPoiId(wmPoiId);
        if (extPo == null) {
            return null;
        }
        if (SessionCategoryEnum.PHF.equals(sessionCategory)) {
            return getEffectiveAggreInfoResultForPhf(wmPoiId, extPo);
        }
        SessionCategoryExtTagsBo tags = getTags(extPo, sessionCategory);
        WmLogisticsEffectiveDetailDTO wmLogisticsEffectiveDetailDTO = getWmLogisticsEffectiveDetailDTO(wmPoiId, sessionCategory);
        if (Objects.isNull(wmLogisticsEffectiveDetailDTO)) {
            return null;
        }

        EffectiveAggreInfoResult effectiveAggreInfo = new EffectiveAggreInfoResult();
        effectiveAggreInfo.setWmPoiId(wmPoiId);
        effectiveAggreInfo.setPlatContractItemList(buildPlatItemInfo(wmLogisticsEffectiveDetailDTO));
        effectiveAggreInfo.setStationRelPoList(buildStationInfo(wmLogisticsEffectiveDetailDTO));
        effectiveAggreInfo.setBrandAndProductList(buildBrandAndProductList(wmLogisticsEffectiveDetailDTO));
        effectiveAggreInfo.setFeeMode(wmLogisticsEffectiveDetailDTO.getMainFeeMode());
        effectiveAggreInfo.setSlaInfoList(buildSlaInfoList(wmLogisticsEffectiveDetailDTO));

        if ((Objects.isNull(tags) && SessionCategoryEnum.isCore(sessionCategory)) || (Objects.nonNull(tags) && BooleanUtils.isNotFalse(tags.getHasArea()))) {
            List<LogisticsAreaView> cloneAreaList = getSpAreaList(wmPoiId, sessionCategory);
            effectiveAggreInfo.setSpAreaList(cloneAreaList);
        }

        if (Objects.nonNull(tags) && (BooleanUtils.isTrue(tags.getPerfSplit()) || BooleanUtils.isTrue(tags.getPerfDR())) && BooleanUtils.isNotFalse(tags.getHasPerf())) {
            QueryEffectContractInfoParam queryEffectContractInfoParam = new QueryEffectContractInfoParam();
            queryEffectContractInfoParam.setWmPoiId(wmPoiId);
            queryEffectContractInfoParam.setBusinessIdentity(PerfBusinessIdentityHelper.convertFromSessionCategory(Optional.ofNullable(sessionCategory).orElse(SessionCategoryEnum.CORE)));
            EffectContractInfoData perfEffectData = bmPerfQueryThriftServiceAdapter.queryEffectContractInfo(queryEffectContractInfoParam);
            if (Objects.nonNull(perfEffectData)) {
                effectiveAggreInfo.setPerfContractItemList(perfEffectData.getContractFeeItemList());
            }
        }
        effectiveAggreInfo.setPerfDR(Objects.nonNull(tags) && BooleanUtils.isTrue(tags.getPerfDR()));
        return effectiveAggreInfo;
    }


    private EffectiveAggreInfoResult getEffectiveAggreInfoResultForPhf(Long wmPoiId, WmPoiLogisticsExtPo extPo) throws GatewayBaseException {
        SessionCategoryExtTagsBo extTagsBo = LogisticsExtTagExtractHelper.extractTagsBySessionCategory(extPo, SessionCategoryEnum.PHF);
        if (Objects.isNull(extTagsBo)) {
            return null;
        }
        PhfContractAggreInfoResult phfContractAggreInfoResult = phfContractQueryAbilityService.queryEffectiveContractAggreInfo(wmPoiId, false);
        if (Objects.isNull(phfContractAggreInfoResult)) {
            return null;
        }
        EffectiveAggreInfoResult effectiveAggreInfoResult = new EffectiveAggreInfoResult();
        effectiveAggreInfoResult.setFeeMode(phfContractAggreInfoResult.getContractBaseInfo().getFeeMode());
        effectiveAggreInfoResult.setBrandAndProductList(convertPhfBrandAndProductList(wmPoiId, phfContractAggreInfoResult.getContractBaseInfo().getOriginServiceBrandProductList()));
        effectiveAggreInfoResult.setPerfContractItemList(phfContractAggreInfoResult.getPerfContractItemList());
        effectiveAggreInfoResult.setPlatContractItemList(phfContractAggreInfoResult.getPlatContractItemList());
        return effectiveAggreInfoResult;
    }

    private List<PoiLogisticsBrandAndProduct> convertPhfBrandAndProductList(Long wmPoiId, List<ServiceBrandProductResponseDTO> originServiceBrandProductList) {
        if (CollectionUtils.isEmpty(originServiceBrandProductList)) {
            return Lists.newArrayList();
        }
        return originServiceBrandProductList.stream()
                .map(serviceBrandProductResponseDTO -> {
                    PoiLogisticsBrandAndProduct poiLogisticsBrandAndProduct = new PoiLogisticsBrandAndProduct();
                    poiLogisticsBrandAndProduct.setWmPoiId(wmPoiId);
                    poiLogisticsBrandAndProduct.setLogisticsId(Integer.valueOf(serviceBrandProductResponseDTO.getServiceBrand()));
                    poiLogisticsBrandAndProduct.setServiceBrand(serviceBrandProductResponseDTO.getServiceBrand());
                    poiLogisticsBrandAndProduct.setServiceProductList(serviceBrandProductResponseDTO.getServiceProductList());
                    return poiLogisticsBrandAndProduct;
                })
                .collect(Collectors.toList());
    }

    private SessionCategoryExtTagsBo getTags(WmPoiLogisticsExtPo extPo, SessionCategoryEnum sessionCategory) {
        WmLogisticsExtTagsStruct wmLogisticsExtTagsStruct = new WmLogisticsExtTagsStruct();
        BeanUtils.copyProperties(Optional.ofNullable(extPo.getTags()).orElse(new WmLogisticsExtTagsBo()), wmLogisticsExtTagsStruct);
        return LogisticsExtTagQueryHelper.getTagsBySessionCategory(wmLogisticsExtTagsStruct, sessionCategory);
    }

    private List<LogisticsAreaView> getSpAreaList(Long wmPoiId, SessionCategoryEnum sessionCategory) throws GatewayAdapterException {
        SpAreaQueryRequest spAreaQueryRequest = new SpAreaQueryRequest();
        spAreaQueryRequest.setPoiId(wmPoiId);
        spAreaQueryRequest.setCls(BmPoiSpAreaEnum.BmPoiSpAreaClsEnum.ALL.getValue());
        spAreaQueryRequest.setValid(BmPoiSpAreaEnum.BmPoiSpAreaValidEnum.ALL.getValue());
        spAreaQueryRequest.setIncludeActivityTagEnums(Lists.newArrayList());
        spAreaQueryRequest.setExcludeActivityTagEnums(Lists.newArrayList(BmPoiSpAreaEnum.BmPoiSpAreaActivityTagBitEnum.AB_TEST.getCode()));

        List<PoiSpareaDTO> poiSpAreaDTOS = bmSpAreaQueryThriftServiceAdapter.querySpArea(spAreaQueryRequest, AreaIdentityHelper.convertFromSessionCategory(sessionCategory));
        return Optional.ofNullable(poiSpAreaDTOS)
                .map(areaTempList -> areaTempList.stream().map(area -> {
                                    String areaJson = JacksonUtil.writeAsJsonStr(area);
                                    LogisticsAreaView logisticsAreaView = JacksonUtil.readValue(areaJson, LogisticsAreaView.class);
                                    logisticsAreaView.setLogisticstype(area.getLogisticsType());
                                    return logisticsAreaView;
                                })
                                .collect(Collectors.toList())
                )
                .orElse(null);
    }

    private WmLogisticsEffectiveDetailDTO getWmLogisticsEffectiveDetailDTO(Long wmPoiId, SessionCategoryEnum sessionCategory) throws GatewayAdapterException {
        WmLogisticsEffectiveDetailDTO wmLogisticsEffectiveDetailDTO;
        EffectDetailRequestDTO param = new EffectDetailRequestDTO();
        param.setWmPoiId(wmPoiId);
        param.setSessionCategory(sessionCategory.name());
        wmLogisticsEffectiveDetailDTO = platContractQueryThriftServiceAdapter.queryEffectiveDetailSlave4SessionCategoryForGateway(param);
        return wmLogisticsEffectiveDetailDTO;
    }

    private List<WmPoiSlaPackageDTO> buildSlaInfoList(WmLogisticsEffectiveDetailDTO wmLogisticsEffectiveDetailDTO) {
        return wmLogisticsEffectiveDetailDTO.getServicePackageList().stream().map(WmLogisticsServicePackageDTO::getSlaPackage).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<PoiLogisticsBrandAndProduct> buildBrandAndProductList(WmLogisticsEffectiveDetailDTO wmLogisticsEffectiveDetailDTO) {
        List<PoiLogisticsBrandAndProduct> brandAndProductList = wmLogisticsEffectiveDetailDTO.getLogisticsList().stream()
                .map(logistics -> {
                    PoiLogisticsBrandAndProduct brandAndProduct = new PoiLogisticsBrandAndProduct();
                    brandAndProduct.setWmPoiId(wmLogisticsEffectiveDetailDTO.getWmPoiId());
                    brandAndProduct.setServiceBrand(logistics.getLogisticsCode());
                    brandAndProduct.setLogisticsId(logistics.getLogisticsId().intValue());
                    return brandAndProduct;
                })
                .collect(Collectors.toList());

        for (WmLogisticsServicePackageDTO servicePackage : wmLogisticsEffectiveDetailDTO.getServicePackageList()) {
            QkPoiLogisticsProductDTO productDTO = servicePackage.getLogisticsProduct();
            if (Objects.isNull(productDTO)) {
                continue;
            }
            brandAndProductList.stream()
                    .filter(brandAndProduct -> brandAndProduct.getServiceBrand().equals(servicePackage.getLogisticsCode()))
                    .forEach(brandAndProduct -> brandAndProduct.setServiceProductList(Lists.newArrayList(productDTO.getProductId().intValue())));
        }
        return brandAndProductList;
    }


    private List<LogisticsBussinessStationDTO> buildStationInfo(WmLogisticsEffectiveDetailDTO wmLogisticsEffectiveDetailDTO) {
        Optional<List<LogisticsBussinessStationDTO>> stationOptional = wmLogisticsEffectiveDetailDTO.getServicePackageList().stream()
                .filter(o -> Objects.equals(o.getLogisticsCode(), LogisticsCodeEnum.SCHOOL_AGGR.getCode()) || Objects.equals(SessionCategoryEnum.DRONE.name(), wmLogisticsEffectiveDetailDTO.getSessionCategory())).map(WmLogisticsServicePackageDTO::getStationList).findFirst();
        return stationOptional.orElse(new ArrayList<>());
    }

    private List<WmLogisticsContractItemDTO> buildPlatItemInfo(WmLogisticsEffectiveDetailDTO wmLogisticsEffectiveDetailDTO) {
        return wmLogisticsEffectiveDetailDTO.getServicePackageList().stream()
                .flatMap(o -> Optional.ofNullable(o.getContractItemPackage()).map(WmLogisticsContractItemPackageDTO::getItemList).orElse(new ArrayList<>()).stream()).collect(Collectors.toList());
    }

    public LogisticsExtTagsResult queryPoiEffectExtTags(LogisticsExtTagsQueryParam param) {
        LogisticsExtTagsResult result = new LogisticsExtTagsResult();
        WmPoiLogisticsExtPo extPo;
        if (BooleanUtils.isTrue(param.getRt())) {
            extPo = wmPoiLogisticsExtBaseService.getByWmPoiIdRT(param.getWmPoiId());
        } else {
            extPo = wmPoiLogisticsExtBaseService.getByWmPoiId(param.getWmPoiId());
        }
        if (extPo == null) {
            return result;
        }
        SessionCategoryExtTagsBo tags = getTags(extPo, param.getSessionCategory());
        if (tags != null) {
            BeanUtils.copyProperties(tags, result);
        }
        result.setWmPoiId(param.getWmPoiId());
        result.setSessionCategory(Optional.ofNullable(param.getSessionCategory()).orElse(SessionCategoryEnum.CORE).name());
        return result;
    }



}