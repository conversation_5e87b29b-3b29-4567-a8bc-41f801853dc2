package com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util;

import com.sankuai.meituan.banma.business.poi.sparea.client.base.dto.IdentityDTO;
import com.sankuai.meituan.banma.poi.sparea.thrift.constants.BmSpAreaCustomerSourceConstants;
import com.sankuai.meituan.banma.thrift.deliveryproduct.common.SaleChannelTypeUtils;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;

import java.util.Objects;
import java.util.Optional;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2024-04-10 10:56
 */
public class AreaIdentityHelper {

    private static final IdentityDTO CORE_IDENTITY = new IdentityDTO();
    private static final IdentityDTO DRONE_IDENTITY = new IdentityDTO();
    private static final IdentityDTO FRUIT_TOGETHER_IDENTITY = new IdentityDTO();
    private static final IdentityDTO PHF_IDENTITY = new IdentityDTO();

    static {
        CORE_IDENTITY.setCustomerSourceType(BmSpAreaCustomerSourceConstants.WAI_MAI);
        DRONE_IDENTITY.setCustomerSourceType(SaleChannelTypeUtils.wrjChannel().getCode());
        FRUIT_TOGETHER_IDENTITY.setCustomerSourceType(SaleChannelTypeUtils.shanGouGqpdChannel().getCode());
        PHF_IDENTITY.setCustomerSourceType(SaleChannelTypeUtils.phfChannel().getCode());
    }


    public static IdentityDTO convertFromSessionCategory(SessionCategoryEnum sessionCategory) {
        sessionCategory = Optional.ofNullable(sessionCategory).orElse(SessionCategoryEnum.CORE);
        if (SessionCategoryEnum.isCore(sessionCategory)) {
            return CORE_IDENTITY;
        } else if (Objects.equals(sessionCategory, SessionCategoryEnum.DRONE)) {
            return DRONE_IDENTITY;
        } else if (Objects.equals(sessionCategory, SessionCategoryEnum.FRUIT_TOGETHER)) {
            return FRUIT_TOGETHER_IDENTITY;
        } else if (Objects.equals(sessionCategory, SessionCategoryEnum.PHF)) {
            return PHF_IDENTITY;
        } else if (Objects.equals(sessionCategory, SessionCategoryEnum.ARRIVE_SHOP)) {
            throw new RuntimeException("不支持到店自取映射");
        } else if (Objects.equals(sessionCategory, SessionCategoryEnum.VIP_CARD)) {
            throw new RuntimeException("不支持会员卡映射");
        } else {
            throw new RuntimeException("暂不支持此场景");
        }
    }

    public static SessionCategoryEnum convertToSessionCategory(Integer areaIdentityCustomerSourceType) {
        if (Objects.equals(areaIdentityCustomerSourceType, SaleChannelTypeUtils.wrjChannel().getCode())) {
            return SessionCategoryEnum.DRONE;
        } else if (Objects.equals(areaIdentityCustomerSourceType, SaleChannelTypeUtils.shanGouGqpdChannel().getCode())) {
            return SessionCategoryEnum.FRUIT_TOGETHER;
        } else if (Objects.equals(areaIdentityCustomerSourceType, SaleChannelTypeUtils.phfChannel().getCode())) {
            return SessionCategoryEnum.PHF;
        } else if (Objects.isNull(areaIdentityCustomerSourceType) || Objects.equals(areaIdentityCustomerSourceType, SaleChannelTypeUtils.wmptChannel().getCode())) {
            return SessionCategoryEnum.CORE;
        }
        throw new RuntimeException("暂不支持此场景");
    }

}