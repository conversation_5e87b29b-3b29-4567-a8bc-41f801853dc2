<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="thriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="50"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="2"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="sgPoiLogisticsTempThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="thriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.shangou.merchant.logistics.thrift.service.SgPoiLogisticsTempThriftService"/>
        <property name="timeout" value="60000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="appKey"
                  value="com.sankuai.heron.contract.gateway"/>
        <property name="remoteAppkey" value="com.sankuai.sgmerchant.logistics"/>
        <!-- 开启 Netty IO -->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="sgPoiLogisticsSignThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="thriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.shangou.merchant.logistics.thrift.service.SgPoiLogisticsSignThriftService"/>
        <property name="timeout" value="60000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="appKey"
                  value="com.sankuai.heron.contract.gateway"/>
        <property name="remoteAppkey" value="com.sankuai.sgmerchant.logistics"/>
        <!--        <property name="remoteServerPort" value="8440"/>-->
        <!-- 开启 Netty IO -->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="sgPoiLogisticsQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="thriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.shangou.merchant.logistics.thrift.service.SgPoiLogisticsQueryThriftService"/>
        <property name="timeout" value="15000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="appKey"
                  value="com.sankuai.heron.contract.gateway"/>
        <property name="remoteAppkey" value="com.sankuai.sgmerchant.logistics"/>
        <!--        <property name="remoteServerPort" value="8440"/>-->
        <!-- 开启 Netty IO -->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="sgPoiLogisticsMissionThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="thriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.shangou.merchant.logistics.thrift.service.SgPoiLogisticsMissionThriftService"/>
        <property name="timeout" value="15000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="appKey"
                  value="com.sankuai.heron.contract.gateway"/>
        <property name="remoteAppkey" value="com.sankuai.sgmerchant.logistics"/>
        <!--        <property name="remoteServerPort" value="8440"/>-->
        <!-- 开启 Netty IO -->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="sgPoiLogisticsEffectiveThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="thriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.shangou.merchant.logistics.thrift.service.SgPoiLogisticsEffectiveThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="appKey"
                  value="com.sankuai.heron.contract.gateway"/>
        <property name="remoteAppkey" value="com.sankuai.sgmerchant.logistics"/>
        <!--        <property name="remoteServerPort" value="8440"/>-->
        <!-- 开启 Netty IO -->
        <property name="nettyIO" value="true"/>
    </bean>

</beans>
