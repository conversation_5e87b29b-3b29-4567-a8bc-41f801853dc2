<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		 http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd">

  <import resource="classpath:banma_common_client.xml" />

  <bean id="bmPoiSpAreaOpenThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
    <property name="maxActive" value="100"/>
    <property name="maxIdle" value="20"/>
    <property name="minIdle" value="1"/>
    <property name="maxWait" value="3000"/>
    <property name="testOnBorrow" value="true"/>
    <property name="testOnReturn" value="false"/>
    <property name="testWhileIdle" value="false"/>
    <property name="normalSize" value="2"/> <!-- nettyIO 最大连接数，不建议设置太大 -->
    <property name="initialSize" value="1"/> <!--nettyIO 初始连接数，可以与最大连接数保持一致 -->
  </bean>

  <bean id="bmOpenBuildSpAreaThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy">
    <property name="mtThriftPoolConfig" ref="bmPoiSpAreaOpenThriftPoolConfig"/>
    <property name="serviceInterface"  value="com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.service.BmOpenBuildSpAreaThriftService"/>
    <property name="timeout" value="30000"/><!-- thrift rpc 超时时间（毫秒） -->
    <property name="appKey" ref="appKey"/>
    <property name="remoteServerPort" value="9001"/>
    <property name="remoteAppkey" value="com.sankuai.deliverybusiness.poi.sparea"/>
    <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
  </bean>

  <bean id="bmOpenQuerySpAreaInProcessThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy">
    <property name="mtThriftPoolConfig" ref="bmPoiSpAreaOpenThriftPoolConfig"/>
    <property name="serviceInterface"  value="com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.service.BmOpenQuerySpAreaInProcessThriftService"/>
    <property name="timeout" value="10000"/><!-- thrift rpc 超时时间（毫秒） -->
    <property name="appKey" ref="appKey"/>
    <property name="remoteServerPort" value="9001"/>
    <property name="remoteAppkey" value="com.sankuai.deliverybusiness.poi.sparea"/>
    <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
  </bean>

  <bean id="bmOpenSaveSpAreaThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy">
    <property name="mtThriftPoolConfig" ref="bmPoiSpAreaOpenThriftPoolConfig"/>
    <property name="serviceInterface"  value="com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.service.BmOpenSaveSpAreaThriftService"/>
    <property name="timeout" value="50000"/><!-- thrift rpc 超时时间（毫秒） -->
    <property name="appKey" ref="appKey"/>
    <property name="remoteServerPort" value="9001"/>
    <property name="remoteAppkey" value="com.sankuai.deliverybusiness.poi.sparea"/>
    <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
  </bean>

  <bean id="bmOpenSignSpAreaThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy">
    <property name="mtThriftPoolConfig" ref="bmPoiSpAreaOpenThriftPoolConfig"/>
    <property name="serviceInterface"  value="com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.service.BmOpenSignSpAreaThriftService"/>
    <property name="timeout" value="10000"/><!-- thrift rpc 超时时间（毫秒） -->
    <property name="appKey" ref="appKey"/>
    <property name="remoteServerPort" value="9001"/>
    <property name="remoteAppkey" value="com.sankuai.deliverybusiness.poi.sparea"/>
    <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
  </bean>

  <bean id="bmOpenSpAreaProcessChangeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy">
    <property name="mtThriftPoolConfig" ref="bmPoiSpAreaOpenThriftPoolConfig"/>
    <property name="serviceInterface"  value="com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.service.BmOpenSpAreaProcessChangeThriftService"/>
    <property name="timeout" value="10000"/><!-- thrift rpc 超时时间（毫秒） -->
    <property name="appKey" ref="appKey"/>
    <property name="remoteServerPort" value="9001"/>
    <property name="remoteAppkey" value="com.sankuai.deliverybusiness.poi.sparea"/>
    <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
  </bean>

  <bean id="bmOpenCommonSpAreaThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy">
    <property name="mtThriftPoolConfig" ref="bmPoiSpAreaOpenThriftPoolConfig"/>
    <property name="serviceInterface"  value="com.sankuai.meituan.banma.business.poi.sparea.client.core.ultimatesparea.service.BmOpenCommonSpAreaThriftService"/>
    <property name="timeout" value="10000"/><!-- thrift rpc 超时时间（毫秒） -->
    <property name="appKey" ref="appKey"/>
    <property name="remoteServerPort" value="9001"/>
    <property name="remoteAppkey" value="com.sankuai.deliverybusiness.poi.sparea"/>
    <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
  </bean>

  <bean id="bmOpenEffectSpAreaThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy">
    <property name="mtThriftPoolConfig" ref="bmPoiSpAreaOpenThriftPoolConfig"/>
    <property name="serviceInterface"  value="com.sankuai.meituan.banma.business.poi.sparea.client.core.ultimatesparea.service.BmOpenEffectSpAreaThriftService"/>
    <property name="timeout" value="30000"/><!-- thrift rpc 超时时间（毫秒） -->
    <property name="appKey" ref="appKey"/>
    <property name="remoteServerPort" value="9001"/>
    <property name="remoteAppkey" value="com.sankuai.deliverybusiness.poi.sparea"/>
    <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
  </bean>

  <bean id="grayControlThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy">
    <property name="mtThriftPoolConfig" ref="bmPoiSpAreaOpenThriftPoolConfig"/>
    <property name="serviceInterface"  value="com.sankuai.meituan.banma.business.poi.sparea.client.support.sys.service.GrayControlThriftService"/>
    <property name="timeout" value="10000"/><!-- thrift rpc 超时时间（毫秒） -->
    <property name="appKey" ref="appKey"/>
    <property name="remoteServerPort" value="9001"/>
    <property name="remoteAppkey" value="com.sankuai.deliverybusiness.poi.sparea"/>
    <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
  </bean>

  <bean id="bmOpenAuditSpAreaThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy">
    <property name="mtThriftPoolConfig" ref="bmPoiSpAreaOpenThriftPoolConfig"/>
    <property name="serviceInterface"  value="com.sankuai.meituan.banma.business.poi.sparea.client.support.audit.service.BmOpenAuditSpAreaThriftService"/>
    <property name="timeout" value="10000"/><!-- thrift rpc 超时时间（毫秒） -->
    <property name="appKey" ref="appKey"/>
    <property name="remoteServerPort" value="9001"/>
    <property name="remoteAppkey" value="com.sankuai.deliverybusiness.poi.sparea"/>
    <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
  </bean>

  <bean id="bmOpenQuerySpAreaThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy">
    <property name="mtThriftPoolConfig" ref="bmPoiSpAreaOpenThriftPoolConfig"/>
    <property name="serviceInterface"  value="com.sankuai.meituan.banma.business.poi.sparea.client.core.ultimatesparea.service.BmOpenQuerySpAreaThriftService"/>
    <property name="timeout" value="10000"/><!-- thrift rpc 超时时间（毫秒） -->
    <property name="appKey" ref="appKey"/>
    <property name="remoteServerPort" value="9001"/>
    <property name="remoteAppkey" value="com.sankuai.deliverybusiness.poi.sparea"/>
    <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
  </bean>

  <bean id="bmOpenControlSpAreaThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy">
    <property name="mtThriftPoolConfig" ref="bmPoiSpAreaOpenThriftPoolConfig"/>
    <property name="serviceInterface"  value="com.sankuai.meituan.banma.business.poi.sparea.client.common.service.BmOpenControlSpAreaThriftService"/>
    <property name="timeout" value="10000"/><!-- thrift rpc 超时时间（毫秒） -->
    <property name="appKey" ref="appKey"/>
    <property name="remoteServerPort" value="9001"/>
    <property name="remoteAppkey" value="com.sankuai.deliverybusiness.poi.sparea"/>
    <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
  </bean>

</beans>
