package com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.*;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.event.ResetYiyaoOnlineTechFeeParam;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.update.PoiApplySignResultDTO;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.exception.WmHeronLogisticsException;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.service.PoiLogisticsEventThriftService;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.service.WmLogisticsHeronCommonThriftService;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.service.WmPoiLogisticsAbilityThriftService;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.service.WmPoiLogisticsQueryThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-05-17 10:43
 */
@Service
@Slf4j
public class HeronLogisticsThriftServiceAdapter {

    @Resource
    private WmPoiLogisticsAbilityThriftService wmPoiLogisticsAbilityThriftService;

    @Resource
    private WmPoiLogisticsQueryThriftService wmPoiLogisticsQueryThriftService;

    @Resource
    private WmLogisticsHeronCommonThriftService wmLogisticsHeronCommonThriftService;

    @Resource
    private PoiLogisticsEventThriftService poiLogisticsEventThriftService;


    public void oldCancelSign(WmPoiConfirmCallbackRequestDTO param) throws GatewayAdapterException {
        try {
            log.info("千鹭服务 oldCancelSign 取消签约, 参数 param: {}", JacksonUtil.writeAsJsonStr(param));
            wmPoiLogisticsAbilityThriftService.cancelSign(param);
            log.info("千鹭服务 oldCancelSign 取消签约完成, 参数 param: {}", JacksonUtil.writeAsJsonStr(param));
        } catch (WmHeronLogisticsException e) {
            log.info("千鹭服务 oldCancelSign 取消签约业务异常, 参数 param: {}", JacksonUtil.writeAsJsonStr(param));
            throw GatewayAdapterException.businessException(e.getCode(), e, "千鹭取消签约失败:" + e.getMessage());
        } catch (TException e) {
            log.info("千鹭服务 oldCancelSign 取消签约系统异常, 参数 param: {}", JacksonUtil.writeAsJsonStr(param));
            throw GatewayAdapterException.thriftException(e, "千鹭取消签约失败");
        }
    }

    public void invalidAuditProcess(Long wmPoiId) throws GatewayAdapterException {
        try {
            log.info("千鹭服务 invalidAuditProcess 千鹭取消老结构流程, wmPoiId: {}", wmPoiId);
            wmPoiLogisticsAbilityThriftService.invalidAuditProcess(wmPoiId);
            log.info("千鹭服务 invalidAuditProcess 千鹭取消老结构流程完成, wmPoiId: {}", wmPoiId);
        } catch (WmHeronLogisticsException e) {
            log.info("千鹭服务 invalidAuditProcess 千鹭取消老结构流程异常, wmPoiId: {}", JacksonUtil.writeAsJsonStr(wmPoiId));
            throw GatewayAdapterException.businessException(e.getCode(), e, "千鹭取消流程失败");
        }
    }

    public WmPoiLogisticsOnlineDetailDTO getOnlineDetail(Long wmPoiId) throws GatewayAdapterException {
        try {
            log.info("千鹭服务 getOnlineDetailV2 千鹭获取老结构生效数据, wmPoiId: {}", wmPoiId);
            return wmPoiLogisticsQueryThriftService.getOnlineDetailV2(wmPoiId);
        } catch (WmHeronLogisticsException e) {
            log.info("千鹭服务 getOnlineDetailV2 千鹭获取老结构生效数据异常, wmPoiId: {}", JacksonUtil.writeAsJsonStr(wmPoiId));
            throw GatewayAdapterException.businessException(e.getCode(), e.getMessage());
        }
    }

    public WmPoiLogisticsOfflineDetailDTO getLatestOfflineData(long wmPoiId) throws GatewayAdapterException {
        try {
            log.info("千鹭服务 getOnlineDetailV2 千鹭获取老结构流程数据, wmPoiId: {}", wmPoiId);
            return wmPoiLogisticsQueryThriftService.getLatestOfflineData(wmPoiId);
        } catch (WmHeronLogisticsException e) {
            log.info("千鹭服务 getOnlineDetailV2 千鹭获取老结构流程数据异常, wmPoiId: {}", JacksonUtil.writeAsJsonStr(wmPoiId));
            throw GatewayAdapterException.businessException(e.getCode(), e.getMessage());
        }
    }



    public void unbindPoiLogisticsInfo(Long wmPoiId) throws GatewayAdapterException {
        try {
            log.info("千鹭服务 unbindPoiLogisticsInfo 千鹭失效老结构配送信息, wmPoiId: {}", wmPoiId);
            wmPoiLogisticsAbilityThriftService.unbindPoiLogisticsInfo(wmPoiId);
            log.info("千鹭服务 unbindPoiLogisticsInfo 千鹭失效老结构配送信息结束, wmPoiId: {}", wmPoiId);
        } catch (WmHeronLogisticsException | TException e) {
            log.info("千鹭服务 unbindPoiLogisticsInfo 千鹭失效老结构配送信息异常, wmPoiId: {}, e: ", JacksonUtil.writeAsJsonStr(wmPoiId), e);
            throw GatewayAdapterException.businessException(-1, e.getMessage());
        }
    }

    public void applySignResultNotice(PoiApplySignResultDTO poiApplySignResultDTO) throws GatewayAdapterException {
        try {
            log.info("千鹭服务 applySignResultNotice 千鹭同步老结构发起签约结果, 参数 param: {}", JacksonUtil.writeAsJsonStr(poiApplySignResultDTO));
            wmPoiLogisticsAbilityThriftService.applySignResultNotice(poiApplySignResultDTO);
            log.info("千鹭服务 applySignResultNotice 千鹭同步老结构发起签约结果, 参数 param: {}", JacksonUtil.writeAsJsonStr(poiApplySignResultDTO));
        } catch (WmHeronLogisticsException e) {
            log.info("千鹭服务 applySignResultNotice 千鹭同步老结构发起签约结果异常, 参数 param: {}, e: ", JacksonUtil.writeAsJsonStr(poiApplySignResultDTO), e);
            throw GatewayAdapterException.businessException(-1, e.getMessage());
        }
    }

    public List<WmLogisticsDTO> getAllLogistics() {
        return wmLogisticsHeronCommonThriftService.getAllLogistics();
    }

    public Map<String, WmLogisticsDTO> getAllLogisticsMap() {
        return getAllLogistics().stream()
                .collect(Collectors.toMap(WmLogisticsDTO::getLogisticsCode, Function.identity()));
    }


    public List<WmPoiLogisticsBatchSubmitResponseDTO> multiCommitAudit(WmPoiLogisticsBatchSubmitRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("千鹭服务 multiCommitAudit 千鹭老结构多店提审结果, 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            List<WmPoiLogisticsBatchSubmitResponseDTO> wmPoiLogisticsBatchSubmitResponseDTOS = wmPoiLogisticsAbilityThriftService.batchSubmit(requestDTO);
            log.info("千鹭服务 multiCommitAudit 千鹭老结构多店提审结果, 结果 result: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            return wmPoiLogisticsBatchSubmitResponseDTOS;
        } catch (WmHeronLogisticsException | TException e) {
            log.info("千鹭服务 multiCommitAudit 千鹭老结构多店提审异常, 参数 param: {}, e: ", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.businessException(-1, e.getMessage());
        }
    }

    public ResultDTO resetYiyaoOnlineTechFee(ResetYiyaoOnlineTechFeeParam param) throws GatewayAdapterException {
        try {
            log.info("千鹭服务 resetYiyaoOnlineTechFee 千鹭执行医药技术费率推送, 参数 param: {}", JacksonUtil.writeAsJsonStr(param));
            ResultDTO resultDTO = poiLogisticsEventThriftService.resetYiyaoOnlineTechFee(param);
            log.info("千鹭服务 resetYiyaoOnlineTechFee 千鹭执行医药技术费率推送, 参数 param: {}, 结果 result: {}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(param));
            return resultDTO;
        } catch (WmHeronLogisticsException | TException e) {
            log.info("千鹭服务 resetYiyaoOnlineTechFee 千鹭执行医药技术费率推送, 参数 param: {}, e: ", JacksonUtil.writeAsJsonStr(param), e);
            throw GatewayAdapterException.businessException(-1, e.getMessage());
        }
    }
}