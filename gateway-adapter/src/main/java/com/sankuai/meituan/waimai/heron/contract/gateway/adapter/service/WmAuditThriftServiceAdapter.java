package com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.thrift.api.audit.WmAuditOperationApi;
import com.sankuai.meituan.waimai.thrift.domain.Operator;
import com.sankuai.meituan.waimai.thrift.domain.audit.request.CreateAuditTaskResDTO;
import com.sankuai.meituan.waimai.thrift.domain.audit.request.FinishCommitResDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmAuditServerException;
import com.sankuai.meituan.waimai.thrift.rateApproval.vo.response.LongResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 外卖审核平台
 * @author: chenyihao04
 * @create: 2024-08-14 16:39
 */
@Slf4j
@Service
public class WmAuditThriftServiceAdapter {

    @Resource
    private WmAuditOperationApi wmAuditOperationApi;

    public LongResponse createTask(CreateAuditTaskResDTO param, Operator operator) throws GatewayAdapterException {
        try {
            log.info("审核服务 WmAuditThriftServiceAdapter createTask param: {}", JacksonUtil.writeAsJsonStr(param));
            LongResponse taskIdResp = wmAuditOperationApi.createTask(param, operator);
            log.info("审核服务 WmAuditThriftServiceAdapter createTask param: {}, result: {}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(taskIdResp));
            if (!taskIdResp.ifSuccess()) {
                log.warn("审核服务 WmAuditThriftServiceAdapter createTask error, param: {}, resp: {}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(taskIdResp));
                throw GatewayAdapterException.businessException(-1, taskIdResp.getMsg());
            }
            return taskIdResp;
        } catch (WmAuditServerException e) {
            log.warn("审核服务 WmAuditThriftServiceAdapter createTask error, param: {}", JacksonUtil.writeAsJsonStr(param), e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "创建外卖审核平台任务异常:" + e.getMessage());
        } catch (Exception e) {
            log.warn("审核服务 WmAuditThriftServiceAdapter createTask error, param: {}", JacksonUtil.writeAsJsonStr(param), e);
            throw GatewayAdapterException.thriftException(e, "创建外卖审核平台任务异常:" + e.getMessage());
        }
    }

    public LongResponse commitFinished(FinishCommitResDTO param, Operator operator) throws GatewayAdapterException {
        try {
            log.info("审核服务 WmAuditThriftServiceAdapter commitFinished param: {}", JacksonUtil.writeAsJsonStr(param));
            LongResponse taskIdResp = wmAuditOperationApi.commitFinished(param, operator);
            log.info("审核服务 WmAuditThriftServiceAdapter commitFinished param: {}, result: {}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(taskIdResp));
            if (!taskIdResp.ifSuccess()) {
                log.warn("审核服务 WmAuditThriftServiceAdapter commitFinished error, param: {}, resp: {}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(taskIdResp));
                throw GatewayAdapterException.businessException(-1, taskIdResp.getMsg());
            }
            return taskIdResp;
        } catch (WmAuditServerException e) {
            log.warn("审核服务 WmAuditThriftServiceAdapter commitFinished error, param: {}", JacksonUtil.writeAsJsonStr(param), e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "创建外卖审核平台任务异常:" + e.getMessage());
        } catch (Exception e) {
            log.warn("审核服务 WmAuditThriftServiceAdapter commitFinished error, param: {}", JacksonUtil.writeAsJsonStr(param), e);
            throw GatewayAdapterException.thriftException(e, "提交外卖审核平台任务ACK异常:" + e.getMessage());
        }
    }
}