package com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service;

import com.sankuai.meituan.banma.business.poi.sparea.client.base.dto.IdentityDTO;
import com.sankuai.meituan.banma.business.poi.sparea.client.base.exception.BmSpareaException;
import com.sankuai.meituan.banma.business.poi.sparea.client.core.ultimatesparea.request.SpAreaPoiConciseEffectRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.core.ultimatesparea.request.SpAreaPoiEffectRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.core.ultimatesparea.service.BmOpenEffectSpAreaThriftService;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.audit.request.SpAreaCommitAuditRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.audit.service.BmOpenAuditSpAreaThriftService;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaNeedSignRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaPoiConfirmedNotificationRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaPoiSaveRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaRebuildAndSaveRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaSave4OnlineRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaSelfSaveRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaSignNotificationRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaStopProcessRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.response.SpAreaNeedSignResponse;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.service.BmOpenSaveSpAreaThriftService;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.service.BmOpenSignSpAreaThriftService;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.service.BmOpenSpAreaProcessChangeThriftService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 范围平台化接口
 * @author: chenyihao04
 * @create: 2023-09-26 11:20
 */
@Service
@Slf4j
public class BmSpAreaFlowThriftServiceAdapter {

    @Resource
    private BmOpenSaveSpAreaThriftService bmOpenSaveSpAreaThriftService;

    @Resource
    private BmOpenSignSpAreaThriftService bmOpenSignSpAreaThriftService;

    @Resource
    private BmOpenEffectSpAreaThriftService bmOpenEffectSpAreaThriftService;

    @Resource
    private BmOpenSpAreaProcessChangeThriftService bmOpenSpAreaProcessChangeThriftService;

    @Resource
    private BmOpenAuditSpAreaThriftService bmOpenAuditSpAreaThriftService;



    public void initAndSave(SpAreaSelfSaveRequest param, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] initAndSave 参数 param: {} identity={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(identity));
            bmOpenSaveSpAreaThriftService.initAndSave(param, identity);
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] initAndSave 自入驻构建并保存范围异常, 参数 param: {}", JacksonUtil.writeAsJsonStr(param), e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "自入驻构建保存配送范围:" + e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] initAndSave 自入驻构建并保存范围异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "自入驻构建并保存范围异常:" + e.getMessage());
        }
    }

    public void validate(SpAreaSelfSaveRequest param, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] validate 参数 param: {} identity={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(identity));
            bmOpenSaveSpAreaThriftService.validate(param, identity);
            log.info("配送范围服务 [范围平台化] validate finish 参数 param: {}", JacksonUtil.writeAsJsonStr(param));
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] validate 保存范围校验异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] checkSinglePoiSelfSettle 保存范围校验异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "保存范围校验异常:" + e.getMessage());
        }
    }

    public void validateWithParam(SpAreaPoiSaveRequest param, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] validateWithParam 参数 param: {} identity={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(identity));
            bmOpenSaveSpAreaThriftService.validateWithParam(param, identity);
            log.info("配送范围服务 [范围平台化] validateWithParam finish ");
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] validateWithParam 保存校验范围异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e,  e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] validateWithParam 保存校验范围异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "保存校验范围异常:" + e.getMessage());
        }
    }

    public void save(SpAreaPoiSaveRequest saveRequest, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] save 参数 param: {} identity={}", JacksonUtil.writeAsJsonStr(saveRequest), JacksonUtil.writeAsJsonStr(identity));
            bmOpenSaveSpAreaThriftService.save(saveRequest, identity);
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] save 配送范围保存异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "保存范围:" + e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] save 配送范围保存异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "配送范围保存异常:" + e.getMessage());
        }
    }

    public void rebuildAndSave(SpAreaRebuildAndSaveRequest saveRequest, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] rebuildAndSave 参数 param: {} identity={}", JacksonUtil.writeAsJsonStr(saveRequest), JacksonUtil.writeAsJsonStr(identity));
            bmOpenSaveSpAreaThriftService.rebuildAndSave(saveRequest, identity);
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] rebuildAndSave 配送范围保存异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "构建并保存范围:" + e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] rebuildAndSave 配送范围保存异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "配送范围保存异常:" + e.getMessage());
        }
    }

    public void validateSave4Online(SpAreaSave4OnlineRequest param, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] validateSave4Online 客户切换不下线校验 param: {} identity={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(identity));
            bmOpenSaveSpAreaThriftService.validateSave4Online(param, identity);
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] validateSave4Online 客户切换不下线校验异常, 参数 param: {}", JacksonUtil.writeAsJsonStr(param), e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "客户切换不下线校验异常:" + e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] validateSave4Online 客户切换不下线校验异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "客户切换不下线校验异常:" + e.getMessage());
        }
    }

    public void save4Online(SpAreaSave4OnlineRequest param, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] save4Online 客户切换不下线 param: {} identity={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(identity));
            bmOpenSaveSpAreaThriftService.save4Online(param, identity);
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] save4Online 客户切换不下线异常, 参数 param: {}", JacksonUtil.writeAsJsonStr(param), e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "客户切换不下线异常:" + e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] save4Online 客户切换不下线异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "客户切换不下线异常:" + e.getMessage());
        }
    }

    public SpAreaNeedSignResponse isNeedSign(SpAreaNeedSignRequest param, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] isNeedSign 参数 param: {} identity={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(identity));
            SpAreaNeedSignResponse needSign = bmOpenSignSpAreaThriftService.isNeedSign(param, identity);
            log.info("配送范围服务 [范围平台化] isNeedSign finish wmPoiId: {}", param.getPoiId());
            return needSign;
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] isNeedSign 判断是否需要签约异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "配送范围服务 [范围平台化]判断是否需要签约异常:" + e.getMsg());
        }  catch (Exception e) {
            log.info("配送范围服务 [范围平台化] isNeedSign 判断是否需要签约异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "配送范围服务 [范围平台化]判断是否需要签约异常:" + e.getMessage());
        }
    }

    public void notifySignStatusChange(SpAreaSignNotificationRequest param, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] notifySignStatusChange 参数 param: {} identity={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(identity));
            bmOpenSignSpAreaThriftService.notifySignStatusChange(param, identity);
            log.info("配送范围服务 [范围平台化] notifySignStatusChange finish wmPoiId: {}", param.getPoiId());
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] notifySignStatusChange 通知配送范围签约状态变更, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "通知配送范围签约状态变更异常:" + e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] notifySignStatusChange 通知配送范围签约状态变更, e: ", e);
            throw GatewayAdapterException.thriftException(e, "通知配送范围签约状态变更异常:" + e.getMessage());
        }
    }

    public void notifyPoiConfirmed(SpAreaPoiConfirmedNotificationRequest param, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] notifyPoiConfirmed 参数 param: {} identity={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(identity));
            bmOpenSignSpAreaThriftService.notifyPoiConfirmed(param, identity);
            log.info("配送范围服务 [范围平台化] notifyPoiConfirmed finish");
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] notifyPoiConfirmed 签约确认异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "通知范围签约确认:" + e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] notifyPoiConfirmed 签约确认异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "签约确认异常:" + e.getMessage());
        }
    }

    public void checkBeforeEffectPoiSpArea(SpAreaPoiEffectRequest param, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] checkBeforeEffectPoiSpArea 参数 param: {} identity={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(identity));
            bmOpenEffectSpAreaThriftService.checkBeforeEffectPoiSpArea(param, identity);
            log.info("配送范围服务 [范围平台化] checkBeforeEffectPoiSpArea finish");
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] checkBeforeEffectPoiSpArea 范围生效校验异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e,  e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] checkBeforeEffectPoiSpArea 范围生效校验异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "范围生效校验异常:" + e.getMessage());
        }
    }

    public void effectPoiSpArea(SpAreaPoiEffectRequest param, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] effectPoiSpArea 参数 param: {} identity={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(identity));
            bmOpenEffectSpAreaThriftService.effectPoiSpArea(param, identity);
            log.info("配送范围服务 [范围平台化] effectPoiSpArea finish");
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] effectPoiSpArea 范围生效异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "范围生效:" + e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] effectPoiSpArea 范围生效异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "范围生效异常:" + e.getMessage());
        }
    }

    public void stopSpAreaProcess(SpAreaStopProcessRequest param, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] stopSpAreaProcess 参数 param: {} identity={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(identity));
            bmOpenSpAreaProcessChangeThriftService.stopSpAreaProcess(param, identity);
            log.info("配送范围服务 [范围平台化] stopSpAreaProcess finish");
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] stopSpAreaProcess 范围终止流程异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "终止范围流程:" + e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] stopSpAreaProcess 范围终止流程异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "范围终止流程异常:" + e.getMessage());
        }
    }

    public void commitAuditSpArea(SpAreaCommitAuditRequest request) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] commitAuditSpArea 参数 request: {}", JacksonUtil.writeAsJsonStr(request));
            bmOpenAuditSpAreaThriftService.commitAuditSpArea(request);
            log.info("配送范围服务 [范围平台化] commitAuditSpArea finish");
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] commitAuditSpArea 范围提审异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "提审范围流程:" + e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] commitAuditSpArea 范围提审异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "范围提审异常:" + e.getMessage());
        }
    }

    /**
     * 范围简洁生效，目前只用于phf生效
     * @param request
     * @param identity
     * @throws GatewayAdapterException
     */
    public void conciseEffectPoiSpArea(SpAreaPoiConciseEffectRequest request, IdentityDTO identity) throws GatewayAdapterException {
        try {
            log.info("配送范围服务 [范围平台化] conciseEffectPoiSpArea 参数 request: {} identity={}", JacksonUtil.writeAsJsonStr(request), JacksonUtil.writeAsJsonStr(identity));
            bmOpenEffectSpAreaThriftService.conciseEffectPoiSpArea(request, identity);
            log.info("配送范围服务 [范围平台化] conciseEffectPoiSpArea finish");
        } catch (BmSpareaException e) {
            log.info("配送范围服务 [范围平台化] conciseEffectPoiSpArea 范围简洁生效异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "范围简洁生效异常:" + e.getMsg());
        } catch (Exception e) {
            log.info("配送范围服务 [范围平台化] conciseEffectPoiSpArea 范围简洁生效异常, e: ", e);
            throw GatewayAdapterException.thriftException(e, "范围简洁生效异常:" + e.getMessage());
        }
    }




}