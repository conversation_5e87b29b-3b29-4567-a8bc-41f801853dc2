package com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.ResultDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.*;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.mission.save.PlatPreSaveCheckResultDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.mission.sign.WmLogisticsContractCancelSignRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.mission.sign.WmLogisticsContractConfirmSignRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfContractSinglePoiSaveRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfPreCheckResponseDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfSaveResponseDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.exception.WmLogisticsContractException;
import com.sankuai.meituan.waimai.logistics.contract.client.service.contractplatform.PhfContractPlatformThriftService;
import com.sankuai.meituan.waimai.logistics.contract.client.service.contractplatform.WmContractPlatformThriftService;
import com.sankuai.meituan.waimai.logistics.contract.client.service.mission.WmLogisticsContractFlowThriftService;
import com.sankuai.meituan.waimai.logistics.contract.client.service.mission.WmLogisticsContractSaveThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-05-11 16:21
 */
@Slf4j
@Service
public class PlatContractFlowThriftServiceAdapter {


    @Resource
    private WmLogisticsContractFlowThriftService wmLogisticsContractFlowThriftService;

    @Resource
    private WmContractPlatformThriftService wmContractPlatformThriftService;

    @Resource
    private WmLogisticsContractSaveThriftService wmLogisticsContractSaveThriftService;

    @Resource
    private PhfContractPlatformThriftService phfContractPlatformThriftService;


    public void oldFlowCancelSign(WmLogisticsContractCancelSignRequestDTO cancelSignRequestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 取消签约 cancelSign request={}", JacksonUtil.writeAsJsonStr(cancelSignRequestDTO));
            wmLogisticsContractFlowThriftService.cancelSign(cancelSignRequestDTO);
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 cancelSign request={}, error", JacksonUtil.writeAsJsonStr(cancelSignRequestDTO), e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同取消签约异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 cancelSign request={}, error", JacksonUtil.writeAsJsonStr(cancelSignRequestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同取消签约异常:" + e.getMessage());
        }
    }

    public SaveResponseDTO tempSaveSinglePoiSelfSettle(SinglePoiSelfSettleSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 tempSaveSinglePoiSelfSettle 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            SaveResponseDTO saveResponseDTO = wmContractPlatformThriftService.tempSaveSinglePoiSelfSettle(requestDTO);
            log.info("平台合同服务 tempSaveSinglePoiSelfSettle 结果 result: {}", JacksonUtil.writeAsJsonStr(saveResponseDTO));
            return saveResponseDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 tempSaveSinglePoiSelfSettle 平台合同单店自入驻异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 tempSaveSinglePoiSelfSettle request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同单店自入驻异常:" + e.getMessage());
        }
    }

    public SaveResponseDTO tempSaveMultiPoiSelfSettle(MultiPoiSelfSettleTempSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 tempSaveMultiPoiSelfSettle 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            SaveResponseDTO saveResponseDTO = wmContractPlatformThriftService.tempSaveMultiPoiSelfSettle(requestDTO);
            log.info("平台合同服务 tempSaveMultiPoiSelfSettle 结果 result: {}", JacksonUtil.writeAsJsonStr(saveResponseDTO));
            return saveResponseDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 tempSaveMultiPoiSelfSettle 平台合同多店自入驻异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 tempSaveMultiPoiSelfSettle request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同多店自入驻异常:" + e.getMessage());
        }
    }

    public SaveResponseDTO tempSaveSinglePoi(SinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 tempSaveSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            SaveResponseDTO saveResponseDTO = wmContractPlatformThriftService.tempSaveSinglePoi(requestDTO);
            log.info("平台合同服务 tempSaveSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(saveResponseDTO));
            return saveResponseDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 tempSaveSinglePoi 平台合同迁移暂存异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 tempSaveSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同迁移暂存异常:" + e.getMessage());
        }
    }


    public SaveResponseDTO saveSinglePoi(SinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 saveSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            SaveResponseDTO saveResponseDTO = wmContractPlatformThriftService.saveSinglePoi(requestDTO);
            log.info("平台合同服务 saveSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(saveResponseDTO));
            return saveResponseDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 saveSinglePoi 平台合同先富单店保存异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 saveSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同先富单店保存异常:" + e.getMessage());
        }
    }

    public SaveResponseDTO saveNewChannelSinglePoi(NewChannelSinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 saveArriveShopSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            SaveResponseDTO saveResponseDTO = wmContractPlatformThriftService.saveNewChannelSinglePoi(requestDTO);
            log.info("平台合同服务 saveArriveShopSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(saveResponseDTO));
            return saveResponseDTO;
        }catch (Exception e) {
            log.info("平台合同服务 saveArriveShopSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同到店自取保存异常:" + e.getMessage());
        }
    }


    public ResultDTO saveCheckNewChannelSinglePoi(NewChannelSinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 saveCheckArriveShopSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.saveCheckNewChannelSinglePoi(requestDTO);
            log.info("平台合同服务 saveCheckArriveShopSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        }catch (Exception e) {
            log.info("平台合同服务 saveCheckArriveShopSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同到店自取保存校验异常:" + e.getMessage());
        }
    }


    public ResultDTO saveCheckSinglePoi(SinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 saveCheckSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.saveCheckSinglePoi(requestDTO);
            log.info("平台合同服务 saveCheckSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 saveCheckSinglePoi 平台合同先富单店保存校验异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 saveCheckSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同先富单店保存校验异常:" + e.getMessage());
        }
    }

    public SaveResponseDTO saveAutoFillSinglePoi(SinglePoiSaveAutoFillRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 saveAutoFillSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            SaveResponseDTO saveResponseDTO = wmContractPlatformThriftService.saveAutoFillSinglePoi(requestDTO);
            log.info("平台合同服务 saveAutoFillSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(saveResponseDTO));
            return saveResponseDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 saveAutoFillSinglePoi 平台合同修改和换签保存校验异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同修改和换签保存校验异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 saveAutoFillSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同修改和换签保存校验异常:" + e.getMessage());
        }
    }

    public PlatPreSaveCheckResultDTO bdSettleSavePreNotice(SinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 bdSettleSavePreNotice 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            PlatPreSaveCheckResultDTO resultDTO = wmLogisticsContractSaveThriftService.platPreSaveCheck(requestDTO);
            log.info("平台合同服务 bdSettleSavePreNotice 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 bdSettleSavePreNotice 先富保存前置提示异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "先富保存前置提示异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 bdSettleSavePreNotice request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "先富保存前置提示异常:" + e.getMessage());
        }
    }

    public ResultDTO commitAuditSinglePoi(SinglePoiCommitAuditRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 commitAuditSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.commitAuditSinglePoi(requestDTO);
            log.info("平台合同服务 commitAuditSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 commitAuditSinglePoi 平台合同单店提审异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同单店提审异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 commitAuditSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同单店提审异常:" + e.getMessage());
        }
    }

    public QueryCommitTaskResponseDTO queryCommitTask(QueryCommitTaskRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 queryCommitTask 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            QueryCommitTaskResponseDTO resultDTO = wmContractPlatformThriftService.queryCommitTask(requestDTO);
            log.info("平台合同服务 queryCommitTask 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 queryCommitTask 平台合同单店提审异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同查询待提审任务异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 queryCommitTask request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同查询待提审任务异常:" + e.getMessage());
        }
    }

    public ResultDTO processStopSinglePoi(SinglePoiProcessStopRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 processStopSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.processStopSinglePoi(requestDTO);
            log.info("平台合同服务 processStopSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 processStopSinglePoi 终止流程异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同终止流程异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 processStopSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同终止流程异常:" + e.getMessage());
        }
    }

    public ResultDTO saveCheckSinglePoiSelfSettle(SinglePoiSelfSettleSaveCheckRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 saveCheckSinglePoiSelfSettle 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.saveCheckSinglePoiSelfSettle(requestDTO);
            log.info("平台合同服务 saveCheckSinglePoiSelfSettle 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 saveCheckSinglePoiSelfSettle 暂存信息校验异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同自入驻校验异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 saveCheckSinglePoiSelfSettle request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同自入驻校验异常:" + e.getMessage());
        }
    }

    public ResultDTO applyWaitSignNoticeSinglePoi(SinglePoiApplyWaitSignSuccessNoticeRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 applyWaitSignNoticeSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.applyWaitSignNoticeSinglePoi(requestDTO);
            log.info("平台合同服务 applyWaitSignNoticeSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 applyWaitSignNoticeSinglePoi 通知待签约结果异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同待发起签约结果通知异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 applyWaitSignNoticeSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同自入驻校验异常:" + e.getMessage());
        }
    }

    public ResultDTO applySignNoticeSinglePoi(SinglePoiApplySignSuccessNoticeRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 applySignNoticeSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.applySignNoticeSinglePoi(requestDTO);
            log.info("平台合同服务 applySignNoticeSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 applySignNoticeSinglePoi 通知签约结果异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同发起签约结果通知异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 applySignNoticeSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同发起签约结果通知异常:" + e.getMessage());
        }
    }

    public ResultDTO waitEffectSinglePoi(SinglePoiWaitEffectRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 waitEffectSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.waitEffectSinglePoi(requestDTO);
            log.info("平台合同服务 waitEffectSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 waitEffectSinglePoi 平台合同待生效异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同待生效异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 applySignNoticeSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同待生效异常:" + e.getMessage());
        }
    }


    public ResultDTO effectSinglePoi(SinglePoiEffectRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 effectSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.effectSinglePoi(requestDTO);
            log.info("平台合同服务 effectSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 effectSinglePoi 平台合同生效异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同生效异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 effectSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同生效异常:" + e.getMessage());
        }
    }

    public ResultDTO effectCheckSinglePoi(SinglePoiEffectRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 effectCheckSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.effectCheckSinglePoi(requestDTO);
            log.info("平台合同服务 effectCheckSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 effectCheckSinglePoi 平台合同生效校验异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同生效校验异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 effectCheckSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同生效校验异常:" + e.getMessage());
        }
    }


    public ResultDTO confirmSignSinglePoi(SinglePoiConfirmSignRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 confirmSignSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.confirmSignSinglePoi(requestDTO);
            log.info("平台合同服务 confirmSignSinglePoi 结果 result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 confirmSignSinglePoi 签约确认异常, e: ", e);
            throw GatewayAdapterException.businessException(e.getCode(), e, "平台合同签约确认异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 confirmSignSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "平台合同签约确认异常:" + e.getMessage());
        }
    }

    public NeedSignResponseDTO needSignSinglePoiWrap(SinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 needSignSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            NeedSignResponseDTO needSign = wmContractPlatformThriftService.needSignSinglePoiWrap(requestDTO);
            log.info("平台合同服务 needSignSinglePoi needSign: {}", JacksonUtil.writeAsJsonStr(needSign));
            return needSign;
        } catch (WmLogisticsContractException | TException e) {
            log.info("平台合同服务 needSignSinglePoi 判断是否需要签约异常, e: ", e);
            throw GatewayAdapterException.businessException(-1, e, "判断是否需要签约异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 needSignSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "判断是否需要签约异常:" + e.getMessage());
        }
    }

    public NeedSignResponseDTO needSignSinglePoi4NewChannel(NewChannelSinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 needSignSinglePoi4NewChannel 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            NeedSignResponseDTO needSign = wmContractPlatformThriftService.needSignSinglePoi4NewChannel(requestDTO);
            log.info("平台合同服务 needSignSinglePoi4NewChannel needSign: {}", JacksonUtil.writeAsJsonStr(needSign));
            return needSign;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 needSignSinglePoi4NewChannel 判断是否需要签约异常, e: ", e);
            throw GatewayAdapterException.businessException(-1, e, "判断是否需要签约异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 needSignSinglePoi4NewChannel request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "判断是否需要签约异常:" + e.getMessage());
        }
    }

    public void confirmSign(WmLogisticsContractConfirmSignRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 原 confirmSign 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            wmLogisticsContractFlowThriftService.confirmSign(requestDTO);
            log.info("平台合同服务 原 confirmSign finish, param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 confirmSign 签约确认异常, e: ", e);
            throw GatewayAdapterException.businessException(-1, e, "签约确认异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 confirmSign request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "签约确认异常:" + e.getMessage());
        }
    }

    public ResultDTO saveCheckAutoFillSinglePoi(SinglePoiSaveAutoFillRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 saveCheckAutoFillSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.saveCheckAutoFillSinglePoi(requestDTO);
            log.info("平台合同服务 saveCheckAutoFillSinglePoi finish. result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 saveCheckAutoFillSinglePoi 按场景更新和换签校验异常, e: ", e);
            throw GatewayAdapterException.businessException(-1, e, "按场景更新和换签校验异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 saveCheckAutoFillSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "按场景更新和换签校验异常:" + e.getMessage());
        }
    }

    public SavePreviewResponseDTO previewSaveSinglePoi(SinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 previewSaveSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            SavePreviewResponseDTO resultDTO = wmContractPlatformThriftService.previewSaveSinglePoi(requestDTO);
            log.info("平台合同服务 previewSaveSinglePoi finish. result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 previewSaveSinglePoi 预览单店保存异常, e: ", e);
            throw GatewayAdapterException.businessException(-1, e, "预览单店保存异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 previewSaveSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "预览单店保存异常:" + e.getMessage());
        }
    }

    public SavePreviewResponseDTO previewSaveAutoFillSinglePoi(SinglePoiSaveAutoFillRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 previewSaveAutoFillSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            SavePreviewResponseDTO resultDTO = wmContractPlatformThriftService.previewSaveAutoFillSinglePoi(requestDTO);
            log.info("平台合同服务 previewSaveAutoFillSinglePoi finish. result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 previewSaveAutoFillSinglePoi 预览批量平台异常, e: ", e);
            throw GatewayAdapterException.businessException(-1, e, "预览批量平台异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 previewSaveAutoFillSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "预览批量平台异常:" + e.getMessage());
        }
    }

    public List<BatchSubmitResponseDTO> multiCommitAudit(MultiPoiSelfSettleCommitAuditRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 multiCommitAudit 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            List<BatchSubmitResponseDTO> resultDTO = wmContractPlatformThriftService.commitAuditMultiPoiSelfSettle(requestDTO);
            log.info("平台合同服务 multiCommitAudit finish. result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 multiCommitAudit 多店提审异常, e: ", e);
            throw GatewayAdapterException.businessException(-1, e, "多店提审异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 multiCommitAudit request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "多店提审异常:" + e.getMessage());
        }
    }

    public List<MultiPoiSettleSaveCheckResponseDTO> saveCheckMultiPoiSettle(MultiPoiSettleSaveCheckRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 saveCheckMultiPoiSettle 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            List<MultiPoiSettleSaveCheckResponseDTO> resultDTO = wmContractPlatformThriftService.saveCheckMultiPoiSettle(requestDTO);
            log.info("平台合同服务 saveCheckMultiPoiSettle finish. result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 saveCheckMultiPoiSettle 多店提审校验异常, e: ", e);
            throw GatewayAdapterException.businessException(-1, e, "多店提审校验异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 saveCheckMultiPoiSettle request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "多店提审校验异常:" + e.getMessage());
        }
    }

    public ResultDTO reSendWmPoiAllFeeConfirmInfo(SinglePoiResendMsgRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 reSendWmPoiAllFeeConfirmInfo 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.reSendWmPoiAllFeeConfirmInfo(requestDTO);
            log.info("平台合同服务 reSendWmPoiAllFeeConfirmInfo finish. result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 reSendWmPoiAllFeeConfirmInfo 重发短信异常, e: ", e);
            throw GatewayAdapterException.businessException(-1, e, "重发短信异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 reSendWmPoiAllFeeConfirmInfo request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "重发短信异常:" + e.getMessage());
        }
    }

    public ResultDTO bizCheckAfterSave(BizCheckAfterSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 bizCheckAfterSave 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            ResultDTO resultDTO = wmContractPlatformThriftService.bizCheckAfterSave(requestDTO);
            log.info("平台合同服务 bizCheckAfterSave finish. result: {}", JacksonUtil.writeAsJsonStr(resultDTO));
            return resultDTO;
        } catch (WmLogisticsContractException e) {
            log.info("平台合同服务 bizCheckAfterSave 保存后校验异常, e: ", e);
            throw GatewayAdapterException.businessException(-1, e, "保存后校验异常：" + e.getMessage());
        } catch (Exception e) {
            log.info("平台合同服务 bizCheckAfterSave request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "保存后校验异常:" + e.getMessage());
        }
    }

    //用于在所有可能发生合同数据变更的时候，通知一下CBase和TSP拉取一次最新的sessionId和cid
    public void gatewayProcessDoneCallback(Long wmPoiId) {
        try {
            log.info("平台合同服务 gatewayProcessDoneCallback 通知流程/事件完成 wmPoiId: {}", JacksonUtil.writeAsJsonStr(wmPoiId));
            wmContractPlatformThriftService.gatewayProcessDoneCallback(wmPoiId);
            log.info("平台合同服务 gatewayProcessDoneCallback 通知流程/事件完成 finish");
        } catch (Exception e) {
            log.warn("平台合同服务 gatewayProcessDoneCallback 通知流程/事件完成 异常, param: {}, e: ", wmPoiId, e);
        }
    }


    public boolean preCheckPhfNeedReapply(PhfContractSinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 preCheckNeedReapply 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            Boolean needReapply = phfContractPlatformThriftService.preCheckNeedReapply(requestDTO);
            log.info("平台合同服务 preCheckNeedReapply finish. result: {}", JacksonUtil.writeAsJsonStr(needReapply));
            return needReapply;
        } catch (Exception e) {
            log.warn("平台合同服务 preCheckNeedReapply request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "判断是否需要重新发起异常:" + e.getMessage());
        }
    }

    public PhfPreCheckResponseDTO preCheckSavePhfSinglePoi(PhfContractSinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 preCheckSavePhfSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            PhfPreCheckResponseDTO responseDTO = phfContractPlatformThriftService.preCheckSavePhfSinglePoi(requestDTO);
            log.info("平台合同服务 preCheckSavePhfSinglePoi finish. result: {}", JacksonUtil.writeAsJsonStr(responseDTO));
            return responseDTO;
        } catch (WmLogisticsContractException e){
            log.warn("平台合同服务 preCheckSavePhfSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.businessException(-1, e, "保存前校验异常：" + e.getMessage());
        } catch (Exception e) {
            log.warn("平台合同服务 preCheckSavePhfSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "保存前校验异常:" + e.getMessage());
        }
    }


    public PhfSaveResponseDTO savePhfSinglePoi(PhfContractSinglePoiSaveRequestDTO requestDTO) throws GatewayAdapterException {
        try {
            log.info("平台合同服务 savePhfSinglePoi 参数 param: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            PhfSaveResponseDTO  responseDTO = phfContractPlatformThriftService.savePhfSinglePoi(requestDTO);
            log.info("平台合同服务 savePhfSinglePoi finish. result: {}", JacksonUtil.writeAsJsonStr(responseDTO));
            return responseDTO;
        } catch (WmLogisticsContractException e){
            log.warn("平台合同服务 savePhfSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.businessException(-1, e, "保存异常：" + e.getMessage());
        } catch (Exception e) {
            log.warn("平台合同服务 savePhfSinglePoi request={}, error", JacksonUtil.writeAsJsonStr(requestDTO), e);
            throw GatewayAdapterException.thriftException(e, "保存异常:" + e.getMessage());
        }

    }

}
