package com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service;

import com.dianping.pigeon.util.CollectionUtils;
import com.sankuai.meituan.health.merchant.delivery.thrift.client.MedicalFeeModeThriftService;
import com.sankuai.meituan.health.merchant.delivery.thrift.dto.MedicalFeeModeQueryDTO;
import com.sankuai.meituan.health.merchant.delivery.thrift.dto.feeMode.QueryHopeFeeModeDTO;
import com.sankuai.meituan.health.merchant.delivery.thrift.response.shepherd.CommonResponse;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.shangou.merchant.logistics.thrift.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class MedicineThriftServiceAdapterService {

    @Autowired
    private MedicalFeeModeThriftService medicalFeeModeThriftService;

    public int queryHopeFeeModeV2(QueryHopeFeeModeDTO queryHopeFeeModeDTO) throws GatewayAdapterException {
        log.info("MedicineThriftServiceAdapterService.queryHopeFeeModeV2#queryDTO:{}", JacksonUtil.writeAsJsonStr(queryHopeFeeModeDTO));
        try {
            CommonResponse<Integer> dataResult = medicalFeeModeThriftService.queryHopeFeeModeV2(queryHopeFeeModeDTO);
            log.info("MedicineThriftServiceAdapterService.queryHopeFeeModeV2#queryDTO:{},result:{}", JacksonUtil.writeAsJsonStr(queryHopeFeeModeDTO), JacksonUtil.writeAsJsonStr(dataResult));
            if (null == dataResult || !dataResult.isSuccess()) {
                throw GatewayAdapterException.businessException(-1, "获取医药门店新期望费率模式时返回失败");
            }
            return dataResult.getData();
        } catch (TException e) {
            log.warn("MedicineThriftServiceAdapterService.queryHopeFeeModeV2#queryDTO:{} 获取医药门店期望费率模式时服务异常", JacksonUtil.writeAsJsonStr(queryHopeFeeModeDTO));
            throw GatewayAdapterException.thriftException(e, e.getMessage());
        }
    }

}
