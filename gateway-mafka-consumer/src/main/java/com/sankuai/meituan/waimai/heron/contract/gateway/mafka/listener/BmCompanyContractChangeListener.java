package com.sankuai.meituan.waimai.heron.contract.gateway.mafka.listener;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.monitor.CatLogMonitor;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.BmCompanyContractChangeEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.utils.DeserializeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.ConsumerListener;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.model.BmCompanyContractChangeMessage;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.model.MessageListenResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.event.NotifyBmCompanyContractChangeParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description: 企客合同变更
 * @author: chenyihao04
 * @create: 2023-11-22 16:52
 */
@Component
@Slf4j
public class BmCompanyContractChangeListener implements ConsumerListener {

    @Resource
    private BmCompanyContractChangeEntry bmCompanyContractChangeEntry;

    @Resource
    private CatLogMonitor catLogMonitor;

    @Override
    public MessageListenResult onMessage(MafkaMessage message, MessagetContext context) {
        String msg = message.getBody().toString();
        log.info("BmCompanyContractChangeListener 企客合同变更消息 on message = {}", msg);
        BmCompanyContractChangeMessage bmCompanyContractChangeMessage = DeserializeUtil.deserialize(msg, BmCompanyContractChangeMessage.class);

        if (Objects.isNull(bmCompanyContractChangeMessage)) {
            log.error("BmCompanyContractChangeListener 反序列化消息失败");
            return MessageListenResult.fail(ConsumeStatus.CONSUME_SUCCESS, "企客合同变更消息反序列化消息失败");
        }
        if (!Objects.equals(bmCompanyContractChangeMessage.getHandleGroup(), BmCompanyContractChangeMessage.HandleGroupEnum.GATEWAY.name())) {//只消费网关需要消费的消息
            catLogMonitor.catMonitor("bm_company_contract_change_not_gateway_message", bmCompanyContractChangeMessage.getWmPoiId());//非网关消费的消息会越来越少，随着企客解耦项目全量，最终将不再产生，打个点观察下
            return MessageListenResult.success();
        }

        GatewayResult process;
        try {
            process = bmCompanyContractChangeEntry.process(buildParam(bmCompanyContractChangeMessage));
        } catch (Exception e) {
            catLogMonitor.catMonitor("bm_company_contract_change_gateway_consume_error", bmCompanyContractChangeMessage.getWmPoiId());
            log.error("BmCompanyContractChangeListener 消费异常, 人工排查, message: {}, e: ", msg, e);
            return MessageListenResult.fail(ConsumeStatus.CONSUME_FAILURE, e);
        }
        if (process.getSuccess()) {
            catLogMonitor.catMonitor("bm_company_contract_change_gateway_consume_fail", bmCompanyContractChangeMessage.getWmPoiId());
            return MessageListenResult.success();
        }
        return MessageListenResult.fail(ConsumeStatus.CONSUME_FAILURE, "企客合同变更处理失败");
    }

    private NotifyBmCompanyContractChangeParam buildParam(BmCompanyContractChangeMessage bmCompanyContractChangeMessage) {
        NotifyBmCompanyContractChangeParam notifyBmCompanyContractChangeParam = new NotifyBmCompanyContractChangeParam();
        notifyBmCompanyContractChangeParam.setWmPoiId(bmCompanyContractChangeMessage.getWmPoiId());
        notifyBmCompanyContractChangeParam.setBmCompanyContractId(bmCompanyContractChangeMessage.getBmCompanyContractId());
        notifyBmCompanyContractChangeParam.setCustomerId(bmCompanyContractChangeMessage.getCustomerId());
        return notifyBmCompanyContractChangeParam;
    }
}