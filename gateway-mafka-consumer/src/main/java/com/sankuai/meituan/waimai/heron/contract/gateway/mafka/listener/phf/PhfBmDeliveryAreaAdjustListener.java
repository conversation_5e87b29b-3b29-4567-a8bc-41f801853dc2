package com.sankuai.meituan.waimai.heron.contract.gateway.mafka.listener.phf;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.banma.thrift.deliveryproduct.common.SaleChannelTypeUtils;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.aspect.PhfBusinessService;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.gray.PhfMigrateSaveGrayDecider;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.phf.PhfBmDeliveryAreaAdjustEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.phf.PhfBmDeliveryAreaAdjustParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.ConsumerListener;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.model.PhfBmDeliveryAreaChangeMsg;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.model.MessageListenResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractMigrateStageEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 拼好饭配送区域变更，拼好饭原代码：PhsDeliveryareaDeliveryCoordinatesConsumerListener
 * <AUTHOR>
 * @date 2024/12/6
 */
@Slf4j
@Component
@PhfBusinessService
public class PhfBmDeliveryAreaAdjustListener implements ConsumerListener {

    @Resource
    private PhfBmDeliveryAreaAdjustEntry phfBmDeliveryAreaAdjustEntry;

    @Resource
    private PhfMigrateSaveGrayDecider phfMigrateSaveGrayDecider;

    @Override
    public MessageListenResult onMessage(MafkaMessage message, MessagetContext context) {
        String msg = message.getBody().toString();
        log.info("PhfBmDeliveryAreaAdjustListener 拼好饭配送区域变更 msg:{}", msg);
        if (StringUtils.isBlank(msg)) {
            log.warn("拼好饭配送区域变更消息为空");
            return MessageListenResult.success();
        }
        PhfBmDeliveryAreaChangeMsg phfBmDeliveryAreaChangeMsg = JSONObject.parseObject(msg, PhfBmDeliveryAreaChangeMsg.class);
        if (phfBmDeliveryAreaChangeMsg == null) {
            log.warn("拼好饭配送区域变更消息解析为空");
            return MessageListenResult.success();
        }
        if (phfBmDeliveryAreaChangeMsg.getAreaId() <= 0
                || MapUtils.isEmpty(phfBmDeliveryAreaChangeMsg.getAffectedPoiIds())
                || !phfBmDeliveryAreaChangeMsg.getAffectedPoiIds().containsKey(SaleChannelTypeUtils.phfChannel().getCode())) {
            log.warn("拼好饭配送区域变更消息不完整");
            return MessageListenResult.success();
        }
        List<Long> wmPoiIdList = phfBmDeliveryAreaChangeMsg.getAffectedPoiIds().get(SaleChannelTypeUtils.phfChannel().getCode());
        List<Long> failedWmPoiIdList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdList) {
            if (wmPoiId == null || wmPoiId <= 0) {
                continue;
            }
            if (!ContractMigrateStageEnum.MIGRATE_NEW.equals(phfMigrateSaveGrayDecider.judgePoiCurrentMigrateStage(wmPoiId))) {
                log.info("拼好饭配送区域变更消息 门店不在灰度中 wmPoiId:{}", wmPoiId);
                continue;
            }
            log.info("拼好饭配送区域变更消息 开始处理 phfBmDeliveryAreaChangeMsg:{}", JacksonUtil.writeAsJsonStr(phfBmDeliveryAreaChangeMsg));
            try {
                PhfBmDeliveryAreaAdjustParam param = PhfBmDeliveryAreaAdjustParam.builder()
                        .wmPoiId(wmPoiId)
                        .areaId(phfBmDeliveryAreaChangeMsg.getAreaId())
                        .build();
                phfBmDeliveryAreaAdjustEntry.process(param);
            } catch (Exception e) {
                log.warn("拼好饭配送区域变更消息有门店 处理失败 wmPoiId:{}", wmPoiId, e);
                failedWmPoiIdList.add(wmPoiId);
            }
        }
        if (CollectionUtils.isNotEmpty(failedWmPoiIdList)) {
            log.warn("拼好饭配送区域变更消息有门店 处理失败 wmPoiIdList:{}", failedWmPoiIdList);
            return MessageListenResult.fail(ConsumeStatus.RECONSUME_LATER, "存在门店处理失败");
        }
        return MessageListenResult.success();
    }

}
