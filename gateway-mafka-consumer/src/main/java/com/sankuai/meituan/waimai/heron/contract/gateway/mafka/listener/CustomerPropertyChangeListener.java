package com.sankuai.meituan.waimai.heron.contract.gateway.mafka.listener;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.WmCustomerServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.WmPoiQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.TimeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.SessionCategoryBuildHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.CoreUnbindNewZsEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.entry.impl.UnbindBcsEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.LogisticsTypeChangeParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.event.param.UnbindLogisticsInfoParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.ConsumerListener;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.model.CustomerPropertyChangeBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.model.MessageListenResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;

/**
 * @description: 客户属性变更消息
 * @author: chenyihao04
 * @create: 2024-05-17 14:02
 */
@Component
@Slf4j
public class CustomerPropertyChangeListener implements ConsumerListener {

    @Resource
    private UnbindBcsEntry unbindBcsEntry;

    @Resource
    private SessionCategoryBuildHelper sessionCategoryBuildHelper;

    @Resource
    private WmCustomerServiceAdapter wmCustomerServiceAdapter;

    @Resource
    private WmPoiQueryThriftServiceAdapter wmPoiQueryThriftServiceAdapter;

    @Resource
    private CoreUnbindNewZsEntry coreUnbindNewZsEntry;

    private static final Integer ELE_SIGN = 2;

    private static final String BCS_LOGISTICS_ID = "1009";
    private static final String DRONE_LOGISTICS_ID = "1008";

    @Override
    public MessageListenResult onMessage(MafkaMessage message, MessagetContext context) {
        String body = String.valueOf(message.getBody());
        log.info("CustomerPropertyChangeListener 客户属性变更消息 on message: {}", JacksonUtil.writeAsJsonStr(message));
        if (StringUtils.isEmpty(body)) {
            log.warn("topic:{}, partition:{}, offset:{}, body is empty!!!", message.getTopic(), message.getParttion(), message.getOffset());
            return MessageListenResult.fail(ConsumeStatus.CONSUME_SUCCESS, "客户属性变更消息为空");
        }

        CustomerPropertyChangeBo customerPropertyChangeBo = JacksonUtil.readValue(body, CustomerPropertyChangeBo.class);
        if (null == customerPropertyChangeBo) {
            log.error("CustomerPropertyChangeListener 解析消息体失败");
            return MessageListenResult.fail(ConsumeStatus.CONSUME_SUCCESS, "客户属性变更消息解析消息体失败");
        }
        if (messageShouldBeIgnored(customerPropertyChangeBo)) {
            return MessageListenResult.success();
        }

        HeronContractOperator operator = HeronContractOperator.builder().opId(-1L).opName("纸质客户配送方式约束").build();

        List<GatewayResult> gatewayResultList = new ArrayList<>();
        try {
            List<Long> wmPoiIdsByWmCustomerId = wmCustomerServiceAdapter.getWmPoiIdsByWmCustomerId(customerPropertyChangeBo.getId());

            List<Long> bcsPoiList = getBcsPoiList(wmPoiIdsByWmCustomerId);
            List<Long> dronePoiList = getDronePoiList(wmPoiIdsByWmCustomerId);
            for (Long wmPoiId : bcsPoiList) {
                UnbindLogisticsInfoParam unbindLogisticsInfoParam = new UnbindLogisticsInfoParam();
                unbindLogisticsInfoParam.setWmPoiId(wmPoiId);
                unbindLogisticsInfoParam.setOperator(operator);
                
                unbindLogisticsInfoParam.setSessionCategory(SessionCategoryEnum.FRUIT_TOGETHER);
                unbindLogisticsInfoParam.setRemark("纸质客户不支持班次送");
                GatewayResult gatewayResult = unbindBcsEntry.process(unbindLogisticsInfoParam);
                gatewayResultList.add(gatewayResult);
                log.info("CustomerPropertyChangeListener 解绑班次送 wmPoiId: {}, result: {}", wmPoiId, JacksonUtil.writeAsJsonStr(gatewayResult));
            }
            for (Long wmPoiId : dronePoiList) {
                if (coreUnbindNewZsEntry.isNeedUnbindDrone(wmPoiId, 0)) {
                    LogisticsTypeChangeParam logisticsTypeChangeParam = new LogisticsTypeChangeParam();
                    logisticsTypeChangeParam.setWmPoiId(wmPoiId);
                    logisticsTypeChangeParam.setOperator(operator);
                    
                    logisticsTypeChangeParam.setSessionCategory(SessionCategoryEnum.DRONE);
                    GatewayResult gatewayResult = coreUnbindNewZsEntry.process(logisticsTypeChangeParam);
                    gatewayResultList.add(gatewayResult);
                    log.info("CustomerPropertyChangeListener 解绑无人机 wmPoiId: {}, result: {}", wmPoiId, JacksonUtil.writeAsJsonStr(gatewayResult));
                }
            }
        } catch (Exception e) {
            log.error("CustomerPropertyChangeListener 消费异常, 人工排查, message: {}, e: ", JacksonUtil.writeAsJsonStr(message), e);
            return MessageListenResult.fail(ConsumeStatus.RECONSUME_LATER, "客户属性变更处理失败");
        }

        if (gatewayResultList.stream().allMatch(GatewayResult::getSuccess)) {
            return MessageListenResult.success();
        } else {
            return MessageListenResult.fail(ConsumeStatus.RECONSUME_LATER, "客户属性变更处理失败");
        }
    }

    private boolean messageShouldBeIgnored(CustomerPropertyChangeBo customerPropertyChangeBo) {
        if (Objects.isNull(customerPropertyChangeBo) || Objects.isNull(customerPropertyChangeBo.getData())) {
            return true;
        }
        if (StringUtils.equals(customerPropertyChangeBo.getType(), "insert")) {
            return true;
        }
        return Objects.equals(customerPropertyChangeBo.getData().getSignMode(), ELE_SIGN);
    }

    private List<Long> getBcsPoiList(List<Long> wmPoiIdList) throws GatewayAdapterException {
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return new ArrayList<>();
        }
        List<List<Long>> wmPoiIdPartitionList = Lists.partition(wmPoiIdList, 200);//接口支持一次查300

        List<Long> bcsPoiList = new ArrayList<>();
        for (List<Long> idList : wmPoiIdPartitionList) {
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftServiceAdapter.batchGetWmPoiAggre(idList, ImmutableSet.of(WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_WM_LOGISTICS_IDS));
            List<Long> collect = wmPoiAggreList.stream()
                    .filter(poiAggre -> StringUtils.isNotBlank(poiAggre.getWm_logistics_ids()))
                    .filter(poiAggre -> Arrays.stream(poiAggre.getWm_logistics_ids().split(","))
                            .anyMatch(logisticsId -> StringUtils.equals(logisticsId, BCS_LOGISTICS_ID)))
                    .map(WmPoiAggre::getWm_poi_id)
                    .collect(Collectors.toList());
            bcsPoiList.addAll(collect);
        }
        return bcsPoiList;
    }

    private List<Long> getDronePoiList(List<Long> wmPoiIdList) throws GatewayAdapterException {
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return new ArrayList<>();
        }
        List<List<Long>> wmPoiIdPartitionList = Lists.partition(wmPoiIdList, 200);//接口支持一次查300

        List<Long> dronePoiList = new ArrayList<>();
        for (List<Long> idList : wmPoiIdPartitionList) {
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftServiceAdapter.batchGetWmPoiAggre(idList, ImmutableSet.of(WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_WM_LOGISTICS_IDS));
            List<Long> collect = wmPoiAggreList.stream()
                    .filter(poiAggre -> StringUtils.isNotBlank(poiAggre.getWm_logistics_ids()))
                    .filter(poiAggre -> Arrays.stream(poiAggre.getWm_logistics_ids().split(","))
                            .anyMatch(logisticsId -> StringUtils.equals(logisticsId, DRONE_LOGISTICS_ID)))
                    .map(WmPoiAggre::getWm_poi_id)
                    .collect(Collectors.toList());
            dronePoiList.addAll(collect);
        }
        return dronePoiList;
    }


}