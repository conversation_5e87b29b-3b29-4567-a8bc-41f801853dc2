package com.sankuai.meituan.waimai.heron.contract.gateway.mafka.consumer.phf;

import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.AbstractConsumer;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.listener.phf.PhfPerfFeeChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/12/6
 */
@Component
@Slf4j
public class PhfPerfFeeChangeConsumer  extends AbstractConsumer {

    @Resource
    private PhfPerfFeeChangeListener phfPerfFeeChangeListener;

    @Autowired
    public void init() {
        log.info("PhfPerfFeeChangeConsumer init");
        super.setNameSpace("waimai");
        super.setAppKey("com.sankuai.heron.contract.gateway");
        super.setTopic("banma.finance.phf.scheme.notic");
        super.setGroup("banma.finance.phf.scheme.notice.gateway.phf");
        super.setConsumerListener(phfPerfFeeChangeListener);
    }

}
