package com.sankuai.meituan.waimai.heron.contract.gateway.mafka.monitor;

import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.alarm.AlarmLevel;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.alarm.GatewayAlarmBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.alarm.GatewayAlarmUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.model.MessageListenResult;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/7/19
 */
@Component
@Aspect
@Slf4j
public class ListenerConsumeMonitor {



    @Pointcut(value = "execution(* com.sankuai.meituan.waimai.heron.contract.gateway.mafka.ConsumerListener.onMessage(..)) && args(message,context)", argNames = "message,context")
    public void onMessagePointCut(MafkaMessage message, MessagetContext context) {

    }

    @AfterReturning(pointcut = "onMessagePointCut(message,context)", returning = "result")
    public void monitorConsumeResult(JoinPoint joinPoint, MafkaMessage message, MessagetContext context, MessageListenResult result) throws Throwable {
        String messageBody = Optional.ofNullable(message.getBody()).map(Object::toString).orElse(null);
        String className = joinPoint.getTarget().getClass().getSimpleName();
        log.info("monitorConsumeResult listener: {} 消费消息 messageBody: {} 消费结果 result: {}", className, messageBody,  JacksonUtil.writeAsJsonStr(result));
        try {
            if (!result.getSuccess()) {
                String alarmMessage = "错误结果为：" + JacksonUtil.writeAsJsonStr(result);
                String alarmTitle = "[" + className + "]消费异常";
                GatewayAlarmBo gatewayAlarmBo = GatewayAlarmBo.builder()
                        .alarmLevel(AlarmLevel.P1)
                        .alarmMessage(alarmMessage)
                        .alarmTitle(alarmTitle)
                        .traceId(Tracer.id())
                        .param(messageBody)
                        .exception(result.getException())
                        .build();
                GatewayAlarmUtil.dxAlarm(gatewayAlarmBo);
            }

        } catch (Exception e) {
            log.error("monitorConsumeResult error listener: {} messageBody: {}", className, messageBody, e);
        }

    }


}
