package com.sankuai.meituan.waimai.heron.contract.gateway.mafka.consumer;

import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.AbstractConsumer;
import com.sankuai.meituan.waimai.heron.contract.gateway.mafka.listener.AreaAuditResultCallbackListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 范围审核结果消息消费
 * @author: chenyihao04
 * @create: 2023-05-10 11:20
 */
@Component
@Slf4j
public class AreaNewAuditResultCallbackConsumer extends AbstractConsumer {

    @Resource
    private AreaAuditResultCallbackListener areaAuditResultCallbackListener;

    @Autowired
    public void init() {
        log.info("AreaAuditResultCallbackConsumer init");
        super.setNameSpace("waimai");
        super.setAppKey("com.sankuai.heron.contract.gateway");
        super.setTopic("bm.poi.sp.area.audit.result.gateway");
        super.setGroup("waimai.bm.poi.sp.area.audit.result.gateway.consumer");
        super.setConsumerListener(areaAuditResultCallbackListener);
    }

}