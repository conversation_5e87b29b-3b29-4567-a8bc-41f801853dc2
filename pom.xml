<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>xframe-starter-parent</artifactId>
        <groupId>com.meituan.xframe</groupId>
        <!--XFrame产品版本：https://km.sankuai.com/custom/onecloud/page/133516477-->
        <version>********</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>合同网关</description>
    <groupId>com.sankuai.meituan.waimai.heron</groupId>
    <artifactId>waimai_e_heron_contract_gateway</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>waimai_e_heron_contract_gateway</name>

    <modules>
        <module>gateway-thrift-service</module>
        <module>gateway-thrift-client</module>
        <module>gateway-query</module>
        <module>gateway-mafka-consumer</module>
        <module>gateway-flow</module>
        <module>gateway-event</module>
        <module>gateway-common</module>
        <module>gateway-basic-service</module>
        <module>gateway-adapter</module>
        <module>gateway-core</module>
        <module>gateway-open-sdk</module>
    </modules>

    <properties>
<!--        <skipTests>true</skipTests>-->
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-extend</artifactId>
                <version>1.7.2</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>gateway-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>gateway-basic-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>gateway-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>gateway-core</artifactId>
                <version>${project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>gateway-event</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>gateway-flow</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>gateway-mafka-consumer</artifactId>
                <version>${project.version}</version>

            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>gateway-query</artifactId>
                <version>${project.version}</version>

            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>gateway-thrift-client</artifactId>
                <version>1.0.36</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>gateway-open-sdk</artifactId>
                <version>1.0.5</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>gateway-thrift-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!--新合同系统服务-->
            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron.logistics</groupId>
                <artifactId>logistics-contract-client</artifactId>
                <version>1.1.87</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>poilogistics-client</artifactId>
                <version>1.1.43</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.heron</groupId>
                <artifactId>heron-config-thrift-client</artifactId>
                <version>1.0.20</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai</groupId>
                <artifactId>waimai_service_poilogistics_client</artifactId>
                <version>1.0.67</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.data</groupId>
                        <artifactId>spring-data-redis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.banma.business</groupId>
                <artifactId>banma_service_poi_sp_area_manage_client</artifactId>
                <version>1.9.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>hibernate-validator</artifactId>
                        <groupId>org.hibernate</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>inf-bom</artifactId>
                        <groupId>com.sankuai</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.banma.business</groupId>
                <artifactId>banma_service_poi_sparea_client</artifactId>
                <version>1.0.28</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.banma</groupId>
                <artifactId>banma_open_client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mt-config-api</artifactId>
                        <groupId>com.sankuai.meituan</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-to-slf4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>log4j-to-slf4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.banma.deliverycontract</groupId>
                <artifactId>delivery-contract-platform-process-sdk</artifactId>
                <version>1.0.37</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.banma.deliveryproduct</groupId>
                <artifactId>banma_service_deliveryproduct_client</artifactId>
                <version>1.0.19</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai</groupId>
                <artifactId>graycenter-sdk</artifactId>
                <version>1.0.3.9</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.kv</groupId>
                <artifactId>group-poi</artifactId>
                <version>2.1.17</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.sgmerchant</groupId>
                <artifactId>shangou-merchant-logistics-sdk</artifactId>
                <version>1.2.8</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.customer</groupId>
                <artifactId>waimai_service_customer_client</artifactId>
                <version>2.1.15</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.poibaseinfo</groupId>
                <artifactId>waimai_service_poibaseinfo_client</artifactId>
                <version>1.3.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.poi</groupId>
                <artifactId>waimai_service_poicategory_client</artifactId>
                <version>1.0.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.poitransfer</groupId>
                <artifactId>waimai_service_poitransfer_client</artifactId>
                <version>1.1.6</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.poi</groupId>
                <artifactId>waimai_service_poiquery_client</artifactId>
                <version>1.0.22</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-core</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-context</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.service.mobile</groupId>
                        <artifactId>mtthrift</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.poi</groupId>
                <artifactId>waimai_service_poi_client</artifactId>
                <version>1.9.27</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-core</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-context</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.elasticsearch</groupId>
                        <artifactId>elasticsearch</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.poi</groupId>
                <artifactId>waimai_service_poibizflow_client</artifactId>
                <version>1.1.21</version>
            </dependency>

            <!--zebra start-->
            <!--核心依赖：数据源-->
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-api</artifactId>
                <version>4.1.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-calcite</artifactId>
                <version>4.1.1</version>
            </dependency>
            <!--核心依赖：CAT监控，请务必依赖-->
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-ds-monitor-client</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-tool</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-dao</artifactId>
                <version>4.1.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.xm</groupId>
                <artifactId>xm-pub-api-client</artifactId>
                <version>1.5.8</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.databus</groupId>
                <artifactId>dbusUtils_thrift0.9.2</artifactId>
                <version>0.0.21</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>empty_version</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai</groupId>
                <artifactId>waimai_util</artifactId>
                <version>1.0.6</version>
            </dependency>
            <!-- 医药 获取期望费率模式-->
            <dependency>
                <groupId>com.sankuai.meituan.health</groupId>
                <artifactId>merchant-delivery-sdk</artifactId>
                <version>1.0.26.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.meituan.waimai.heron</groupId>
                        <artifactId>poilogistics-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.audit</groupId>
                <artifactId>waimai_service_audit_client</artifactId>
                <version>1.1.17</version>
                <exclusions>
                    <exclusion>
                        <artifactId>hibernate-validator</artifactId>
                        <groupId>org.hibernate</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>waimai_e_scm_brand_client</artifactId>
                        <groupId>com.sankuai.meituan.waimai.scmbrand</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.7.7</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>3.7.7</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>1.10.19</version>
                <scope>test</scope>
            </dependency>

            <!-- 拼好饭-->
            <dependency>
                <groupId>com.sankuai.waimaipoi.bargain</groupId>
                <artifactId>waimai-service-bargain-client</artifactId>
                <version>1.6.4</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.tsp.phf</groupId>
                <artifactId>tsp_phf_poi_contract_client</artifactId>
                <version>2.0.13-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.phfpoi</groupId>
                <artifactId>tsp_phf_poi_processor_client</artifactId>
                <version>1.0.6-SNAPSHOT</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.8.1</version>
            </plugin>

            <!--    修改父子module version plugin       -->
<!--                   mvn versions:set -DnewVersion=1.0.28-->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>


        </plugins>
    </build>
</project>
