package com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.processing;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import com.google.common.collect.ImmutableSet;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.WmPoiQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.NationalSubsidyPoiHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.BatchApplySignStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.SignItemBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.SessionFlowTerminateFlowStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.sign.BatchApplySignFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.ApplySignFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.SessionFlowTerminateFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractAutoBatchSignParam;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AutoBatchApplySignFlowOrchestrationTest {

    @Mock
    private BatchApplySignFlowStep batchApplySignFlowStep;

    @Mock
    private ApplySignFailFlowStep applySignFailFlowStep;

    @Mock
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Mock
    private WmPoiQueryThriftServiceAdapter wmPoiQueryThriftServiceAdapter;

    @Mock
    private NationalSubsidyPoiHelper nationalSubsidyPoiHelper;

    @Mock
    private SessionFlowTerminateFlowStep sessionFlowTerminateFlowStep;

    @InjectMocks
    private AutoBatchApplySignFlowOrchestration orchestration;

    @Before
    public void setup() {
        // Setup common mocks
        when(sessionFlowTerminateFlowStep.execute(any())).thenReturn(createMockFlowExecResult(false));
    }

    private HeronContractAutoBatchSignParam createParam() {
        HeronContractOperator operator = new HeronContractOperator();
        operator.setOpId(1L);
        operator.setOpName("test");
        HeronContractAutoBatchSignParam param = new HeronContractAutoBatchSignParam();
        param.setOperator(operator);
        param.setWmPoiIdList(Arrays.asList(1L, 2L));
        return param;
    }

    private HeronContractGatewaySession createSession() {
        HeronContractGatewaySession session = new HeronContractGatewaySession();
        session.setSessionLeafId(100L);
        session.setWmPoiId(1L);
        session.setStatus(ContractSessionStatusEnum.AUDIT_PASS);
        session.setSessionCategory(SessionCategoryEnum.CORE);
        return session;
    }

    private FlowExecResult createMockFlowExecResult(boolean isFail) {
        FlowExecResult result = new FlowExecResult();
        result.setSuccess(!isFail);
        if (isFail) {
            result.setMessage("Mock failure message");
            result.setFailCauseDomain(SubDomainEnum.GATEWAY);
        }
        return result;
    }

    @Test
    public void testFlowWhenWmPoiQueryThrowsException() throws Throwable {
        // Arrange
        HeronContractAutoBatchSignParam param = createParam();
        param.setScene(ContractCreateAndUpdateSceneEnum.HQ_ADD_NATIONAL_SUBSIDY_AGGREMENT);
        // Mock WmPoiQueryThriftServiceAdapter to throw exception
        when(wmPoiQueryThriftServiceAdapter.batchGetWmPoiAggre(anyList(), any())).thenThrow(new GatewayAdapterException(500, false, "query failed"));
        // Mock session retrieval
        HeronContractGatewaySession mockSession = createSession();
        when(heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(anyLong(), any())).thenReturn(mockSession);
        // Mock sessionFlowTerminateFlowStep execution
        when(sessionFlowTerminateFlowStep.execute(any())).thenReturn(createMockFlowExecResult(false));
        // Act
        OrchestrationExecResult result = orchestration.flow(param);
        // Assert
        assertFalse(result.getSuccess());
        verify(sessionFlowTerminateFlowStep, times(param.getWmPoiIdList().size())).execute(any());
    }

    @Test
    public void testFlowWhenNationalSubsidyValidationFails() throws Throwable {
        // Arrange
        HeronContractAutoBatchSignParam param = createParam();
        param.setScene(ContractCreateAndUpdateSceneEnum.HQ_ADD_NATIONAL_SUBSIDY_AGGREMENT);
        // Mock WmPoiQueryThriftServiceAdapter
        WmPoiAggre mockPoiAggre = new WmPoiAggre();
        mockPoiAggre.setBiz_org_code(1);
        mockPoiAggre.setLabel_ids("1,2,3");
        when(wmPoiQueryThriftServiceAdapter.batchGetWmPoiAggre(anyList(), any())).thenReturn(Collections.singletonList(mockPoiAggre));
        // Mock NationalSubsidyPoiHelper
        when(nationalSubsidyPoiHelper.isHQSupplierPoi(anyInt(), anyList())).thenReturn(false);
        // Mock session retrieval
        HeronContractGatewaySession mockSession = createSession();
        when(heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(anyLong(), any())).thenReturn(mockSession);
        // Mock batch apply sign step
        // Act
        OrchestrationExecResult result = orchestration.flow(param);
        // Assert
        assertFalse(result.getSuccess());
        verify(sessionFlowTerminateFlowStep, times(param.getWmPoiIdList().size())).execute(any());
    }

    @Test
    public void testFlowWhenNoValidSessionsFound() throws Throwable {
        // Arrange
        HeronContractAutoBatchSignParam param = createParam();
        when(heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(anyLong(), any())).thenReturn(null);
        // Act
        OrchestrationExecResult result = orchestration.flow(param);
        // Assert
        assertTrue(result.getSuccess());
        verify(batchApplySignFlowStep, never()).execute(any());
    }
}
