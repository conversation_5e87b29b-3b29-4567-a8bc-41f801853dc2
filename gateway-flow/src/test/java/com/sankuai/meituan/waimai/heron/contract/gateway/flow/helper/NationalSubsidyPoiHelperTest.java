package com.sankuai.meituan.waimai.heron.contract.gateway.flow.helper;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mockStatic;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.config.MccConfig;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.BizOrgHelper;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.NationalSubsidyPoiHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.*;
import org.mockito.InjectMocks;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidyPoiHelperTest {

    private static final String SUPPLIER_POI_LABEL = "952";

    private static final String HQ_SUPPLIER_POI_LABEL = "954";

    private static final int SHANGOU_BIZ_ORG_CODE = 2;

    private static final int NON_SHANGOU_BIZ_ORG_CODE = 1;

    private NationalSubsidyPoiHelper nationalSubsidyPoiHelper;

    private static final int SG_BIZ_ORG_CODE = 19;

    private static final int WM_BIZ_ORG_CODE = 1;

    private static final String SUPPLIER_LABEL = "952";

    private static final String DEALERS_LABEL = "955";

    @Before
    public void setUp() {
        nationalSubsidyPoiHelper = new NationalSubsidyPoiHelper();
    }

    /**
     * 测试非闪购业务场景
     */
    @Test
    public void testIsHQSupplierPoi_NonShangouBizOrg() throws Throwable {
        // arrange
        List<String> labelList = Arrays.asList(SUPPLIER_POI_LABEL, HQ_SUPPLIER_POI_LABEL);
        try (MockedStatic<BizOrgHelper> bizOrgHelperMock = mockStatic(BizOrgHelper.class);
            MockedStatic<MccConfig> mccConfigMock = mockStatic(MccConfig.class)) {
            bizOrgHelperMock.when(() -> BizOrgHelper.isShangou(NON_SHANGOU_BIZ_ORG_CODE)).thenReturn(false);
            mccConfigMock.when(MccConfig::getSupplierPoiLabel).thenReturn(SUPPLIER_POI_LABEL);
            mccConfigMock.when(MccConfig::getHQSupplierPoiLabel).thenReturn(HQ_SUPPLIER_POI_LABEL);
            // act
            boolean result = nationalSubsidyPoiHelper.isHQSupplierPoi(NON_SHANGOU_BIZ_ORG_CODE, labelList);
            // assert
            assertFalse(result);
        }
    }

    /**
     * 测试闪购业务但不包含供应商标签的场景
     */
    @Test
    public void testIsHQSupplierPoi_ShangouWithoutSupplierLabel() throws Throwable {
        // arrange
        List<String> labelList = Collections.singletonList("other_label");
        try (MockedStatic<BizOrgHelper> bizOrgHelperMock = mockStatic(BizOrgHelper.class);
            MockedStatic<MccConfig> mccConfigMock = mockStatic(MccConfig.class)) {
            bizOrgHelperMock.when(() -> BizOrgHelper.isShangou(SHANGOU_BIZ_ORG_CODE)).thenReturn(true);
            mccConfigMock.when(MccConfig::getSupplierPoiLabel).thenReturn(SUPPLIER_POI_LABEL);
            mccConfigMock.when(MccConfig::getHQSupplierPoiLabel).thenReturn(HQ_SUPPLIER_POI_LABEL);
            // act
            boolean result = nationalSubsidyPoiHelper.isHQSupplierPoi(SHANGOU_BIZ_ORG_CODE, labelList);
            // assert
            assertFalse(result);
        }
    }

    /**
     * 测试闪购业务只包含供应商标签但不包含HQ供应商标签的场景
     */
    @Test
    public void testIsHQSupplierPoi_ShangouWithOnlySupplierLabel() throws Throwable {
        // arrange
        List<String> labelList = Collections.singletonList(SUPPLIER_POI_LABEL);
        try (MockedStatic<BizOrgHelper> bizOrgHelperMock = mockStatic(BizOrgHelper.class);
            MockedStatic<MccConfig> mccConfigMock = mockStatic(MccConfig.class)) {
            bizOrgHelperMock.when(() -> BizOrgHelper.isShangou(SHANGOU_BIZ_ORG_CODE)).thenReturn(true);
            mccConfigMock.when(MccConfig::getSupplierPoiLabel).thenReturn(SUPPLIER_POI_LABEL);
            mccConfigMock.when(MccConfig::getHQSupplierPoiLabel).thenReturn(HQ_SUPPLIER_POI_LABEL);
            // act
            boolean result = nationalSubsidyPoiHelper.isHQSupplierPoi(SHANGOU_BIZ_ORG_CODE, labelList);
            // assert
            assertFalse(result);
        }
    }

    /**
     * 测试闪购业务包含所有所需标签的场景
     */
    @Test
    public void testIsHQSupplierPoi_ShangouWithAllLabels() throws Throwable {
        // arrange
        List<String> labelList = Arrays.asList(SUPPLIER_POI_LABEL, HQ_SUPPLIER_POI_LABEL);
        try (MockedStatic<BizOrgHelper> bizOrgHelperMock = mockStatic(BizOrgHelper.class);
            MockedStatic<MccConfig> mccConfigMock = mockStatic(MccConfig.class)) {
            bizOrgHelperMock.when(() -> BizOrgHelper.isShangou(SHANGOU_BIZ_ORG_CODE)).thenReturn(true);
            mccConfigMock.when(MccConfig::getSupplierPoiLabel).thenReturn(SUPPLIER_POI_LABEL);
            mccConfigMock.when(MccConfig::getHQSupplierPoiLabel).thenReturn(HQ_SUPPLIER_POI_LABEL);
            // act
            boolean result = nationalSubsidyPoiHelper.isHQSupplierPoi(SHANGOU_BIZ_ORG_CODE, labelList);
            // assert
            assertTrue(result);
        }
    }

    /**
     * 测试空标签列表的场景
     */
    @Test
    public void testIsHQSupplierPoi_EmptyLabelList() throws Throwable {
        // arrange
        List<String> labelList = Collections.emptyList();
        try (MockedStatic<BizOrgHelper> bizOrgHelperMock = mockStatic(BizOrgHelper.class);
            MockedStatic<MccConfig> mccConfigMock = mockStatic(MccConfig.class)) {
            bizOrgHelperMock.when(() -> BizOrgHelper.isShangou(SHANGOU_BIZ_ORG_CODE)).thenReturn(true);
            mccConfigMock.when(MccConfig::getSupplierPoiLabel).thenReturn(SUPPLIER_POI_LABEL);
            mccConfigMock.when(MccConfig::getHQSupplierPoiLabel).thenReturn(HQ_SUPPLIER_POI_LABEL);
            // act
            boolean result = nationalSubsidyPoiHelper.isHQSupplierPoi(SHANGOU_BIZ_ORG_CODE, labelList);
            // assert
            assertFalse(result);
        }
    }

    @Test
    public void testIsDealersSupplierPoi_WhenAllConditionsMet_ShouldReturnTrue() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class);
            MockedStatic<BizOrgHelper> bizOrgHelperMockedStatic = mockStatic(BizOrgHelper.class)) {
            mccConfigMockedStatic.when(MccConfig::getSupplierPoiLabel).thenReturn(SUPPLIER_LABEL);
            mccConfigMockedStatic.when(MccConfig::getDealersSupplierPoiLabel).thenReturn(DEALERS_LABEL);
            bizOrgHelperMockedStatic.when(() -> BizOrgHelper.isShangou(SG_BIZ_ORG_CODE)).thenReturn(true);
            NationalSubsidyPoiHelper helper = new NationalSubsidyPoiHelper();
            boolean result = helper.isDealersSupplierPoi(SG_BIZ_ORG_CODE, Arrays.asList(SUPPLIER_LABEL, DEALERS_LABEL));
            assertTrue("Should return true when all conditions are met", result);
        }
    }

    @Test
    public void testIsDealersSupplierPoi_WhenNotShangou_ShouldReturnFalse() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class);
            MockedStatic<BizOrgHelper> bizOrgHelperMockedStatic = mockStatic(BizOrgHelper.class)) {
            mccConfigMockedStatic.when(MccConfig::getSupplierPoiLabel).thenReturn(SUPPLIER_LABEL);
            mccConfigMockedStatic.when(MccConfig::getDealersSupplierPoiLabel).thenReturn(DEALERS_LABEL);
            bizOrgHelperMockedStatic.when(() -> BizOrgHelper.isShangou(WM_BIZ_ORG_CODE)).thenReturn(false);
            NationalSubsidyPoiHelper helper = new NationalSubsidyPoiHelper();
            boolean result = helper.isDealersSupplierPoi(WM_BIZ_ORG_CODE, Arrays.asList(SUPPLIER_LABEL, DEALERS_LABEL));
            assertFalse("Should return false when not Shangou", result);
        }
    }

    @Test
    public void testIsDealersSupplierPoi_WhenSupplierLabelMissing_ShouldReturnFalse() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class);
            MockedStatic<BizOrgHelper> bizOrgHelperMockedStatic = mockStatic(BizOrgHelper.class)) {
            mccConfigMockedStatic.when(MccConfig::getSupplierPoiLabel).thenReturn(SUPPLIER_LABEL);
            mccConfigMockedStatic.when(MccConfig::getDealersSupplierPoiLabel).thenReturn(DEALERS_LABEL);
            bizOrgHelperMockedStatic.when(() -> BizOrgHelper.isShangou(SG_BIZ_ORG_CODE)).thenReturn(true);
            NationalSubsidyPoiHelper helper = new NationalSubsidyPoiHelper();
            boolean result = helper.isDealersSupplierPoi(SG_BIZ_ORG_CODE, Collections.singletonList(DEALERS_LABEL));
            assertFalse("Should return false when supplier label is missing", result);
        }
    }

    @Test
    public void testIsDealersSupplierPoi_WhenDealersLabelMissing_ShouldReturnFalse() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class);
            MockedStatic<BizOrgHelper> bizOrgHelperMockedStatic = mockStatic(BizOrgHelper.class)) {
            mccConfigMockedStatic.when(MccConfig::getSupplierPoiLabel).thenReturn(SUPPLIER_LABEL);
            mccConfigMockedStatic.when(MccConfig::getDealersSupplierPoiLabel).thenReturn(DEALERS_LABEL);
            bizOrgHelperMockedStatic.when(() -> BizOrgHelper.isShangou(SG_BIZ_ORG_CODE)).thenReturn(true);
            NationalSubsidyPoiHelper helper = new NationalSubsidyPoiHelper();
            boolean result = helper.isDealersSupplierPoi(SG_BIZ_ORG_CODE, Collections.singletonList(SUPPLIER_LABEL));
            assertFalse("Should return false when dealers label is missing", result);
        }
    }

    @Test
    public void testIsDealersSupplierPoi_WhenEmptyLabelList_ShouldReturnFalse() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class);
            MockedStatic<BizOrgHelper> bizOrgHelperMockedStatic = mockStatic(BizOrgHelper.class)) {
            mccConfigMockedStatic.when(MccConfig::getSupplierPoiLabel).thenReturn(SUPPLIER_LABEL);
            mccConfigMockedStatic.when(MccConfig::getDealersSupplierPoiLabel).thenReturn(DEALERS_LABEL);
            bizOrgHelperMockedStatic.when(() -> BizOrgHelper.isShangou(SG_BIZ_ORG_CODE)).thenReturn(true);
            NationalSubsidyPoiHelper helper = new NationalSubsidyPoiHelper();
            boolean result = helper.isDealersSupplierPoi(SG_BIZ_ORG_CODE, Collections.emptyList());
            assertFalse("Should return false when label list is empty", result);
        }
    }

    @Test(expected = NullPointerException.class)
    public void testIsDealersSupplierPoi_WhenNullLabelList_ShouldThrowNPE() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class);
            MockedStatic<BizOrgHelper> bizOrgHelperMockedStatic = mockStatic(BizOrgHelper.class)) {
            mccConfigMockedStatic.when(MccConfig::getSupplierPoiLabel).thenReturn(SUPPLIER_LABEL);
            mccConfigMockedStatic.when(MccConfig::getDealersSupplierPoiLabel).thenReturn(DEALERS_LABEL);
            bizOrgHelperMockedStatic.when(() -> BizOrgHelper.isShangou(SG_BIZ_ORG_CODE)).thenReturn(true);
            NationalSubsidyPoiHelper helper = new NationalSubsidyPoiHelper();
            helper.isDealersSupplierPoi(SG_BIZ_ORG_CODE, null);
        }
    }

    @Test
    public void testIsDealersSupplierPoi_WhenDifferentConfigValues_ShouldMatchNewValues() throws Throwable {
        String newSupplierLabel = "999";
        String newDealersLabel = "888";
        try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class);
            MockedStatic<BizOrgHelper> bizOrgHelperMockedStatic = mockStatic(BizOrgHelper.class)) {
            mccConfigMockedStatic.when(MccConfig::getSupplierPoiLabel).thenReturn(newSupplierLabel);
            mccConfigMockedStatic.when(MccConfig::getDealersSupplierPoiLabel).thenReturn(newDealersLabel);
            bizOrgHelperMockedStatic.when(() -> BizOrgHelper.isShangou(SG_BIZ_ORG_CODE)).thenReturn(true);
            NationalSubsidyPoiHelper helper = new NationalSubsidyPoiHelper();
            boolean result = helper.isDealersSupplierPoi(SG_BIZ_ORG_CODE, Arrays.asList(newSupplierLabel, newDealersLabel));
            assertTrue("Should return true when all conditions are met with new label values", result);
        }
    }
}
