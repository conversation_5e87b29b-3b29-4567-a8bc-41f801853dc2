package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.effect.area;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.AreaSinglePoiFlowRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.effect.area.AreaEffectiveCheckReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.effect.areaNew.AreaNewEffectiveCheckReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 生效校验路由
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class AreaEffectiveCheckReqRouter extends AreaSinglePoiFlowRequestRouter {

    @Resource
    private AreaEffectiveCheckReqSender areaEffectiveCheckReqSender;

    @Resource
    private AreaNewEffectiveCheckReqSender areaNewEffectiveCheckReqSender;

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        if (SessionCategoryEnum.PHF.equals(GlobalFlowSessionHandler.getSessionCategory())) {
            RouteResult routeResult = new RouteResult();
            routeResult.setExecTag(routeParam.getAreaExecTag());
            return routeResult;
        }
        return buildNotPhfAreaResult(routeParam, BooleanUtils.isTrue(routeParam.getRouteDimensionParam().getUseAreaNewServiceV1()) ? areaNewEffectiveCheckReqSender : areaEffectiveCheckReqSender);
    }

}