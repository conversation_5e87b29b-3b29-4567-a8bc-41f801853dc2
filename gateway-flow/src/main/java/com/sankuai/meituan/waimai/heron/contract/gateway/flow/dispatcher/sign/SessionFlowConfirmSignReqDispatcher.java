package com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.sign;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.dispatcher.BaseFlowProcessingRequestDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.RouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.ConfirmSignStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.sign.area.AreaSignConfirmReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.sign.perf.PerfContractSignConfirmReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.sign.plat.PlatContractSignConfirmReqRouter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
@Slf4j
public class SessionFlowConfirmSignReqDispatcher extends BaseFlowProcessingRequestDispatcher<ConfirmSignStepParam> {

    @Resource
    private RouteHelper routeHelper;
    @Override
    protected List<Class<? extends RequestRouter>> registerRouterClass() {
        return ImmutableList.of(PlatContractSignConfirmReqRouter.class, PerfContractSignConfirmReqRouter.class, AreaSignConfirmReqRouter.class);
    }

    @Override
    protected RouteParam buildRouteParam(ConfirmSignStepParam originParam) throws GatewayAdapterException {
        return routeHelper.buildFlowRouteParam(originParam.getWmPoiId());
    }
}
