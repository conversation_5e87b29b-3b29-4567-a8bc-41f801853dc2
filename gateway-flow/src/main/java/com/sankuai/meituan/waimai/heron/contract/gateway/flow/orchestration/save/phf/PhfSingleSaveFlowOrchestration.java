package com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.save.phf;

import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.audit.CommitAuditStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.AuditRejectStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.SaveCheckFailStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.SaveFailStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.LogisticsFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.audit.CommitAuditFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.save.phf.PhfSingleSaveCheckFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.save.phf.PhfSingleSaveFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.CommitAuditFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.SaveFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractSingleFlowSaveParam;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
@Service
public class PhfSingleSaveFlowOrchestration implements LogisticsFlowOrchestration<PhfContractSingleFlowSaveParam> {

    @Resource
    private PhfSingleSaveCheckFlowStep phfSingleSaveCheckFlowStep;

    @Resource
    private PhfSingleSaveFlowStep phfSingleSaveFlowStep;


    @Resource
    private SaveFailFlowStep saveFailFlowStep;

    @Resource
    private CommitAuditFlowStep commitAuditFlowStep;

    @Resource
    private CommitAuditFailFlowStep commitAuditFailFlowStep;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Override
    public OrchestrationExecResult flow(PhfContractSingleFlowSaveParam param) {
        FlowExecResult checkResult = phfSingleSaveCheckFlowStep.execute(param);
        HeronContractGatewaySession prevProcessingFlowSession = heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(param.getWmPoiId(), SessionCategoryEnum.PHF);
        if (checkResult.isFail()) {
            //不存在流程中session，记录错误原因，存在流程中session
            if (prevProcessingFlowSession == null) {
                saveCheckFailSession(checkResult);
            }
            return OrchestrationExecResult.fail(PhfSingleSaveCheckFlowStep.class, checkResult);
        }
        if (prevProcessingFlowSession != null) {
            return OrchestrationExecResult.fail("存在流程中session，无法发起新流程");
        }
        //拼好饭在校验通过时插入session
        heronContractGatewaySessionBasicService.insert(GlobalFlowSessionHandler.getSession());
        FlowExecResult saveResult = phfSingleSaveFlowStep.execute(param);
        if (saveResult.isFail()) {
            saveFailFlowStep.execute(buildSaveFailStepParam(param, saveResult));
            return OrchestrationExecResult.fail(PhfSingleSaveFlowStep.class, saveResult);
        }
        FlowExecResult commitAuditResult = commitAuditFlowStep.execute(buildCommitAuditParam(param));
        if (commitAuditResult.isFail()) {
            commitAuditFailFlowStep.execute(buildCommitAuditFailStepParam(param, commitAuditResult));
            return OrchestrationExecResult.fail(CommitAuditFlowStep.class, commitAuditResult);
        }
        return OrchestrationExecResult.success();
    }

    private void saveCheckFailSession(FlowExecResult checkResult) {
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        session.setStatus(ContractSessionStatusEnum.SAVE_FAIL);
        String failRemark = "failTraceId=" + Tracer.id()  + ", 子领域" + checkResult.getFailCauseDomain() + ", 错误原因: "  + checkResult.getMessage();
        session.setFailRemark(failRemark.substring(0, Math.min(failRemark.length(), 200)));
        heronContractGatewaySessionBasicService.insert(session);
    }

    private AuditRejectStepParam buildCommitAuditFailStepParam(PhfContractSingleFlowSaveParam param, FlowExecResult commitAuditResult) {
        return AuditRejectStepParam.builder()
                .causeDomain(commitAuditResult.getFailCauseDomain())
                .wmPoiId(param.getWmPoiId())
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .failReason("提审失败:" + commitAuditResult.getMessage())
                .operator(param.getOperator())
                .build();
    }

    private CommitAuditStepParam buildCommitAuditParam(PhfContractSingleFlowSaveParam param) {
        return CommitAuditStepParam.builder()
                .operator(param.getOperator())
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .wmPoiId(param.getWmPoiId())
                .build();
    }

    private SaveFailStepParam buildSaveFailStepParam(PhfContractSingleFlowSaveParam param, FlowExecResult saveResult) {
        return SaveFailStepParam.builder()
                .causeDomain(saveResult.getFailCauseDomain())
                .wmPoiId(param.getWmPoiId())
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .failReason("保存失败:" + saveResult.getMessage())
                .operator(param.getOperator())
                .build();
    }

    private SaveCheckFailStepParam buildSaveCheckFailStepParam(PhfContractSingleFlowSaveParam param, FlowExecResult checkResult) {
        return SaveCheckFailStepParam.builder()
                .causeDomain(checkResult.getFailCauseDomain())
                .wmPoiId(param.getWmPoiId())
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .failReason(checkResult.getMessage())
                .operator(param.getOperator())
                .build();
    }
    
    
}
