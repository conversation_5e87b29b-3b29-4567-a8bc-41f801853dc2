package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.area;

import com.sankuai.meituan.banma.poi.sparea.thrift.param.save.BmOpenSpAreaPoiSaveRequest;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmAreaFlowThriftServiceAdapterOld;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.AreaDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.AreaMigrateSaveParamConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractMigrateSaveParam;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 迁移场景-配送信息保存校验请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class AreaMigrateSavePreCheckReqSender extends AreaDomainRequestSender<HeronContractMigrateSaveParam, BmOpenSpAreaPoiSaveRequest> {

    @Resource
    private BmAreaFlowThriftServiceAdapterOld bmAreaFlowThriftServiceAdapterOld;


    @Override
    protected BmOpenSpAreaPoiSaveRequest buildRemoteParam(SendParam<HeronContractMigrateSaveParam> param) throws GatewayArgumentException, GatewayArgumentException {
        return AreaMigrateSaveParamConverter.convert(param);
    }


    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.SAVE_CHECK;
    }

    @Override
    protected void sendToSpArea(BmOpenSpAreaPoiSaveRequest bmOpenSpAreaPoiSaveRequest) throws GatewayAdapterException {
        bmAreaFlowThriftServiceAdapterOld.validateWithParam(bmOpenSpAreaPoiSaveRequest);
    }
}