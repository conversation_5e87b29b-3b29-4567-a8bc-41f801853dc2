package com.sankuai.meituan.waimai.heron.contract.gateway.flow.helper;

import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.banma.business.poi.sparea.client.common.request.SpAreaProcessControlRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.common.response.SpAreaProcessControlResponse;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.NeedCreatePerfFlowSessionParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfSettleFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmSpAreaQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.bo.SessionContextBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SessionSceneGroupEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.mafka.MafkaMessageSendService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.TimeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.gray.PhfMigrateSaveGrayDecider;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.leaf.LeafIdGenerator;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.SessionCategoryHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.AreaIdentityHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ApplySignActionEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractMigrateStageEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractInfoParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractSingleFlowSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.LogisticsBrandProductParamStruct;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.PhfContractBatchSaveExtParamStruct;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.PhfContractSessionNoticeMessageStruct;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.PhfContractSingleSaveExtParamStruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/10
 */
@Component
@Slf4j
public class PhfSessionBuildHelper {

    @Resource
    private BmPerfSettleFlowThriftServiceAdapter bmPerfSettleFlowThriftServiceAdapter;

    @Resource
    private BmSpAreaQueryThriftServiceAdapter bmSpAreaQueryThriftServiceAdapter;

    @Resource
    private PhfMigrateSaveGrayDecider phfMigrateSaveGrayDecider;

    @Resource
    private MafkaMessageSendService mafkaMessageSendService;

    public HeronContractGatewaySession buildSession(PhfContractSingleFlowSaveParam param, ContractCreateAndUpdateSceneEnum scene) throws GatewayAdapterException, GatewayArgumentException {
        PhfContractSingleSaveExtParamStruct extParamStruct = JacksonUtil.readValueNotNull(param.getProcessExtParam(), PhfContractSingleSaveExtParamStruct.class);
        HeronContractGatewaySession session = new HeronContractGatewaySession();
        session.setWmPoiId(param.getWmPoiId());
        session.setSessionScene(scene);
        session.setSessionLeafId(LeafIdGenerator.generateSessionId());
        session.setSceneGroup(SessionSceneGroupEnum.FLOW);
        SessionContextBo contextBo = SessionContextBo.newFlowSession();
        ContractMigrateStageEnum migrateStageEnum = phfMigrateSaveGrayDecider.judgeSaveMigrateStage(param.getWmPoiId(), param.getContractType());
        fillSessionContext(contextBo, migrateStageEnum);
        contextBo.setPhfContractType(param.getContractType());
        contextBo.setPhfContractId(Optional.ofNullable(param.getPhfContractInfo()).map(PhfContractInfoParam::getPhfContractId).orElse(null));
        contextBo.setPhfOriginRecordKey(Optional.ofNullable(param.getPhfContractInfo()).map(PhfContractInfoParam::getEcontractRecordKey).orElse(null));
        contextBo.setApplySignAction(ApplySignActionEnum.DIRECT);
        contextBo.setPhfFeeMode(extParamStruct.getFeeMode());
        session.setContext(contextBo);
        session.setStatus(ContractSessionStatusEnum.SAVE);
        session.setBatchRelationId(LeafIdGenerator.generateBatchRelationId());
        session.setOpId(param.getOperator().getOpId());
        session.setOpName(Optional.ofNullable(param.getOperator().getOpName()).orElse(""));
        session.setValid(1);
        session.setSessionCategory(SessionCategoryEnum.PHF);
        contextBo.setHasPerf(judgeHasPerf(session, extParamStruct, migrateStageEnum));
        contextBo.setHasArea(judgeHasArea(session, extParamStruct));
        return session;
    }

    public  void fillSessionContext(SessionContextBo contextBo, ContractMigrateStageEnum migrateStageEnum) {
        contextBo.setAreaSplit(ContractMigrateStageEnum.MIGRATE_NEW.equals(migrateStageEnum));
        contextBo.setPerfSplit(ContractMigrateStageEnum.MIGRATE_NEW.equals(migrateStageEnum));
        contextBo.setPerfDR(ContractMigrateStageEnum.BOTH_RUN.equals(migrateStageEnum));
        contextBo.setTechDR(ContractMigrateStageEnum.BOTH_RUN.equals(migrateStageEnum));
        contextBo.setAreaDR(ContractMigrateStageEnum.BOTH_RUN.equals(migrateStageEnum));
        contextBo.setTraceId(Tracer.id());
        ExecTagEnum execTagEnum = ContractMigrateStageEnum.BOTH_RUN.equals(migrateStageEnum) ? ExecTagEnum.BOTH_EXEC : ExecTagEnum.EXEC;
        contextBo.setPerfExecTag(execTagEnum);
        contextBo.setAreaExecTag(execTagEnum);
        contextBo.setMigrateStage(migrateStageEnum);
    }

    public HeronContractGatewaySession buildBatchSession(Long wmPoiId,
                                                         ContractMigrateStageEnum migrateStageEnum,
                                                         PhfContractBatchSaveExtParamStruct extParamStruct,
                                                         ContractCreateAndUpdateSceneEnum sceneEnum) throws GatewayAdapterException, GatewayArgumentException {

        HeronContractGatewaySession session = new HeronContractGatewaySession();
        session.setSessionLeafId(LeafIdGenerator.generateSessionId());
        session.setWmPoiId(wmPoiId);
        session.setSessionScene(sceneEnum);
        session.setSceneGroup(SessionSceneGroupEnum.FLOW);
        SessionContextBo contextBo = SessionContextBo.newFlowSession();
        fillSessionContext(contextBo, migrateStageEnum);
        session.setContext(contextBo);
        contextBo.setPhfFeeMode(extParamStruct.getFeeMode());
        session.setStatus(ContractSessionStatusEnum.SAVE);
        session.setValid(1);
        session.setSessionCategory(SessionCategoryEnum.PHF);
        contextBo.setHasPerf(judgeHasPerf(session, extParamStruct, migrateStageEnum));
        contextBo.setHasArea(judgeHasArea(session,extParamStruct));
        return session;
    }

    private Boolean judgeHasPerf(HeronContractGatewaySession session, PhfContractBatchSaveExtParamStruct extParamStruct, ContractMigrateStageEnum migrateStageEnum) throws GatewayAdapterException {
        Set<String> serviceBrandSet = extParamStruct.getServiceBrandProductList().stream()
                .map(LogisticsBrandProductParamStruct::getServiceBrand)
                .collect(Collectors.toSet());
        NeedCreatePerfFlowSessionParam needCreatePerfFlowSessionParam = new NeedCreatePerfFlowSessionParam();
        needCreatePerfFlowSessionParam.setSessionId(session.getSessionLeafId());
        needCreatePerfFlowSessionParam.setWmPoiId(session.getWmPoiId());
        needCreatePerfFlowSessionParam.setFeeMode(extParamStruct.getFeeMode());
        needCreatePerfFlowSessionParam.setBusinessIdentity(SessionCategoryHandler.convertFromSessionCategory(SessionCategoryEnum.PHF, ContractMigrateStageEnum.BOTH_RUN.equals(migrateStageEnum)));
        needCreatePerfFlowSessionParam.setServiceBrands(serviceBrandSet);
        return bmPerfSettleFlowThriftServiceAdapter.isNeedCreatePerfFlowSession(needCreatePerfFlowSessionParam).getData().isNeedCreatePerfFlowSession();
    }

    private boolean judgeHasArea(HeronContractGatewaySession session, PhfContractBatchSaveExtParamStruct extParamStruct) {
        SpAreaProcessControlRequest param = new SpAreaProcessControlRequest();
        param.setPoiId(session.getWmPoiId());
        param.setExtMap(new HashMap<>());
        param.setFeeMode(extParamStruct.getFeeMode());
        SpAreaProcessControlResponse spAreaProcessControlResponse = null;
        try {
            spAreaProcessControlResponse = bmSpAreaQueryThriftServiceAdapter.judgeProcessControlSpAreaAbility(param, AreaIdentityHelper.convertFromSessionCategory(SessionCategoryEnum.PHF));
        } catch (GatewayAdapterException e) {
            log.info("PhfContractFlowBatchPoiSaveEntry judgeHasArea error session={}", JacksonUtil.writeAsJsonStr(session), e);
            return false;
        }
        return !spAreaProcessControlResponse.getIsNotSupportAbility();
    }

    private boolean judgeHasArea(HeronContractGatewaySession session, PhfContractSingleSaveExtParamStruct extParamStruct) {
        SpAreaProcessControlRequest param = new SpAreaProcessControlRequest();
        param.setPoiId(session.getWmPoiId());
        param.setExtMap(new HashMap<>());
        param.setFeeMode(extParamStruct.getFeeMode());
        SpAreaProcessControlResponse spAreaProcessControlResponse = null;
        try {
            spAreaProcessControlResponse = bmSpAreaQueryThriftServiceAdapter.judgeProcessControlSpAreaAbility(param, AreaIdentityHelper.convertFromSessionCategory(SessionCategoryEnum.PHF));
        } catch (GatewayAdapterException e) {
            log.info("PhfContractSinglePoiFlowSaveEntry judgeHasArea error session={}", JacksonUtil.writeAsJsonStr(session), e);
            return false;
        }
        return !spAreaProcessControlResponse.getIsNotSupportAbility();
    }

    private Boolean judgeHasPerf(HeronContractGatewaySession session, PhfContractSingleSaveExtParamStruct extParamStruct, ContractMigrateStageEnum migrateStageEnum) throws GatewayAdapterException {
        Set<String> serviceBrandSet = extParamStruct.getServiceBrandProductList().stream()
                .map(LogisticsBrandProductParamStruct::getServiceBrand)
                .collect(Collectors.toSet());
        NeedCreatePerfFlowSessionParam needCreatePerfFlowSessionParam = new NeedCreatePerfFlowSessionParam();
        needCreatePerfFlowSessionParam.setSessionId(session.getSessionLeafId());
        needCreatePerfFlowSessionParam.setWmPoiId(session.getWmPoiId());
        needCreatePerfFlowSessionParam.setFeeMode(extParamStruct.getFeeMode());
        needCreatePerfFlowSessionParam.setBusinessIdentity(SessionCategoryHandler.convertFromSessionCategory(SessionCategoryEnum.PHF, ContractMigrateStageEnum.BOTH_RUN.equals(migrateStageEnum)));
        needCreatePerfFlowSessionParam.setServiceBrands(serviceBrandSet);
        return bmPerfSettleFlowThriftServiceAdapter.isNeedCreatePerfFlowSession(needCreatePerfFlowSessionParam).getData().isNeedCreatePerfFlowSession();
    }


    public void sendSessionNotice(HeronContractGatewaySession session) {
        PhfContractSessionNoticeMessageStruct messageStruct = PhfContractSessionNoticeMessageStruct.builder()
                .phfContractId(session.getContext().getPhfContractId())
                .sendTime(TimeUtil.unixTime())
                .sessionId(session.getSessionLeafId())
                .wmPoiId(session.getWmPoiId())
                .build();
        mafkaMessageSendService.sendPhfSessionNotice(messageStruct);
    }
}
