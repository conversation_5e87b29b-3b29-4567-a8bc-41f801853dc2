package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.plat.phf;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PlatSingleFlowPoiRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.plat.phf.PlatPhfBatchSplitSingleSaveReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/3
 */
@Component
public class PlatPhfBatchSplitSingleSaveReqRouter extends PlatSingleFlowPoiRequestRouter {

    @Resource
    private PlatPhfBatchSplitSingleSaveReqSender platPhfBatchSplitSingleSaveReqSender;

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        return  RouteResult.builder()
                .execTag(ExecTagEnum.EXEC)
                .requestSender(platPhfBatchSplitSingleSaveReqSender)
                .build();
    }
}
