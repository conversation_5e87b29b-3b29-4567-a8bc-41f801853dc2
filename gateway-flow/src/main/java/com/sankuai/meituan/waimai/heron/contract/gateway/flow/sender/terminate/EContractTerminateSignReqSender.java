package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.terminate;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.EContractSignThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.EContractDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.EContractTerminateParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractForceCancelSignByTaskIdBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description: 终止签约请求发送
 * @author: chenyihao04
 * @create: 2023-05-12 10:49
 */
@Component
@Slf4j
public class EContractTerminateSignReqSender extends EContractDomainRequestSender<EContractTerminateParam, EContractTerminateParam> {

    @Resource
    private EContractSignThriftServiceAdapter eContractSignThriftServiceAdapter;



    @Override
    protected EContractTerminateParam buildRemoteParam(SendParam<EContractTerminateParam> param) {
        return param.getOriginParam();
    }

    @Override
    protected SendResult doSend(EContractTerminateParam terminateBo) throws GatewayAdapterException {
        boolean needCallback = terminateBo.getCancelSignWithCallback() == null || terminateBo.getCancelSignWithCallback();
        if (supportCancelSign(terminateBo)) {
            HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
            log.info("取消签约任务或待签约任务 terminateBo: {} session: {}", JacksonUtil.writeAsJsonStr(terminateBo), JacksonUtil.writeAsJsonStr(session));
            //自动续签任务的签约是打包签约，所以需要强制取消打包签约任务
            if (session != null && ContractCreateAndUpdateSceneEnum.C1_EXPIRE_AUTO_SIGN.equals(session.getSessionScene())) {
                eContractSignThriftServiceAdapter.forceCancelSignByTaskId(buildForceCancelParam(terminateBo.getConfirmId()));
            } else {
                eContractSignThriftServiceAdapter.cancelSign(terminateBo.getConfirmId(), needCallback);
            }

        } else if (supportCancelManualSign(terminateBo)) {
            eContractSignThriftServiceAdapter.cancelManualSign(terminateBo.getManualConfirmId(), terminateBo.getOperator().getOpId().intValue());
        } else {
            log.error("无存在的签约任务或待打包签约任务, 无需调用电子合同 参数 param: {}", JacksonUtil.writeAsJsonStr(terminateBo));
        }
        return SendResult.success();

    }

    private EcontractForceCancelSignByTaskIdBo buildForceCancelParam(Long confirmId) {
        EcontractForceCancelSignByTaskIdBo cancelSignByTaskIdBo = new EcontractForceCancelSignByTaskIdBo();
        cancelSignByTaskIdBo.setTaskId(confirmId);
        cancelSignByTaskIdBo.setReason("取消C1自动续签打包任务");
        cancelSignByTaskIdBo.setCallBack(true);
        return cancelSignByTaskIdBo;
    }

    private boolean supportCancelManualSign(EContractTerminateParam remoteParam) {
        return BooleanUtils.isTrue(remoteParam.getSupportCancelManualSign()) && Objects.nonNull(remoteParam.getManualConfirmId()) && remoteParam.getManualConfirmId() > 0;
    }

    private boolean supportCancelSign(EContractTerminateParam remoteParam) {
        return BooleanUtils.isTrue(remoteParam.getSupportCancelSign()) && Objects.nonNull(remoteParam.getConfirmId()) && remoteParam.getConfirmId() > 0;
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.TERMINATE_FLOW;
    }

}