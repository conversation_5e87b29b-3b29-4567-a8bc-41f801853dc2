package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.terminate;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PlatSingleFlowPoiRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.terminate.PlatContractFlowTerminateReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/7/6
 */
@Component
public class PlatContractSessionFlowTerminateReqRouter  extends PlatSingleFlowPoiRequestRouter {

    @Resource
    private PlatContractFlowTerminateReqSender platContractFlowTerminateReqSender;
    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setAreaSplit(routeParam.getProcessingAreaSplit());
        routeResult.setPerfSplit(routeParam.getProcessingPerfSplit());
        routeResult.setPerfDR(routeParam.getProcessingPerfDR());
        routeResult.setRequestSender(platContractFlowTerminateReqSender);
        return routeResult;
    }
}
