package com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.terminate;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.FlowTerminateBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.terminate.AreaFlowTerminateReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.terminate.PerfContractFlowTerminateReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.terminate.PlatContractSessionFlowTerminateReqRouter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6
 */
@Component
@Slf4j
public class SessionFlowTerminateReqDispatcher extends BaseFlowTerminateReqDispatcher {

    @Resource
    private RouteHelper routeHelper;

    @Override
    protected List<Class<? extends RequestRouter>> registerRouterClass() {
        return ImmutableList.of(PlatContractSessionFlowTerminateReqRouter.class, PerfContractFlowTerminateReqRouter.class, AreaFlowTerminateReqRouter.class);
    }

    @Override
    protected SinglePoiFlowRouteParam buildRouteParam(FlowTerminateBo param) {
        try {
            SinglePoiFlowRouteParam singlePoiFlowRouteParam = routeHelper.buildFlowRouteParam(param.getWmPoiId());
            singlePoiFlowRouteParam.setSkipDomainSet(param.getSkipDomainSet());
            return singlePoiFlowRouteParam;
        } catch (GatewayAdapterException e) {
            log.error("获取路由参数异常 参数 param: {}", JacksonUtil.writeAsJsonStr(param), e);
            throw new RuntimeException(e);
        }

    }
}
