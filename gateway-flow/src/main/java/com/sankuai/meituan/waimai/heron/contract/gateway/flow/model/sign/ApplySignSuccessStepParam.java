package com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign;

import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 发起签约结果同步子领域BO
 * @author: chenyihao04
 * @create: 2023-05-17 16:24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplySignSuccessStepParam {
    private Long wmPoiId;

    private Long confirmId;

    private Long sessionId;

    private Boolean success;

    private HeronContractOperator operator;

    private SessionCategoryEnum sessionCategory;

    private String techPdfUrl;

    private String perfPdfUrl;

    private String recordKey;
}