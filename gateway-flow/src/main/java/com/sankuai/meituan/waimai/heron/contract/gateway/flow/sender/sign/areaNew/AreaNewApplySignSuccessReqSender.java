package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.sign.areaNew;

import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.enums.WmSignTypeEnum;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaSignNotificationRequest;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmSpAreaFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.AreaDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.AreaIdentityHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.ApplySignSuccessStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ApplySignActionEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 发起签约成功请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class AreaNewApplySignSuccessReqSender extends AreaDomainRequestSender<ApplySignSuccessStepParam, SpAreaSignNotificationRequest> {

    @Resource
    private BmSpAreaFlowThriftServiceAdapter bmSpAreaFlowThriftServiceAdapter;

    @Override
    protected SpAreaSignNotificationRequest buildRemoteParam(SendParam<ApplySignSuccessStepParam> param) {
        ApplySignSuccessStepParam originParam = param.getOriginParam();
        SpAreaSignNotificationRequest bmOpenSpAreaSignNotificationRequest = new SpAreaSignNotificationRequest();
        bmOpenSpAreaSignNotificationRequest.setPoiId(originParam.getWmPoiId());
        bmOpenSpAreaSignNotificationRequest.setOperator(OperatorConverter.toAreaNewOperator(originParam.getOperator()));
        bmOpenSpAreaSignNotificationRequest.setSignBizId(originParam.getConfirmId());
        WmSignTypeEnum signTypeEnum = ApplySignActionEnum.LATER.equals(GlobalFlowSessionHandler.getSession().getContext().getApplySignAction()) ? WmSignTypeEnum.PACKAGE_SIGN : WmSignTypeEnum.DIRECT_SIGN;
        bmOpenSpAreaSignNotificationRequest.setSignType(signTypeEnum);
        bmOpenSpAreaSignNotificationRequest.setSessionId(originParam.getSessionId());
        return bmOpenSpAreaSignNotificationRequest;
    }

    @Override
    protected void sendToSpArea(SpAreaSignNotificationRequest remoteParam) throws GatewayAdapterException {
        bmSpAreaFlowThriftServiceAdapter.notifySignStatusChange(remoteParam, AreaIdentityHelper.convertFromSessionCategory(GlobalFlowSessionHandler.getSessionCategory()));
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.APPLY_SIGN_NOTICE;
    }

}