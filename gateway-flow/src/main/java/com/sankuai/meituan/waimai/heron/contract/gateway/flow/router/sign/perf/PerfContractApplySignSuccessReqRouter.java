package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.sign.perf;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PerfSinglePoiFlowRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.sign.perf.PerfContractApplySignSuccessReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 发起签约成功路由
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class PerfContractApplySignSuccessReqRouter extends PerfSinglePoiFlowRequestRouter {

    @Resource
    private PerfContractApplySignSuccessReqSender perfContractApplySignSuccessReqSender;

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        return super.buildDefaultPerfResult(routeParam, perfContractApplySignSuccessReqSender);
    }

}