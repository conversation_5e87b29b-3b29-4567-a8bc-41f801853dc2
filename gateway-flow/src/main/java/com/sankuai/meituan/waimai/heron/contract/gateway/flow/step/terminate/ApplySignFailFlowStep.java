package com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.TimeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.DispatchParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.DispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.terminate.FlowSignFailReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.ApplySignFailStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.EContractTerminateParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.FlowTerminateBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.terminate.EContractTerminateSignReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.statushandler.FlowFailStatusHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.LogisticsFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 发起签约失败步骤
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
@Slf4j
public class ApplySignFailFlowStep implements LogisticsFailFlowStep<ApplySignFailStepParam> {

    @Resource
    private FlowSignFailReqDispatcher flowSignFailReqDispatcher;

    @Resource
    private EContractTerminateSignReqSender eContractTerminateSignReqSender;

    @Resource
    private RouteHelper routeHelper;

    @Override
    @FlowFailStatusHandler(targetFailStatus = ContractSessionStatusEnum.SIGN_FAIL)
    public FlowExecResult execute(ApplySignFailStepParam param) {

        DispatchParam<FlowTerminateBo> dispatchParam = new DispatchParam<>(buildFlowTerminateBo(param));
        DispatchResult flowDispatchResult = flowSignFailReqDispatcher.dispatch(dispatchParam);
        cancelEContractTask(param);
        return FlowExecResult.build(flowDispatchResult);
    }

    private FlowTerminateBo buildFlowTerminateBo(ApplySignFailStepParam param) {
        return FlowTerminateBo.builder()
                .wmPoiId(param.getWmPoiId())
                .sessionId(param.getSessionId())
                .manualConfirmId(param.getManualConfirmId())
                .causeDomain(param.getCauseDomain())
                .targetStatus(ContractSessionStatusEnum.SIGN_FAIL)
                .failReason("["+param.getCauseDomain().getDesc() + "]" + param.getFailReason())
                .dealTime(TimeUtil.unixTime())
                .operator(param.getOperator())
                .sessionCategory(param.getSessionCategory()).build();
    }

    //取消签约平台任务，用于打包发起签约失败时，取消签约平台的待签约任务，其他场景不会发起调用
    private void cancelEContractTask(ApplySignFailStepParam param) {

        try {
            eContractTerminateSignReqSender.sendRequest(new SendParam<>(buildRouteResult(), buildEContractTerminateParam(param)));
        } catch (Exception e) {
            log.error("发起签约失败, 取消签约平台任务异常, param: {}, e: ", JacksonUtil.writeAsJsonStr(param));
        }

    }

    private EContractTerminateParam buildEContractTerminateParam(ApplySignFailStepParam param) {
        return EContractTerminateParam.builder()
                .wmPoiId(param.getWmPoiId())
                .sessionId(param.getSessionId())
                .manualConfirmId(param.getManualConfirmId())
                .operator(param.getOperator())
                .supportCancelSign(false)
                .supportCancelManualSign(true)
                .build();
    }


    private RouteResult buildRouteResult() {
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setAreaSplit(GlobalFlowSessionHandler.getAreaSplit());
        routeResult.setPerfSplit(GlobalFlowSessionHandler.getPerfSplit());
        routeResult.setPerfDR(GlobalFlowSessionHandler.getPerfDR());
        routeResult.setRequestSender(eContractTerminateSignReqSender);
        return routeResult;
    }
}