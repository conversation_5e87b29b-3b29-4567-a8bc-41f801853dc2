package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.effect.plat;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.EffectiveStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.ResultDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SinglePoiEffectRequestDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 生效请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class PlatContractEffectiveReqSender extends PlatDomainRequestSender<EffectiveStepParam, SinglePoiEffectRequestDTO> {

    @Resource
    private PlatContractFlowThriftServiceAdapter platContractFlowThriftServiceAdapter;

    @Override
    protected SinglePoiEffectRequestDTO buildRemoteParam(SendParam<EffectiveStepParam> param) {
        EffectiveStepParam effectiveStepParam = param.getOriginParam();
        SinglePoiEffectRequestDTO effectRequestDTO = new SinglePoiEffectRequestDTO();
        effectRequestDTO.setWmPoiId(effectiveStepParam.getWmPoiId());
        effectRequestDTO.setSessionId(effectiveStepParam.getSessionId());
        effectRequestDTO.setEffectTime(effectiveStepParam.getEffectiveTime());
        effectRequestDTO.setOperator(OperatorConverter.toPlatOperator(effectiveStepParam.getOperator()));
        effectRequestDTO.setAreaSplit(param.getRouteResult().getAreaSplit());
        effectRequestDTO.setPerfSplit(param.getRouteResult().getPerfSplit());
        effectRequestDTO.setPerfDR(param.getRouteResult().getPerfDR());
        effectRequestDTO.setSessionCategory(GlobalFlowSessionHandler.getSessionCategory().name());
        return effectRequestDTO;
    }

    @Override
    protected SendResult doSend(SinglePoiEffectRequestDTO remoteParam) throws GatewayAdapterException {
        ResultDTO resultDTO = platContractFlowThriftServiceAdapter.effectSinglePoi(remoteParam);
        return convertResult(resultDTO);
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.EFFECT;
    }

}