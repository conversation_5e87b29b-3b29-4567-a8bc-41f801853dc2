package com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf;

import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractBatchMockFlowParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhfMockSaveCheckFlowStepParam {

    private Long wmPoiId;

    private PhfContractBatchMockFlowParam mockFlowParam;
}
