package com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.TimeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.DispatchParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.DispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.terminate.FlowSignFailReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.ConfirmSignFailStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.FlowTerminateBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.LogisticsFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class NonSessionConfirmSignFailFlowStep implements LogisticsFailFlowStep<ConfirmSignFailStepParam> {

    @Resource
    private FlowSignFailReqDispatcher flowSignFailReqDispatcher;

    @Override
    public FlowExecResult execute(ConfirmSignFailStepParam param) {
        DispatchParam<FlowTerminateBo> dispatchParam = new DispatchParam<>(buildFlowTerminateBo(param));
        DispatchResult flowDispatchResult = flowSignFailReqDispatcher.dispatch(dispatchParam);
        return FlowExecResult.build(flowDispatchResult);
    }


    private FlowTerminateBo buildFlowTerminateBo(ConfirmSignFailStepParam param) {
        return FlowTerminateBo.builder()
                .wmPoiId(param.getWmPoiId())
                .sessionId(param.getSessionId())
                .causeDomain(param.getCauseDomain())
                .confirmId(param.getConfirmId())
                .failReason(param.getFailReason())
                .targetStatus(ContractSessionStatusEnum.SIGN_FAIL)
                .dealTime(TimeUtil.unixTime())
                .operator(param.getOperator()).build();
    }
}
