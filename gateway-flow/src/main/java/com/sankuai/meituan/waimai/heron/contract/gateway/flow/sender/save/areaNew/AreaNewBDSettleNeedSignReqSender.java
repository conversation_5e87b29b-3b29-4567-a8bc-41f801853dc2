package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.areaNew;

import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaNeedSignRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.response.SpAreaNeedSignResponse;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmSpAreaFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestErrorTypeEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.AreaDomainNeedResultRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.AreaBDSettleSaveParamConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.AreaIdentityHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractBDSettleJudgeSignParam;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 单店保存判断是否需要签约请求发送
 * @author: chenyihao04
 * @create: 2023-06-27 16:02
 */
@Component
public class AreaNewBDSettleNeedSignReqSender extends AreaDomainNeedResultRequestSender<HeronContractBDSettleJudgeSignParam, SpAreaNeedSignRequest> {

    @Resource
    private BmSpAreaFlowThriftServiceAdapter bmSpAreaFlowThriftServiceAdapter;

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.SAVE_CHECK;
    }


    @Override
    protected SpAreaNeedSignRequest buildRemoteParam(SendParam<HeronContractBDSettleJudgeSignParam> param) throws GatewayAdapterException {
        SpAreaNeedSignRequest spAreaNeedSignRequest = AreaBDSettleSaveParamConverter.convertAreaNewNeedSignRequest(param);
        return spAreaNeedSignRequest;
    }

    @Override
    protected SendResult doSend(SpAreaNeedSignRequest remoteParam) throws GatewayAdapterException {
        SpAreaNeedSignResponse needSign;
        try {
            needSign = bmSpAreaFlowThriftServiceAdapter.isNeedSign(remoteParam, AreaIdentityHelper.convertFromSessionCategory(GlobalFlowSessionHandler.getSessionCategory()));
            SendResult sendResult = SendResult.success(String.valueOf(needSign.getNeedSign()), Boolean.class);
            return sendResult;
        } catch (GatewayAdapterException e) {
            if (!e.isThriftException()) {
                return SendResult.fail(e.getCode(), e.getMessage(), RequestErrorTypeEnum.DOMAIN_BUSINESS_EXCEPTION);
            }
            throw e;
        }
    }


}