package com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.phf;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.TimeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.DispatchParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.DispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.terminate.FlowSignFailReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.BaseTerminateFlowStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.EContractTerminateParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.FlowTerminateBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.PhfBdCancelSignStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.terminate.EContractTerminateSignReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.LogisticsFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

import static com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum.SIGN_FAIL;

/**
 * <AUTHOR>
 * @date 2025/2/10
 */
@Component
@Slf4j
public class PhfBDCancelSignFlowStep implements LogisticsFailFlowStep<PhfBdCancelSignStepParam> {

    @Resource
    private FlowSignFailReqDispatcher flowSignFailReqDispatcher;

    @Resource
    private EContractTerminateSignReqSender eContractTerminateSignReqSender;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Override
    public FlowExecResult execute(PhfBdCancelSignStepParam param) {
        HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(param.getWmPoiId(), SessionCategoryEnum.PHF);
        if (Objects.isNull(session)) {
            return FlowExecResult.success();
        }
        FlowTerminateBo terminateBo = buildFlowTerminateBo(param);
        DispatchParam<FlowTerminateBo> terminateParamDispatchParam = new DispatchParam<>(terminateBo);
        DispatchResult flowDispatchResult = flowSignFailReqDispatcher.dispatch(terminateParamDispatchParam);
        heronContractGatewaySessionBasicService.updateStatusAndFailRemark(GlobalFlowSessionHandler.getSessionId(), SIGN_FAIL, param.getFailReason());
        //无论是否取消成功，都调用签约平台进行取消，签约平台回调取消其他门店任务
        if (param.getNoNeedCallEcontract() == null || BooleanUtils.isTrue(param.getNeedCallEcontract())) {
            EContractTerminateParam eContractTerminateParam = buildEContractTerminateParam(param);
            SendResult sendResult = eContractTerminateSignReqSender.sendRequest(new SendParam<>(eContractTerminateParam));
            if (Objects.nonNull(sendResult) && !sendResult.getSuccess()) {
                log.warn("电子签约平台取消签约失败 wmPoiId={} sendResult={}", param.getWmPoiId(), JacksonUtil.writeAsJsonStr(sendResult));
            }
        }

        return FlowExecResult.build(flowDispatchResult);
    }

    private FlowTerminateBo buildFlowTerminateBo(BaseTerminateFlowStepParam param) {
        HeronContractGatewaySession session = Objects.requireNonNull(GlobalFlowSessionHandler.getSession());
        return FlowTerminateBo.builder()
                .wmPoiId(param.getWmPoiId())
                .sessionId(session.getSessionLeafId())
                .confirmId(session.getContext().getConfirmId())
                .causeDomain(param.getCauseDomain())
                .failReason(param.getFailReason())
                .targetStatus(SIGN_FAIL)
                .dealTime(TimeUtil.unixTime())
                .operator(param.getOperator())
                .build();
    }

    private EContractTerminateParam buildEContractTerminateParam(BaseTerminateFlowStepParam param) {
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        return EContractTerminateParam.builder()
                .wmPoiId(param.getWmPoiId())
                .sessionId(session.getSessionLeafId())
                .confirmId(session.getContext().getConfirmId())
                .cancelSignWithCallback(true)
                .supportCancelSign(true)
                .operator(param.getOperator()).build();
    }
}
