package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.plat;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PlatSingleFlowPoiRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.plat.PlatContractMigrateTempSaveReqSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 迁移场景-配送信息暂存路由
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
@Slf4j
public class PlatContractMigrateTempSaveReqRouter extends PlatSingleFlowPoiRequestRouter {

    @Resource
    private PlatContractMigrateTempSaveReqSender platContractMigrateTempSaveReqSender;

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setAreaSplit(routeParam.getProcessingAreaSplit());
        routeResult.setPerfSplit(routeParam.getProcessingPerfSplit());
        routeResult.setPerfDR(routeParam.getProcessingPerfDR());
        routeResult.setRequestSender(matchReqSender(routeParam));
        return routeResult;
    }

    private RequestSender matchReqSender(SinglePoiFlowRouteParam routeParam) {
        if (routeParam.getProcessingContractStruct().isContractStruct()) {
            return platContractMigrateTempSaveReqSender;
        } else if (routeParam.getProcessingContractStruct().isOldStruct()) {
            log.error("本期暂不支持老结构流程, wmPoiId: {}", routeParam.getWmPoiId());
        } else {
            log.error("识别门店结构类型失败, 请排查. wmPoiId: {}", routeParam.getWmPoiId());
        }
        return null;
    }
}