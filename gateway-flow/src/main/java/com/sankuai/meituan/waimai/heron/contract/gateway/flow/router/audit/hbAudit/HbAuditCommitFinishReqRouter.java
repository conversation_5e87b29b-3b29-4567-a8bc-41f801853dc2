package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.audit.hbAudit;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.SinglePoiFlowRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.audit.hbAudit.HbAuditCommitFinishReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 蜂鸟归并提审结束
 * @author: chenyihao04
 * @create: 2024-08-22 11:32
 */
@Component
public class HbAuditCommitFinishReqRouter extends SinglePoiFlowRequestRouter {

    @Resource
    private HbAuditCommitFinishReqSender hbAuditCommitFinishReqSender;

    @Override
    public SubDomainEnum routeTargetDomain() {
        return SubDomainEnum.HB;
    }

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setRequestSender(hbAuditCommitFinishReqSender);
        return routeResult;
    }
}