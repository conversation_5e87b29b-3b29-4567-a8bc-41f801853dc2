package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.perf;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.SaveSinglePoiParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfSettleFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PerfDomainFlowRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.PerfBDSettleSaveParamConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractBDSettleSaveParam;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 单店保存请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class PerfContractBDSettleSaveReqSender extends PerfDomainFlowRequestSender<HeronContractBDSettleSaveParam, SaveSinglePoiParam> {

    @Resource
    private BmPerfSettleFlowThriftServiceAdapter bmPerfSettleFlowThriftServiceAdapter;


    @Override
    protected SaveSinglePoiParam buildRemoteParam(SendParam<HeronContractBDSettleSaveParam> param) throws GatewayArgumentException {
        return PerfBDSettleSaveParamConverter.convertSaveParam(param);
    }


    @Override
    protected BmContractPlatformProcessResp sendToPerf(SaveSinglePoiParam remoteParam) throws GatewayAdapterException {
        return bmPerfSettleFlowThriftServiceAdapter.saveSinglePoi(remoteParam);
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.SAVE;
    }

}