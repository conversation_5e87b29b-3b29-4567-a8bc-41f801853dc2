package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.sign.area;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.AreaSinglePoiFlowRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.sign.area.AreaApplySignSuccessReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.sign.areaNew.AreaNewApplySignSuccessReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 发起签约成功路由
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class AreaApplySignSuccessReqRouter extends AreaSinglePoiFlowRequestRouter {

    @Resource
    private AreaApplySignSuccessReqSender areaApplySignSuccessReqSender;

    @Resource
    private AreaNewApplySignSuccessReqSender areaNewApplySignSuccessReqSender;


    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        if (SessionCategoryEnum.PHF.equals(GlobalFlowSessionHandler.getSessionCategory())) {
            return RouteResult.builder().execTag(routeParam.getAreaExecTag()).build();
        }
        return buildNotPhfAreaResult(routeParam, BooleanUtils.isTrue(routeParam.getRouteDimensionParam().getUseAreaNewServiceV1()) ? areaNewApplySignSuccessReqSender : areaApplySignSuccessReqSender);
    }

}