package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.audit.perf;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PerfSinglePoiFlowRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.audit.perf.PerfAuditTaskQueryReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2024-08-22 12:00
 */
@Component
public class PerfAuditTaskQueryReqRouter extends PerfSinglePoiFlowRequestRouter {

    @Resource
    private PerfAuditTaskQueryReqSender perfAuditTaskQueryReqSender;

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        return super.buildDefaultPerfResult(routeParam, perfAuditTaskQueryReqSender);
    }
}