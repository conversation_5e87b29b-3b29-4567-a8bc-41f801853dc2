package com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.save.phf;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.dispatcher.BaseFlowProcessingRequestDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.RouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfMockSaveCheckFlowStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.perf.phf.PerfPhfMockSaveCheckReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.plat.phf.PlatPhfMockSaveCheckReqRouter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/11
 */
@Component
public class PhfMockSaveCheckReqDispatcher extends BaseFlowProcessingRequestDispatcher<PhfMockSaveCheckFlowStepParam> {

    @Resource
    private RouteHelper routeHelper;

    @Override
    protected RouteParam buildRouteParam(PhfMockSaveCheckFlowStepParam originParam) throws GatewayBaseException {
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        SinglePoiFlowRouteParam routeParam = new SinglePoiFlowRouteParam();
        routeParam.setPerfExecTag(session.getContext().getPerfExecTag());
        routeParam.setProcessingHasPerf(session.getContext().getHasPerf());
        return routeParam;
    }

    @Override
    protected List<Class<? extends RequestRouter>> registerRouterClass() {
        return ImmutableList.of(PlatPhfMockSaveCheckReqRouter.class, PerfPhfMockSaveCheckReqRouter.class);
    }
}
