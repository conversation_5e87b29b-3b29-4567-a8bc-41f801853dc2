package com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.processing;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.WmPoiQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.NationalSubsidyPoiHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.BatchApplySignStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.SignItemBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.ApplySignFailStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.SessionFlowTerminateFlowStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.LogisticsFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.sign.BatchApplySignFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.ApplySignFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.SessionFlowTerminateFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.CancelSignOperateType;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractAutoBatchSignParam;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;

/**
 * @description: 自动打包发起签约流程编排
 * @author: chenyihao04
 * @create: 2023-04-26 14:21
 */
@Component
@Slf4j
public class AutoBatchApplySignFlowOrchestration implements LogisticsFlowOrchestration<HeronContractAutoBatchSignParam> {

    @Resource
    private BatchApplySignFlowStep batchApplySignFlowStep;

    @Resource
    private ApplySignFailFlowStep applySignFailFlowStep;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private WmPoiQueryThriftServiceAdapter wmPoiQueryThriftServiceAdapter;

    @Resource
    private NationalSubsidyPoiHelper nationalSubsidyPoiHelper;

    @Resource
    private SessionFlowTerminateFlowStep sessionFlowTerminateFlowStep;


    public OrchestrationExecResult flow(HeronContractAutoBatchSignParam param) {
        // 对于总部增开国补协议场景，增加门店标签的校验，不通过则直接终止全部流程
        if (!batchCheckNationalSubsidy(param)) {
            for (Long wmPoiId : param.getWmPoiIdList()) {
                HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(wmPoiId, SessionCategoryEnum.CORE);
                if (session == null || !session.getSessionScene().equals(ContractCreateAndUpdateSceneEnum.HQ_ADD_NATIONAL_SUBSIDY_AGGREMENT)) {
                    continue;
                }
                GlobalFlowSessionHandler.applyWithSession(session, buildTerminateFlowParam(wmPoiId, param.getOperator()), sessionFlowTerminateFlowStep::execute);
            }
            return OrchestrationExecResult.fail("总部增开国补协议场景，存在标签校验未通的门店，终止全部门店流程:["+param.getWmPoiIdList()+"]");
        }
        //打包签约的校验在老服务有单独接口，api内可以做一些有限的校验
        BatchApplySignStepParam stepParam = buildBatchApplySignBo(param);
        if (CollectionUtils.isNotEmpty(stepParam.getSignItemBoList())) {
            FlowExecResult manualApplySignResult = batchApplySignFlowStep.execute(stepParam);
            if (manualApplySignResult.isFail()) {
                List<Long> failWmPoiIdList = stepParam.getSignItemBoList().stream()
                        .map(SignItemBo::getWmPoiId)
                        .collect(Collectors.toList());
                for (Long wmPoiId : failWmPoiIdList) {
                    HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(wmPoiId, SessionCategoryEnum.CORE);
                    GlobalFlowSessionHandler.applyWithSession(session, buildApplySignFailParam(wmPoiId, param.getOperator()), applySignFailFlowStep::execute);
                }
                return OrchestrationExecResult.fail(BatchApplySignFlowStep.class, manualApplySignResult);
            }
        }
        return OrchestrationExecResult.success();
    }

    /**
     * 对于总部增开国补的场景，判断门店是否全是国补门店，若不是则返回失败
     * @param param
     * @return true 通过校验；false 不通过校验
     */
    private boolean batchCheckNationalSubsidy(HeronContractAutoBatchSignParam param) {
        if (CollectionUtils.isEmpty(param.getWmPoiIdList()) || !ContractCreateAndUpdateSceneEnum.HQ_ADD_NATIONAL_SUBSIDY_AGGREMENT.equals(param.getScene())) {
            return true;
        }
        List<WmPoiAggre> wmPoiAggreList = null;
        try {
            wmPoiAggreList = wmPoiQueryThriftServiceAdapter.batchGetWmPoiAggre(param.getWmPoiIdList(), ImmutableSet.of(
                    WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_BIZ_ORG_CODE, WM_POI_FIELD_LABEL_IDS));
        } catch (GatewayAdapterException e) {
            log.warn("batchCheckNationalSubsidy#查询门店信息失败,wmPoiIdList:{}", param.getWmPoiIdList());
            throw new RuntimeException("查询门店信息失败");
        }
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            List<String> labelList = Lists.newArrayList(wmPoiAggre.getLabel_ids().split(","));
            if (!nationalSubsidyPoiHelper.isHQSupplierPoi(wmPoiAggre.getBiz_org_code(), labelList)) {
                log.warn("batchCheckNationalSubsidy#总部增开国补-自动打包签约时，存在非总部国补的门店,wmPoiId:{}", wmPoiAggre.getWm_poi_id());
                return false;
            }
        }
        return true;
    }

    private BatchApplySignStepParam buildBatchApplySignBo(HeronContractAutoBatchSignParam param) {
        BatchApplySignStepParam batchApplySignStepParam = new BatchApplySignStepParam();
        List<SignItemBo> signItemBoList = new ArrayList<>();
        for (Long wmPoiId : param.getWmPoiIdList()) {
            HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(wmPoiId, SessionCategoryEnum.CORE);
            if (session != null && ContractSessionStatusEnum.AUDIT_PASS.equals(session.getStatus())) {
                Long sessionId = session.getSessionLeafId();
                SignItemBo signItemBo = new SignItemBo();
                signItemBo.setWmPoiId(wmPoiId);
                signItemBo.setSessionId(sessionId);
                signItemBo.setSession(session);
                signItemBoList.add(signItemBo);
            }
        }
        batchApplySignStepParam.setSignItemBoList(signItemBoList);
        batchApplySignStepParam.setOperator(param.getOperator());
        batchApplySignStepParam.setSessionCategory(SessionCategoryEnum.CORE);
        return batchApplySignStepParam;
    }


    private ApplySignFailStepParam buildApplySignFailParam(Long wmPoiId, HeronContractOperator operator) {
        ApplySignFailStepParam applySignFailBo = new ApplySignFailStepParam();
        applySignFailBo.setWmPoiId(wmPoiId);
        applySignFailBo.setFailReason("自动打包签约发起失败");
        applySignFailBo.setCauseDomain(SubDomainEnum.E_CONTRACT);
        applySignFailBo.setOperator(operator);
        applySignFailBo.setSessionCategory(SessionCategoryEnum.CORE);
        return applySignFailBo;
    }

    private SessionFlowTerminateFlowStepParam buildTerminateFlowParam(Long wmPoiId, HeronContractOperator operator) {
        return SessionFlowTerminateFlowStepParam.builder()
                .failReason("总部增开国补协议场景，存在标签校验未通的门店，终止原流程")
                .wmPoiId(wmPoiId)
                .cancelSignOperateType(CancelSignOperateType.NO_CANCEL_SIGN)
                .operator(operator)
                .sessionCategory(SessionCategoryEnum.CORE)
                .targetStatus(ContractSessionStatusEnum.SIGN_FAIL)
                .build();
    }

}