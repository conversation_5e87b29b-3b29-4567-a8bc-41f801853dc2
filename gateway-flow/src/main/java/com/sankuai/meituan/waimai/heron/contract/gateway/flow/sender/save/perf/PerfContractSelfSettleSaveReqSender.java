package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.perf;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.TempSaveSinglePoiSelfSettleParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfSettleFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PerfDomainFlowRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.PerfBusinessIdentityHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.utils.DeserializeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ApplySignActionEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractSelfSettleParam;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 单店自入驻保存请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class PerfContractSelfSettleSaveReqSender extends PerfDomainFlowRequestSender<HeronContractSelfSettleParam, TempSaveSinglePoiSelfSettleParam> {

    @Resource
    private BmPerfSettleFlowThriftServiceAdapter bmPerfSettleFlowThriftServiceAdapter;



    @Override
    protected TempSaveSinglePoiSelfSettleParam buildRemoteParam(SendParam<HeronContractSelfSettleParam> sendParam) {
        HeronContractSelfSettleParam originParam = sendParam.getOriginParam();
        String settleBaseInfo = originParam.getSelfSettleBaseParam();
        TempSaveSinglePoiSelfSettleParam saveParam = DeserializeUtil.deserialize(settleBaseInfo, TempSaveSinglePoiSelfSettleParam.class);
        saveParam.setOperatorParam(OperatorConverter.toPerfOperator(originParam.getOperator()));
        saveParam.setSessionId(GlobalFlowSessionHandler.getSessionId());
        saveParam.setRequestId(sendParam.getRequestLeafId());
        saveParam.setNeedPackageSign(ApplySignActionEnum.LATER.equals(GlobalFlowSessionHandler.getSession().getContext().getApplySignAction()));
        saveParam.setPlatShareInfo(sendParam.getFlowShareInfoMap().get(SubDomainEnum.PLAT));
        saveParam.setBusinessIdentity(GlobalFlowSessionHandler.getPerfBusinessIdentity());
        return saveParam;
    }


    @Override
    protected BmContractPlatformProcessResp sendToPerf(TempSaveSinglePoiSelfSettleParam remoteParam) throws GatewayAdapterException {
        return bmPerfSettleFlowThriftServiceAdapter.tempSaveSinglePoiSelfSettle(remoteParam);
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.SAVE;
    }


}