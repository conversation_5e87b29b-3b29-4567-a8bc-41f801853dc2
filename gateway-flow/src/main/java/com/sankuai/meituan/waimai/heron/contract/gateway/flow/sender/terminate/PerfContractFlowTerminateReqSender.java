package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.terminate;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.ProcessStopSinglePoiParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfSettleFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.TimeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PerfDomainFlowRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.FlowTerminateBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.StatusConverter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @description: 流程终止请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class PerfContractFlowTerminateReqSender extends PerfDomainFlowRequestSender<FlowTerminateBo, ProcessStopSinglePoiParam> {

    @Resource
    private BmPerfSettleFlowThriftServiceAdapter bmPerfSettleFlowThriftServiceAdapter;



    @Override
    protected ProcessStopSinglePoiParam buildRemoteParam(SendParam<FlowTerminateBo> param) {
        FlowTerminateBo originParam = param.getOriginParam();
        ProcessStopSinglePoiParam processStopSinglePoiParam = new ProcessStopSinglePoiParam();
        processStopSinglePoiParam.setOperatorParam(OperatorConverter.toPerfOperator(originParam.getOperator()));
        processStopSinglePoiParam.setWmPoiId(originParam.getWmPoiId());
        processStopSinglePoiParam.setSessionId(Optional.of(GlobalFlowSessionHandler.getSessionId()).orElse(-1L));
        processStopSinglePoiParam.setStopReason(originParam.getFailReason());
        processStopSinglePoiParam.setTargetStatus(StatusConverter.toPerfStatus(originParam.getTargetStatus()));
        processStopSinglePoiParam.setDealTime(TimeUtil.unixTime());
        processStopSinglePoiParam.setRequestId(param.getRequestLeafId());
        return processStopSinglePoiParam;
    }

    @Override
    protected BmContractPlatformProcessResp sendToPerf(ProcessStopSinglePoiParam remoteParam) throws GatewayAdapterException {
        return bmPerfSettleFlowThriftServiceAdapter.processStopSinglePoi(remoteParam);
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.TERMINATE_FLOW;
    }

}