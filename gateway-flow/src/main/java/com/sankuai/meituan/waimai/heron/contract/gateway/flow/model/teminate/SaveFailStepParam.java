package com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 *
 * 保存失败流程节点参数
 * <AUTHOR>
 * @date 2023/6/15
 */
@Data
@NoArgsConstructor
@SuperBuilder
public class SaveFailStepParam extends BaseTerminateFlowStepParam {

    //当前流程是闪购迁移双写或医药迁移双写流程
    private Boolean currentSgOrYyTransferDrProcess;

}
