package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.perf;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.CheckSinglePoiAutoFillParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfSettleFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PerfDomainFlowRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.PerfSceneOpSourceConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.utils.DeserializeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractUpdateAndSceneParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractUpdateSceneExtParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.PerfBusinessIdentityHelper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @description: 换签任务和更新 校验请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class PerfContractUpdateInfoPreCheckReqSender extends PerfDomainFlowRequestSender<HeronContractUpdateAndSceneParam, CheckSinglePoiAutoFillParam> {

    @Resource
    private BmPerfSettleFlowThriftServiceAdapter bmPerfSettleFlowThriftServiceAdapter;



    @Override
    protected CheckSinglePoiAutoFillParam buildRemoteParam(SendParam<HeronContractUpdateAndSceneParam> param) {
        HeronContractUpdateAndSceneParam sceneParam = param.getOriginParam();
        CheckSinglePoiAutoFillParam autoFillParam = DeserializeUtil.deserialize(sceneParam.getUpdateBaseParam(), CheckSinglePoiAutoFillParam.class);
        if (autoFillParam == null) {
            autoFillParam = new CheckSinglePoiAutoFillParam();
        }
        autoFillParam.setOperatorParam(OperatorConverter.toPerfOperator(sceneParam.getOperator()));
        autoFillParam.setSessionId(GlobalFlowSessionHandler.getSessionId());
        autoFillParam.setWmPoiId(sceneParam.getWmPoiId());
        autoFillParam.setOperatorSourceCode(PerfSceneOpSourceConverter.convertOpSource(sceneParam.getUpdateScene()).getCode());
        autoFillParam.setOperateMissionType(Optional.ofNullable(sceneParam.getUpdateSceneExtParam().getPlatMissionType()).orElse(0));
        autoFillParam.setPlatShareInfo(param.getFlowShareInfoMap().get(SubDomainEnum.PLAT));
        autoFillParam.setWmCustomerId(Optional.ofNullable(sceneParam.getUpdateSceneExtParam()).map(HeronContractUpdateSceneExtParam::getTargetCustomerId).orElse(null));
        autoFillParam.setBusinessIdentity(GlobalFlowSessionHandler.getPerfBusinessIdentity());
        return autoFillParam;
    }

    @Override
    protected BmContractPlatformProcessResp sendToPerf(CheckSinglePoiAutoFillParam remoteParam) throws GatewayAdapterException {
        return bmPerfSettleFlowThriftServiceAdapter.tempSaveCheckSinglePoiAutoFill(remoteParam);
    }


    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.SAVE_CHECK;
    }

}