package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.area;

import com.sankuai.meituan.banma.poi.sparea.thrift.constants.BmSpAreaLogisticsOpSceneEnum;
import com.sankuai.meituan.banma.poi.sparea.thrift.param.save.BmOpenSpAreaSelfSettleSaveRequest;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmAreaFlowThriftServiceAdapterOld;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.AreaDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.utils.DeserializeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractMultiSettleParam;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 多店入驻保存请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class AreaMultiSettleSaveReqSender extends AreaDomainRequestSender<HeronContractMultiSettleParam, BmOpenSpAreaSelfSettleSaveRequest> {

    @Resource
    private BmAreaFlowThriftServiceAdapterOld bmAreaFlowThriftServiceAdapterOld;


    @Override
    protected BmOpenSpAreaSelfSettleSaveRequest buildRemoteParam(SendParam<HeronContractMultiSettleParam> param) {
        HeronContractMultiSettleParam originParam = param.getOriginParam();
        String settleBaseInfo = originParam.getMultiSettleBaseParam();
        BmOpenSpAreaSelfSettleSaveRequest saveParam = DeserializeUtil.deserialize(settleBaseInfo, BmOpenSpAreaSelfSettleSaveRequest.class);
        saveParam.setOperator(OperatorConverter.toAreaOperator(originParam.getOperator()));
        saveParam.setSessionId(GlobalFlowSessionHandler.getSessionId());
        saveParam.setBmSpAreaLogisticsOpSceneEnum(BmSpAreaLogisticsOpSceneEnum.MULTI_SELF_SETTLE);
        return saveParam;
    }



    @Override
    protected void sendToSpArea(BmOpenSpAreaSelfSettleSaveRequest remoteParam) throws GatewayAdapterException {
        bmAreaFlowThriftServiceAdapterOld.initAndSave(remoteParam);
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.SAVE;
    }

    @Override
    protected Boolean needRecordParam() {
        return true;
    }

}