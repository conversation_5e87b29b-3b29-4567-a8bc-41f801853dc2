package com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.save;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.dispatcher.BaseFlowProcessingRequestDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.area.AreaCustomerChangePreCheckReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.perf.PerfContractCustomerChangePreCheckReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.plat.PlatContractCustomerChangePreCheckReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractUpdateAndSceneParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @description: 校验客户切换不下线场景
 * @author: chenyihao04
 * @create: 2023-07-21 16:59
 */
@Slf4j
@Component
public class CustomerChangePreCheckReqDispatcher extends BaseFlowProcessingRequestDispatcher<HeronContractUpdateAndSceneParam> {

    @Resource
    private RouteHelper routeHelper;

    @Override
    protected List<Class<? extends RequestRouter>> registerRouterClass() {
        return ImmutableList.of(PlatContractCustomerChangePreCheckReqRouter.class, PerfContractCustomerChangePreCheckReqRouter.class, AreaCustomerChangePreCheckReqRouter.class);
    }

    @Override
    protected SinglePoiFlowRouteParam buildRouteParam(HeronContractUpdateAndSceneParam originParam) throws GatewayAdapterException {
        return routeHelper.buildFlowRouteParam(originParam.getWmPoiId());
    }

    @Override
    protected Map<SubDomainEnum, Integer> getGivenOrdinal() {
        return PLAT_FIRST_EXECUTE_ORDINAL;
    }
}