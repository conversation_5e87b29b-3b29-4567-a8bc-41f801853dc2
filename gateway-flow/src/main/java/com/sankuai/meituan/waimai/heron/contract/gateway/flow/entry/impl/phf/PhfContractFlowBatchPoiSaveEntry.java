package com.sankuai.meituan.waimai.heron.contract.gateway.flow.entry.impl.phf;

import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PhfThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.batchflow.BatchSignFlowRedisHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.config.MccConfig;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.BatchSignFlowSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBusinessException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayRuntimeException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLockHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.mafka.MafkaMessageSendService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.GatewayNumberUtils;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.GatewayPreconditions;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.alarm.AlarmLevel;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.alarm.GatewayAlarmBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.alarm.GatewayAlarmUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.gray.PhfMigrateSaveGrayDecider;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.LogisticsExtTagsHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.helper.PhfSessionBuildHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfBatchSaveWithSessionParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfBatchSplitSingleSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfMigrateToDrParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.SessionFlowTerminateFlowStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.processing.phf.PhfAutoBatchApplySignFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.save.phf.PhfBatchSaveFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.phf.PhfMigrateRollbackProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.phf.PhfMigrateSplitForSaveProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.phf.PhfMigrateToDrDataProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.plat.phf.PlatPhfBatchSplitSingleNeedReApplyReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.SessionFlowTerminateFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ApplySignActionEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.CancelSignOperateType;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractMigrateStageEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.PhfBatchOpTypeEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.PhfContractOriginErrorPoiParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfContractDrMigrateCleanParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractBatchFlowSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractInfoParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.SinglePoiResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.PhfBatchOpResultMessageStruct;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.PhfContractBatchSaveExtParamStruct;
import com.sankuai.tsp.phf.contract.dto.contract.CancelSignParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/9
 */
@Service
@Slf4j
public class PhfContractFlowBatchPoiSaveEntry {

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private PhfMigrateSaveGrayDecider phfMigrateSaveGrayDecider;

    @Resource
    private BatchSignFlowRedisHelper batchSignFlowRedisHelper;

    @Resource
    private PhfBatchSaveFlowOrchestration phfBatchSaveFlowOrchestration;

    @Resource
    private PhfAutoBatchApplySignFlowOrchestration phfAutoBatchApplySignFlowOrchestration;

    @Resource
    private EntryAndProcessorLockHandler entryAndProcessorLockHandler;

    @Resource
    private PlatPhfBatchSplitSingleNeedReApplyReqSender platPhfBatchSplitSingleNeedReApplyReqSender;

    @Resource
    private MafkaMessageSendService mafkaMessageSendService;

    @Resource
    private LogisticsExtTagsHelper logisticsExtTagsHelper;

    @Resource
    private PhfMigrateRollbackProcessor phfMigrateRollbackProcessor;


    @Resource
    private PhfThriftServiceAdapter phfThriftServiceAdapter;

    @Resource
    private PhfMigrateSplitForSaveProcessor phfMigrateSplitForSaveProcessor;


    @Resource
    private SessionFlowTerminateFlowStep sessionFlowTerminateFlowStep;

    @Resource
    private PhfSessionBuildHelper phfSessionBuildHelper;

    @Resource
    private PhfMigrateToDrDataProcessor phfMigrateToDrDataProcessor;


    private final ExecutorService batchSaveExecutorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 2, Runtime.getRuntime().availableProcessors() * 2, 2L,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(10000), new ThreadPoolExecutor.DiscardPolicy());

    private final ExecutorService batchMigrateOrRollbackExecutorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors() * 2, 20L,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(10000), new ThreadPoolExecutor.DiscardPolicy());


    public void batchSave(PhfContractBatchFlowSaveParam param) throws GatewayBaseException, InterruptedException {
        this.checkParam(param);
        ContractMigrateStageEnum decideMigrateStage = phfMigrateSaveGrayDecider.judgeSaveMigrateStage(param.getWmPoiIdList(), param.getContractType());
        if (ContractMigrateStageEnum.NO_MIGRATE.equals(decideMigrateStage)) {
            log.error("未开启迁移，需检查为何调用网关进行保存 batchFlowSaveParam={}", JacksonUtil.writeAsJsonStr(param));
            return;
        }
        log.info("拼好饭批量保存合同 batchSignTaskId={}, 门店数量={}", param.getBatchSignTaskId(), param.getContractInfoList().size());
        //校验批量任务是否重复执行，重复执行直接return
        if (!batchSignFlowRedisHelper.canStartBatchSave(String.valueOf(param.getBatchSignTaskId()), BatchSignFlowSceneEnum.PHF_BATCH_SIGN_FLOW_SCENE, param.getWmPoiIdList())) {
            log.warn("批量任务重复调用，无需再处理 batchSignTaskId={}", param.getBatchSignTaskId());
            return;
        }
//        ContractMigrateStageEnum migrateStageEnum = phfMigrateSaveGrayDecider.judgeSaveMigrateStage(param.getWmPoiIdList(), param.getContractType());
        //构建session，如果门店未清洗数据到一体化，则将拼好饭数据同步到一体化
        List<HeronContractGatewaySession> allNewSessionList = buildSessionAndMigrateData(param, decideMigrateStage);
        //执行保存
        doBatchSave(param, allNewSessionList, decideMigrateStage);

    }


    private List<HeronContractGatewaySession> buildSessionAndMigrateData(PhfContractBatchFlowSaveParam param, ContractMigrateStageEnum migrateStageEnum) throws InterruptedException, GatewayBusinessException {
        //先构建session，需要考虑建设保存重试能力
        //排个序，保证构建签约的门店列表是有序的
        List<Long> wmPoiIdList = Lists.newArrayList(param.getWmPoiIdList());
        Collections.sort(wmPoiIdList);
        List<List<Long>> partitionPoiIdList = Lists.partition(wmPoiIdList, MccConfig.getPhfBatchSignGroupPoiLimit());
        CountDownLatch countDownLatch = new CountDownLatch(partitionPoiIdList.size());
        AtomicBoolean createSessionSuccess = new AtomicBoolean(true);
        Map<Long, PhfContractInfoParam> poiIdAndContractInfoParamMap = param.getContractInfoList().stream().collect(Collectors.toMap(PhfContractInfoParam::getWmPoiId, Function.identity()));
        List<HeronContractGatewaySession> allNewSessionList = Collections.synchronizedList(Lists.newArrayList());
        for (int i = 1; i <= partitionPoiIdList.size(); i++) {
            List<Long> poiIdList = partitionPoiIdList.get(i - 1);
            batchSaveExecutorService.submit(() -> {
                try {
                    if (!createSessionSuccess.get()) {
                        return;
                    }
                    for (Long wmPoiId : poiIdList) {
                        //判断门店是否已清洗数据，未清洗数据的门店需要调用拼好饭的清洗接口
                        //判断是否需要终止流程
                        SendParam<PhfBatchSplitSingleSaveParam> sendParam = new SendParam<>(PhfBatchSplitSingleSaveParam.build(wmPoiId, param, poiIdAndContractInfoParamMap));
                        SendResult needReapplyResult = platPhfBatchSplitSingleNeedReApplyReqSender.sendRequest(sendParam);
                        boolean needReapply = JacksonUtil.readValueNotNull(needReapplyResult.getSendAdditionalInfo(), Boolean.class);
                        HeronContractGatewaySession lastSession = heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(wmPoiId, SessionCategoryEnum.PHF);
                        if (lastSession != null) {
                            if (needReapply) {
                                SessionFlowTerminateFlowStepParam terminateFlowStepParam = SessionFlowTerminateFlowStepParam.builder()
                                        .sessionId(lastSession.getSessionLeafId())
                                        .failReason("用户发起重新签约流程，终止原流程")
                                        .wmPoiId(lastSession.getWmPoiId())
                                        .cancelSignOperateType(CancelSignOperateType.NEED_CANCEL_SIGN)
                                        .operator(param.getOperator())
                                        .sessionCategory(lastSession.getSessionCategory())
                                        .targetStatus(ContractSessionStatusEnum.SIGN_FAIL)
                                        .build();
                                GlobalFlowSessionHandler.applyWithSession(lastSession, terminateFlowStepParam, sessionFlowTerminateFlowStep::execute);
                            }
                        }
                    }
                    List<HeronContractGatewaySession> sessionList = this.batchBuildSession(migrateStageEnum, poiIdList, param);
                    allNewSessionList.addAll(sessionList);
                    this.sendSessionNotice(sessionList);
                    this.batchMigrate(migrateStageEnum, poiIdList, poiIdAndContractInfoParamMap);
                    for (Long wmPoiId : poiIdList) {
                        logisticsExtTagsHelper.savePhfTagsForSave(wmPoiId, migrateStageEnum);

                    }
                } catch (Exception e) {
                    log.error("构建session失败 poiIdList={}", poiIdList, e);
                    createSessionSuccess.set(false);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        if (!createSessionSuccess.get()) {
            log.error("构建session失败，流程终止 batchSignTaskId={}", param.getBatchSignTaskId());
            GatewayAlarmUtil.alarmMessage("拼好饭批量保存, 构建session失败，流程终止 batchSignTaskId=" + param.getBatchSignTaskId());
            if (ContractMigrateStageEnum.BOTH_RUN.equals(migrateStageEnum)) {
                this.bothRunCheckConsistenceOrRollback(param.getBatchSignTaskId(), param.getWmPoiIdList());
            }
            //删除redis缓存，流程可重新发起
            batchSignFlowRedisHelper.removeBatchFlow(String.valueOf(param.getBatchSignTaskId()), BatchSignFlowSceneEnum.PHF_BATCH_SIGN_FLOW_SCENE);
            throw new GatewayBusinessException(-1, "构建session失败，流程终止");
        }
        return allNewSessionList;
    }


    private void doBatchSave(PhfContractBatchFlowSaveParam param, List<HeronContractGatewaySession> allNewSessionList, ContractMigrateStageEnum migrateStageEnum) {
        //使用线程池线程异步做批量保存
        batchSaveExecutorService.submit(() -> {
            try {
                entryAndProcessorLockHandler.batchApplyWithLock(param.getWmPoiIdList(), SessionCategoryEnum.PHF, null, p -> {
                    PhfBatchSaveWithSessionParam saveWithSessionParam = new PhfBatchSaveWithSessionParam();
                    saveWithSessionParam.setSaveParam(param);
                    saveWithSessionParam.setSessionList(allNewSessionList);
                    // 执行批量保存流程
                    OrchestrationExecResult<List<SinglePoiResult>> result = phfBatchSaveFlowOrchestration.flow(saveWithSessionParam);
                    log.info("批量保存流程执行完成 wmPoiIdList={}", JacksonUtil.writeAsJsonStr(param.getWmPoiIdList()));
                    // 判断是否可以开始批量签约
                    if (batchSignFlowRedisHelper.canStartBatchSign(String.valueOf(param.getBatchSignTaskId()), BatchSignFlowSceneEnum.PHF_BATCH_SIGN_FLOW_SCENE)) {
                        phfAutoBatchApplySignFlowOrchestration.flow(String.valueOf(param.getBatchSignTaskId()));
                    }
                    sendBatchSignNotice(param, result);
                    if (ContractMigrateStageEnum.BOTH_RUN.equals(migrateStageEnum)) {
                        compareAndRollback(param, result);
                    }
                    return null;
                });
            } catch (Throwable e) {
                log.error("批量保存流程执行失败 wmPoiIdList={}", JacksonUtil.writeAsJsonStr(param.getWmPoiIdList()), e);
                if (ContractMigrateStageEnum.BOTH_RUN.equals(migrateStageEnum)) {
                    this.bothRunCheckConsistenceOrRollback(param.getBatchSignTaskId(), param.getWmPoiIdList());
                }
                throw new GatewayRuntimeException(e);
            }

        });
    }

    private void sendSessionNotice(List<HeronContractGatewaySession> sessionList) {
        if (CollectionUtils.isEmpty(sessionList)) {
            return;
        }
        for (HeronContractGatewaySession session : sessionList) {
            phfSessionBuildHelper.sendSessionNotice(session);
        }
    }


    private List<HeronContractGatewaySession> batchBuildSession(ContractMigrateStageEnum migrateStageEnum, List<Long> wmPoiIdList, PhfContractBatchFlowSaveParam param) throws GatewayAdapterException, GatewayArgumentException {
        return this.buildSessionList(migrateStageEnum, wmPoiIdList, param);
    }


    private List<HeronContractGatewaySession> buildSessionList(ContractMigrateStageEnum migrateStageEnum, List<Long> wmPoiIdList, PhfContractBatchFlowSaveParam param) throws GatewayAdapterException, GatewayArgumentException {
        PhfContractBatchSaveExtParamStruct extParamStruct = JacksonUtil.readValueNotNull(param.getProcessExtParam(), PhfContractBatchSaveExtParamStruct.class);
        Map<Long, PhfContractInfoParam> poiContractInfoMap = Optional.ofNullable(param.getContractInfoList()).orElse(Lists.newArrayList()).stream()
                .collect(Collectors.toMap(PhfContractInfoParam::getWmPoiId, Function.identity(), (x1, x2) -> x1));
        List<HeronContractGatewaySession> sessionList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdList) {
            HeronContractGatewaySession session = phfSessionBuildHelper.buildBatchSession(wmPoiId, migrateStageEnum, extParamStruct, ContractCreateAndUpdateSceneEnum.PHF_BATCH_PROCESS);
            // 设置申请签约动作，根据门店数量判断是否为批量签约
            session.getContext().setPhfContractType(param.getContractType());
            session.getContext().setApplySignAction(param.getWmPoiIdList().size() > 1 ? ApplySignActionEnum.AUTO_BATCH : ApplySignActionEnum.DIRECT);
            session.getContext().setPhfContractId(Optional.ofNullable(poiContractInfoMap.get(wmPoiId)).map(PhfContractInfoParam::getPhfContractId).orElse(null));
            session.getContext().setPhfOriginRecordKey(Optional.ofNullable(poiContractInfoMap.get(wmPoiId)).map(PhfContractInfoParam::getEcontractRecordKey).orElse(null));
            session.setBatchRelationId(String.valueOf(param.getBatchSignTaskId()));
            session.setOpId(param.getOperator().getOpId());
            session.setOpName(Optional.ofNullable(param.getOperator().getOpName()).orElse(""));
            sessionList.add(session);

        }
        return sessionList;
    }


    private void checkParam(PhfContractBatchFlowSaveParam param) throws GatewayArgumentException {
        GatewayPreconditions.checkNotNull(param, "单店保存参数不能为空");
        GatewayPreconditions.checkArgument(param.getBatchSignTaskId() != null && param.getBatchSignTaskId() > 0, "批量签约任务ID不能为空");
        GatewayPreconditions.checkArgument(CollectionUtils.isNotEmpty(param.getWmPoiIdList()), "批量保存参数门店id列表不能为空");
        GatewayPreconditions.checkNotBlank(param.getContractType(), "合同类型不能为空");
        GatewayPreconditions.checkNotNull(param.getEffectBySchedule(), "是否按预约日期生效不能为空");
        GatewayPreconditions.checkNotNull(param.getScheduledEffectiveTime(), "生效时间不能为空");
        GatewayPreconditions.checkNotBlank(param.getProcessExtParam(), "流程扩展参数不能为空");
        GatewayPreconditions.checkArgument(JacksonUtil.readValue(param.getProcessExtParam(), PhfContractBatchSaveExtParamStruct.class) != null, "流程扩展参数反序列化为对象失败");
        GatewayPreconditions.checkNotNull(param.getOperator(), "操作人不能为空");
        GatewayPreconditions.checkArgument(param.getOperator().getOpId() != null, "操作人ID不能为空");
        GatewayPreconditions.checkNotNull(param.getOperator().getOpSource(), "操作人来源不能为空");
    }


    private void sendBatchSignNotice(PhfContractBatchFlowSaveParam param, OrchestrationExecResult<List<SinglePoiResult>> result) {
        log.info("发送批量保存结果通知 sendBatchSignNotice param={} result={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(result));
        PhfBatchOpResultMessageStruct message = new PhfBatchOpResultMessageStruct();
        message.setOpType(PhfBatchOpTypeEnum.BATCH_SAVE.name());
        message.setBatchBizId(param.getBatchSignTaskId());
        List<PhfBatchOpResultMessageStruct.PoiDetailInfo> successPoiList = Lists.newArrayList();
        message.setSuccessPoiList(successPoiList);
        List<PhfBatchOpResultMessageStruct.PoiDetailInfo> errorPoiList = Lists.newArrayList();
        message.setErrorPoiList(errorPoiList);
        Map<Long, PhfContractInfoParam> contractInfoParamMap = param.getContractInfoList().stream()
                .collect(Collectors.toMap(PhfContractInfoParam::getWmPoiId, Function.identity()));
        if (CollectionUtils.isNotEmpty(result.getData())) {
            for (SinglePoiResult singlePoiResult : result.getData()) {
                PhfBatchOpResultMessageStruct.PoiDetailInfo poiDetailInfo = new PhfBatchOpResultMessageStruct.PoiDetailInfo();
                poiDetailInfo.setContractId(contractInfoParamMap.get(singlePoiResult.getWmPoiId()).getPhfContractId());
                poiDetailInfo.setWmPoiId(singlePoiResult.getWmPoiId());
                if (singlePoiResult.getResult().getSuccess()) {
                    successPoiList.add(poiDetailInfo);
                } else {
                    poiDetailInfo.setMessage(singlePoiResult.getResult().getMsg());
                    errorPoiList.add(poiDetailInfo);
                }
            }
        }
        mafkaMessageSendService.sendPhfBatchOpMessage(message);
    }


    private void compareAndRollback(PhfContractBatchFlowSaveParam param, OrchestrationExecResult<List<SinglePoiResult>> newFlowResultList) {
        boolean needRollback = false;
        if (CollectionUtils.isEmpty(newFlowResultList.getData())) {
            if (CollectionUtils.isEmpty(param.getDrOriginErrorPoiList()) || param.getDrOriginErrorPoiList().size() != param.getWmPoiIdList().size()) {
                needRollback = true;
            }
        } else {
            Set<Long> newFlowErrorPoiSet = newFlowResultList.getData().stream()
                    .filter(singlePoiResult -> BooleanUtils.isNotTrue(singlePoiResult.getResult().getSuccess()))
                    .map(SinglePoiResult::getWmPoiId)
                    .collect(Collectors.toSet());

            List<SinglePoiResult> newFlowErrorResultList = newFlowResultList.getData().stream()
                    .filter(singlePoiResult -> BooleanUtils.isNotTrue(singlePoiResult.getResult().getSuccess()))
                    .collect(Collectors.toList());
            log.info("compareAndRollback newFlowErrorPoiSet={}, newFlowErrorResultList={}", newFlowErrorPoiSet, JacksonUtil.writeAsJsonStr(newFlowErrorResultList));
            Set<Long> oldFlowErrorPoiSet = Optional.ofNullable(param.getDrOriginErrorPoiList())
                    .map(oldErrorList -> oldErrorList.stream()
                            .map(PhfContractOriginErrorPoiParam::getWmPoiId)
                            .collect(Collectors.toSet()))
                    .orElse(new HashSet<>());
            needRollback = !CollectionUtils.isEqualCollection(newFlowErrorPoiSet, oldFlowErrorPoiSet);
        }
        log.info("compareAndRollback batchSignTaskId={} needRollback={}", param.getBatchSignTaskId(), needRollback);
        if (needRollback) {
            log.error("批量发起签约 compareAndRollback 拼好饭双写对比不一致 param={}", JacksonUtil.writeAsJsonStr(param));
            this.bothRunCheckConsistenceOrRollback(param.getBatchSignTaskId(), param.getWmPoiIdList());
        }
    }

    private void bothRunCheckConsistenceOrRollback(Long batchSignTaskId, List<Long> batchWmPoiIdList) {

        batchMigrateOrRollbackExecutorService.submit(() -> {
            if (MccConfig.getPhfBothRunFlowConsistency()) {
                log.info("拼好饭批量保存双写链路对比不一致，新旧流程进行流程终止 batchSignTaskId={} batchWmPoiIdList={}", batchSignTaskId, batchWmPoiIdList);
                for (Long wmPoiId : batchWmPoiIdList) {
                    try {
                        CancelSignParamDTO cancelSignParamDTO = new CancelSignParamDTO();
                        cancelSignParamDTO.setWmPoiId(wmPoiId);
                        cancelSignParamDTO.setOpUname("contractGateway");
                        phfThriftServiceAdapter.cancelSign(cancelSignParamDTO);
                    } catch (Exception e) {
                        log.warn("双写批量保存失败，终止拼好饭原流程也失败 wmPoiId={}", wmPoiId, e);
                    }
                }

            } else {
                log.info("拼好饭批量保存双写链路对比不一致进行回滚 batchSignTaskId={} batchWmPoiIdList={}", batchSignTaskId, batchWmPoiIdList);
                for (Long wmPoiId : batchWmPoiIdList) {
                    batchRollback(Collections.singletonList(wmPoiId), "双写批量保存失败");
                }
            }
        });
        try {
            GatewayAlarmBo alarmBo = new GatewayAlarmBo();
            alarmBo.setAlarmTitle("拼好饭批量保存新旧链路结果不一致");
            alarmBo.setAlarmLevel(AlarmLevel.P2);
            alarmBo.setTraceId(Tracer.id());
            alarmBo.setAlarmMessage("拼好饭批量保存新旧链路结果不一致，batchSignTaskId=" + batchSignTaskId + " wmPoiIdList=" + StringUtils.substring(batchWmPoiIdList.toString(), 0, 500));
            GatewayAlarmUtil.dxAlarm(alarmBo);
        } catch (Exception e) {
            log.info("拼好饭批量保存新旧流程结果不一致告警异常 batchSignTaskId={}", batchSignTaskId, e);
        }

    }

    private void batchMigrate(ContractMigrateStageEnum migrateStageEnum, List<Long> wmPoiIdList, Map<Long, PhfContractInfoParam> poiIdAndContractInfoParamMap) {
        if (ContractMigrateStageEnum.BOTH_RUN.equals(migrateStageEnum)) {
            this.migrateToDr(wmPoiIdList, poiIdAndContractInfoParamMap);
        } else if (ContractMigrateStageEnum.MIGRATE_NEW.equals(migrateStageEnum)) {
            this.migrateToSplit(wmPoiIdList);
        }

    }


    private void migrateToDr(List<Long> wmPoiIdList, Map<Long, PhfContractInfoParam> poiIdAndContractInfoParamMap) {
        if (MccConfig.getPhfMigrateBothRunFinish()) {
            return;
        }
        AtomicBoolean migrateSuccess = new AtomicBoolean(true);
        try {
            Map<Long, ContractMigrateStageEnum> poiAndMigrateStageMap = phfMigrateSaveGrayDecider.batchJudgeCurrentMigrateStage(wmPoiIdList);
            List<Long> noMigratePoiList = wmPoiIdList.stream()
                    .filter(wmPoiId -> ContractMigrateStageEnum.NO_MIGRATE.equals(poiAndMigrateStageMap.get(wmPoiId)) || (poiAndMigrateStageMap.get(wmPoiId) == null))
                    .collect(Collectors.toList());
            List<List<Long>> partitionPoiList = Lists.partition(noMigratePoiList, 10);
            CountDownLatch countDownLatch = new CountDownLatch(partitionPoiList.size());

            for (List<Long> subPoiIdList : partitionPoiList) {
                batchMigrateOrRollbackExecutorService.submit(() -> {
                    log.info("拼好饭批量保存 batchMigrate 未迁移的门店为 noMigratePoiList={}", noMigratePoiList);
                    try {
                        if (!migrateSuccess.get()) {
                            log.info("拼好饭批量保存 batchMigrate 已有失败门店，提前结束");
                            return;
                        }
                        Map<Long, List<PhfContractDrMigrateCleanParam>> poiMigrateParamMap = phfThriftServiceAdapter.queryPurgeData(subPoiIdList);
                        if (MapUtils.isEmpty(poiMigrateParamMap)) {
                            return;
                        }

                        for (Map.Entry<Long, List<PhfContractDrMigrateCleanParam>> entry : poiMigrateParamMap.entrySet()) {
                            Long wmPoiId = entry.getKey();
                            List<PhfContractDrMigrateCleanParam> migrateCleanParamList = entry.getValue();
                            PhfContractInfoParam contractInfoParam = poiIdAndContractInfoParamMap.get(wmPoiId);
                            if (CollectionUtils.isEmpty(migrateCleanParamList) || contractInfoParam == null) {
                                continue;
                            }
                            List<PhfContractDrMigrateCleanParam> needMigrateParamList = Lists.newArrayList();
                            for (PhfContractDrMigrateCleanParam migrateCleanParam : migrateCleanParamList) {
                                if (GatewayNumberUtils.isSameNumber(contractInfoParam.getPhfContractId(), migrateCleanParam.getPhfContractId())) {
                                    if (CollectionUtils.isNotEmpty(migrateCleanParam.getFlowStationParamList())) {
                                        PhfContractDrMigrateCleanParam onlyCleanStationParam = new PhfContractDrMigrateCleanParam();
                                        onlyCleanStationParam.setFlowStationParamList(migrateCleanParam.getFlowStationParamList());
                                        onlyCleanStationParam.setWmPoiId(wmPoiId);
                                        needMigrateParamList.add(onlyCleanStationParam);
                                    }
                                } else {
                                    needMigrateParamList.add(migrateCleanParam);
                                }
                            }
                            if (CollectionUtils.isNotEmpty(needMigrateParamList)) {
                                phfMigrateToDrDataProcessor.process(PhfMigrateToDrParam.builder().poiContractParamList(needMigrateParamList).wmPoiId(wmPoiId).build());
                                List<Long> migrateContractIdList = needMigrateParamList.stream()
                                        .map(PhfContractDrMigrateCleanParam::getPhfContractId)
                                        .filter(GatewayNumberUtils::isPositiveNumber)
                                        .collect(Collectors.toList());
                                phfThriftServiceAdapter.checkContract(wmPoiId, migrateContractIdList);
                            }
                        }

                    } catch (Exception e) {
                        log.warn("batchMigrate 迁移拼好饭门店双写失败 subPoiIdList={}", subPoiIdList, e);
                        GatewayAlarmUtil.alarmMessage("批量保存清洗门店双写失败，门店:" + StringUtils.substring(subPoiIdList.toString(), 0, 100) + "，异常信息:" + e.getMessage());
                        migrateSuccess.set(false);
                    } finally {
                        countDownLatch.countDown();
                    }

                });
            }

            countDownLatch.await();
        } catch (Exception e) {
            log.info("拼好饭批量保存 双写清洗门店存在异常门店 wmPoiIdList={}", wmPoiIdList, e);
            migrateSuccess.set(false);
        }
        if (!migrateSuccess.get()) {
            batchRollback(wmPoiIdList, "双写清洗门店存在异常门店");
            throw new RuntimeException("拼好饭批量保存双写清洗门店存在异常门店");
        }
    }

    private void migrateToSplit(List<Long> wmPoiIdList) {
        try {
            Map<Long, ContractMigrateStageEnum> poiAndMigrateStageMap = phfMigrateSaveGrayDecider.batchJudgeCurrentMigrateStage(wmPoiIdList);
            List<Long> noMigratePoiList = wmPoiIdList.stream()
                    .filter(wmPoiId -> ContractMigrateStageEnum.BOTH_RUN.equals(poiAndMigrateStageMap.get(wmPoiId)))
                    .collect(Collectors.toList());
            List<List<Long>> partitionPoiList = Lists.partition(noMigratePoiList, 10);
            CountDownLatch countDownLatch = new CountDownLatch(partitionPoiList.size());
            for (List<Long> subPoiIdList : partitionPoiList) {
                batchMigrateOrRollbackExecutorService.submit(() -> {
                    try {
                        for (Long wmPoiId : subPoiIdList) {
                            try {
                                phfMigrateSplitForSaveProcessor.migrateSplit(wmPoiId);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                    } finally {
                        countDownLatch.countDown();
                    }
                });

            }

            countDownLatch.await();
        } catch (Exception e) {
            log.info("拼好饭批量保存 拆分清洗门店存在异常门店 wmPoiIdList={}", wmPoiIdList, e);
            throw new RuntimeException(e);
        }
    }

    private void batchRollback(List<Long> wmPoiIdList, String remark) {
        for (Long wmPoiId : wmPoiIdList) {
            try {
                phfMigrateRollbackProcessor.migrateRollback(wmPoiId);
            } catch (Exception e) {
                log.warn("batchRollback {} 回滚失败 wmPoiId={}", remark, wmPoiId, e);
            }
        }
    }

}
