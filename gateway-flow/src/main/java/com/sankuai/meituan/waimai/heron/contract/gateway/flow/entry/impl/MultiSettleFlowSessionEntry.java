package com.sankuai.meituan.waimai.heron.contract.gateway.flow.entry.impl;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.bo.SessionContextBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.leaf.LeafIdGenerator;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.GrayDecideParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.monitor.CatLogMonitor;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.entry.AbstractLogisticsSaveFlowBaseEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.save.MultiSettleSaveFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.utils.DeserializeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ApplySignActionEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.OperateSourceEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractMultiSettleParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.save.ContractSaveResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.MultiSettleBaseParamStruct;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.MultiPoiSelfSettleTempSaveRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.ServiceBrandProductParamRequestDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description: 多店（自）入驻
 * @author: chenyihao04
 * @create: 2023-04-26 10:03
 */
@Service
public class MultiSettleFlowSessionEntry extends AbstractLogisticsSaveFlowBaseEntry<HeronContractMultiSettleParam, ContractSaveResult> {

    @Resource
    private MultiSettleSaveFlowOrchestration multiSettleSaveFlowOrchestration;

    @Resource
    private CatLogMonitor catLogMonitor;
    @Override
    protected ContractSaveResult doSaveFlow(HeronContractMultiSettleParam saveRequestParam) {
        catLogMonitor.catMonitor("multi_settle_save", saveRequestParam.getWmPoiId());
        OrchestrationExecResult execResult = multiSettleSaveFlowOrchestration.flow(saveRequestParam);
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        if (execResult.getSuccess()) {
            return ContractSaveResult.builder().success(true).sessionId(session.getSessionLeafId()).build();
        } else {
            return ContractSaveResult.builder().success(false).code(-1).sessionId(session.getSessionLeafId()).message(execResult.getMessage()).build();
        }
    }

    protected void fillSession(HeronContractGatewaySession session, HeronContractMultiSettleParam saveRequestParam) throws GatewayBaseException {
        SessionContextBo sessionContextBo = SessionContextBo.newFlowSession();
        sessionContextBo.setApplySignAction(ApplySignActionEnum.LATER);
        if (Objects.nonNull(saveRequestParam.getSettleExtParam()) && Objects.nonNull(saveRequestParam.getSettleExtParam().getDrOriginFlowParam())
                && Objects.nonNull(saveRequestParam.getSettleExtParam().getDrOriginFlowParam().getSgTransferOriginContractVersionId())) {
            sessionContextBo.setSgTransferContractVersionId(saveRequestParam.getSettleExtParam().getDrOriginFlowParam().getSgTransferOriginContractVersionId());
        }
        if (Objects.nonNull(saveRequestParam.getSettleExtParam()) && Objects.nonNull(saveRequestParam.getSettleExtParam().getDrOriginFlowParam())
                && Objects.nonNull(saveRequestParam.getSettleExtParam().getDrOriginFlowParam().getYyTransferOriginContractVersionId())) {
            sessionContextBo.setYyTransferContractVersionId(saveRequestParam.getSettleExtParam().getDrOriginFlowParam().getYyTransferOriginContractVersionId());
        }

        session.setWmPoiId(saveRequestParam.getWmPoiId());
        session.setSessionScene(matchScene(saveRequestParam));
        session.setContext(sessionContextBo);
        session.setOpId(saveRequestParam.getOperator().getOpId());
        session.setOpName(saveRequestParam.getOperator().getOpName());
        session.setBatchRelationId(LeafIdGenerator.generateBatchRelationId());
        GrayDecideParam grayDecideParam = buildMultiSettleGrayParam(saveRequestParam);
        super.fillSessionContextGrayInfo(session, grayDecideParam);
    }

    private GrayDecideParam buildMultiSettleGrayParam(HeronContractMultiSettleParam saveRequestParam) throws GatewayAdapterException {
        MultiPoiSelfSettleTempSaveRequestDTO multiPoiSelfSettleTempSaveRequestDTO = DeserializeUtil.deserialize(saveRequestParam.getMultiSettleBaseParam(), MultiPoiSelfSettleTempSaveRequestDTO.class);
        List<String> logisticsCodeList = multiPoiSelfSettleTempSaveRequestDTO.getServiceBrandProductParam().stream().map(ServiceBrandProductParamRequestDTO::getServiceBrand).collect(Collectors.toList());
        MultiSettleBaseParamStruct multiSettleBaseParamStruct = JacksonUtil.readValue(saveRequestParam.getMultiSettleBaseParam(), MultiSettleBaseParamStruct.class);
        Integer feeMode = Optional.ofNullable(multiSettleBaseParamStruct).map(MultiSettleBaseParamStruct::getFeeMode).orElse(null);
        GrayDecideParam grayDecideParam = super.buildGrayDecideParam(saveRequestParam.getWmPoiId(), logisticsCodeList, false, feeMode);
        grayDecideParam.setIsAgent(Objects.nonNull(multiPoiSelfSettleTempSaveRequestDTO.getAgentId()) && multiPoiSelfSettleTempSaveRequestDTO.getAgentId() > 0);
        grayDecideParam.setBizOrgCode(multiPoiSelfSettleTempSaveRequestDTO.getBizOrgCode());
        return grayDecideParam;
    }

    private ContractCreateAndUpdateSceneEnum matchScene(HeronContractMultiSettleParam saveRequestParam) {
        OperateSourceEnum opSource = saveRequestParam.getOperator().getOpSource();
        if(opSource.isMultiSelfSettleChannel()){
            return ContractCreateAndUpdateSceneEnum.MULTI_SELF_SETTLE;
        }else if(opSource.isMultiSettleChannel()){
            return ContractCreateAndUpdateSceneEnum.MULTI_SETTLE;
        }else{
            throw new RuntimeException("多店场景opSource不正确");
        }
    }

}