package com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PerfContractCreatePdfStepParam {

    private String batchSignScene;

    private Long batchRelationId;

    private List<HeronContractGatewaySession> sessionList;

}
