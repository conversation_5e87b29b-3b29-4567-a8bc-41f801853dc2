package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.audit.perf;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.SubmitSinglePoiParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfSettleFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PerfDomainFlowRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.audit.CommitAuditStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 单店提审请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class PerfContractCommitAuditReqSender extends PerfDomainFlowRequestSender<CommitAuditStepParam, SubmitSinglePoiParam> {

    @Resource
    private BmPerfSettleFlowThriftServiceAdapter bmPerfSettleFlowThriftServiceAdapter;



    @Override
    protected SubmitSinglePoiParam buildRemoteParam(SendParam<CommitAuditStepParam> param) {
        CommitAuditStepParam commitAuditStepParam = param.getOriginParam();
        SubmitSinglePoiParam singlePoiParam = new SubmitSinglePoiParam();
        singlePoiParam.setWmPoiId(commitAuditStepParam.getWmPoiId());
        singlePoiParam.setSessionId(commitAuditStepParam.getSessionId());
        singlePoiParam.setOperatorParam(OperatorConverter.toPerfOperator(commitAuditStepParam.getOperator()));
        singlePoiParam.setRequestId(param.getRequestLeafId());
        singlePoiParam.setHbAuditMap(commitAuditStepParam.getHbAuditMap());
        return singlePoiParam;
    }

    @Override
    protected BmContractPlatformProcessResp sendToPerf(SubmitSinglePoiParam remoteParam) throws GatewayAdapterException {
        return bmPerfSettleFlowThriftServiceAdapter.submitSinglePoi(remoteParam);
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.COMMIT_AUDIT;
    }


}