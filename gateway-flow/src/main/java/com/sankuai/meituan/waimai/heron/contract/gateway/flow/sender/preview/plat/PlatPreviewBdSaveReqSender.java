package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.preview.plat;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.PlatContractBDSettleSaveParamConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractBDSettleSaveParam;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SavePreviewResponseDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SaveResponseDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SinglePoiSaveRequestDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-16 10:52
 */
@Component
public class PlatPreviewBdSaveReqSender extends PlatDomainRequestSender<HeronContractBDSettleSaveParam, SinglePoiSaveRequestDTO> {

    @Resource
    private PlatContractFlowThriftServiceAdapter platContractFlowThriftServiceAdapter;

    @Resource
    private PlatContractBDSettleSaveParamConverter platContractBDSettleSaveParamConverter;

    @Override
    protected SinglePoiSaveRequestDTO buildRemoteParam(SendParam<HeronContractBDSettleSaveParam> param) {
        HeronContractBDSettleSaveParam bdSettleSaveParam = param.getOriginParam();
        return platContractBDSettleSaveParamConverter.convert(bdSettleSaveParam);
    }

    @Override
    protected SendResult doSend(SinglePoiSaveRequestDTO remoteParam) throws GatewayAdapterException {
        SavePreviewResponseDTO responseDTO = platContractFlowThriftServiceAdapter.previewSaveSinglePoi(remoteParam);
        return convertResult(responseDTO);
    }


    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.PREVIEW_BD_SAVE;
    }

}