package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.sign.econtract;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.sign.econtract.EContractDirectSignReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 直接发起单店电子合同签约
 * @author: chenyihao04
 * @create: 2023-05-17 17:45
 */
@Component
public class EContractDirectSignReqRouter implements RequestRouter<SinglePoiFlowRouteParam> {

    @Resource
    private EContractDirectSignReqSender eContractDirectSignReqSender;

    @Override
    public SubDomainEnum routeTargetDomain() {
        return SubDomainEnum.E_CONTRACT;
    }

    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setPerfSplit(routeParam.getProcessingPerfSplit());
        routeResult.setAreaSplit(routeParam.getProcessingAreaSplit());
        routeResult.setPerfDR(routeParam.getProcessingPerfDR());
        routeResult.setRequestSender(eContractDirectSignReqSender);
        return routeResult;
    }

}