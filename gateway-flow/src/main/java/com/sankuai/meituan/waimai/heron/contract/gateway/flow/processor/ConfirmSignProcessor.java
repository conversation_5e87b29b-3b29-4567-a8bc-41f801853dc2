package com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SessionSceneGroupEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBusinessException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayNoAlarmBusinessException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.idempotent.GatewayIdempotent;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.addition.CustomerSwitchFlowAdditionService;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.processing.ConfirmSignFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.effect.EffectiveCheckFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractConfirmSignParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Objects;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.AcquireFailActionEnum.WAIT;
import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock.WmPoiIdExtractTypeEnum.PARAM_FIELD;

/**
 * @description: 签约确认
 * @author: chenyihao04
 * @create: 2023-04-26 11:46
 */
@Service
@Slf4j
public class ConfirmSignProcessor {

    @Resource
    private ConfirmSignFlowOrchestration confirmSignFlowOrchestration;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private CustomerSwitchFlowAdditionService customerSwitchFlowAdditionService;

    @EntryAndProcessorLock(extractType = PARAM_FIELD, acquireFailAction = WAIT, supportReentry = true)
    @GatewayIdempotent(idempotentKeys = "wmPoiId,confirmId,applyType")
    public void confirmSign(HeronContractConfirmSignParam param) throws GatewayBaseException {
        log.info("签约确认 confirmSign, 参数 param: {}", JacksonUtil.writeAsJsonStr(param));
        HeronContractGatewaySession sessionTemp = heronContractGatewaySessionBasicService.getLastByWmPoiIdAndGroupRT(param.getWmPoiId(), SessionSceneGroupEnum.FLOW, SessionCategoryEnum.convertFromApplyType(param.getApplyType()));
        HeronContractGatewaySession session = null;
        //判断session的confirmId和签约确认confirmId是否一致
        if (sessionTemp != null) {
            if (param.getConfirmId().equals(sessionTemp.getContext().getConfirmId())) {
                if (sessionTemp.getStatus().isProcessing()) {
                    session = sessionTemp;
                } else {
                    log.error("签约确认 confirmSign session已经不在流程中 param: {} session:{}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(sessionTemp));
                    return;
                }
            } else {
                //只打印错误日志，但不作为异常
                if (sessionTemp.getStatus().isProcessing()
                        && (Objects.isNull(sessionTemp.getContext().getSgTransferContractVersionId()) || sessionTemp.getContext().getSgTransferContractVersionId() <= 0)
                        && (Objects.isNull(sessionTemp.getContext().getYyTransferContractVersionId()) || sessionTemp.getContext().getYyTransferContractVersionId() <= 0)) {
                    log.error("签约确认 confirmSign 流程中的session和签约确认confirmId不一致 param: {} session:{}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(sessionTemp));
                }
            }

        }
        OrchestrationExecResult result = GlobalFlowSessionHandler.applyWithSession(session, param, confirmSignFlowOrchestration::flow);
        if (session == null || SessionCategoryEnum.CORE.equals(session.getSessionCategory())) {
            customerSwitchFlowAdditionService.refreshTaskStatusByWmPoiId(param.getWmPoiId());
        }
        log.info("签约确认 confirmSign, 结果 result: {}", JacksonUtil.writeAsJsonStr(result));
        if (result.isFail()) {
            //生效校验异常，抛出不告警异常
            if (result.getExecFailStepClazz() != null && EffectiveCheckFlowStep.class.getSimpleName().equals(result.getExecFailStepClazz().getSimpleName())) {
                throw new GatewayNoAlarmBusinessException(result.getMessage());
            }
            throw new GatewayBusinessException(-1, result.getMessage());
        }

    }
}