package com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.sign;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.dispatcher.BaseFlowProcessingRequestDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.RouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.ConfirmSignStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.sign.area.AreaSignConfirmReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.sign.perf.PerfContractSignConfirmReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.sign.plat.PlatContractSignConfirmReqRouter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @description: 签约确认分发
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
@Slf4j
public class
SignConfirmReqDispatcher extends BaseFlowProcessingRequestDispatcher<ConfirmSignStepParam> {

    @Resource
    private RouteHelper routeHelper;

    @Override
    protected List<Class<? extends RequestRouter>> registerRouterClass() {
        return ImmutableList.of(PlatContractSignConfirmReqRouter.class, PerfContractSignConfirmReqRouter.class, AreaSignConfirmReqRouter.class);
    }

    @Override
    protected RouteParam buildRouteParam(ConfirmSignStepParam originParam) {
        SinglePoiFlowRouteParam routeParam;
        try {
            if (Objects.nonNull(originParam.getSessionId()) && originParam.getSessionId() > 0) {
                routeParam = routeHelper.buildRouteParamWithSessionId(originParam.getWmPoiId(), originParam.getSessionId());
            } else {
                routeParam = routeHelper.buildRouteParamWithNoSession(originParam.getWmPoiId());
            }
            return routeParam;
        } catch (GatewaySystemException e) {
            log.error("获取路由参数异常 参数 param: {}", JacksonUtil.writeAsJsonStr(originParam), e);
            throw new RuntimeException(e);
        }
    }
}