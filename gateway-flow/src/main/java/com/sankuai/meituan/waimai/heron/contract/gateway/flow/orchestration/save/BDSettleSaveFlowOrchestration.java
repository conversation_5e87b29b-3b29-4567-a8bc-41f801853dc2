package com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.save;

import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.audit.CommitAuditStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.AuditRejectStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.BizCheckAfterSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.SaveCheckFailStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.SaveFailStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.LogisticsFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.audit.CommitAuditFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.save.BDSettleSaveCheckFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.save.BDSettleSaveFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.save.BizCheckAfterSaveFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.CommitAuditFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.SaveCheckFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.SaveFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractBDSettleSaveParam;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description: 单店入驻流程编排
 * @author: chenyihao04
 * @create: 2023-04-26 10:43
 */
@Service
public class BDSettleSaveFlowOrchestration implements LogisticsFlowOrchestration<HeronContractBDSettleSaveParam> {


    @Resource
    private BDSettleSaveCheckFlowStep bdSettleSaveCheckFlowStep;

    @Resource
    private SaveCheckFailFlowStep saveCheckFailFlowStep;

    @Resource
    private BDSettleSaveFlowStep bdSettleSaveFlowStep;

    @Resource
    private SaveFailFlowStep saveFailFlowStep;

    @Resource
    private BizCheckAfterSaveFlowStep bizCheckAfterSaveFlowStep;

    @Resource
    private CommitAuditFlowStep commitAuditFlowStep;

    @Resource
    private CommitAuditFailFlowStep commitAuditFailFlowStep;

    public OrchestrationExecResult flow(HeronContractBDSettleSaveParam param) {

        //保存校验
        FlowExecResult checkResult = bdSettleSaveCheckFlowStep.execute(param);
        if (checkResult.isFail()) {
            saveCheckFailFlowStep.execute(buildSaveCheckFailStepParam(param, checkResult));
            return OrchestrationExecResult.fail(BDSettleSaveCheckFlowStep.class, checkResult);
        }
        //保存
        FlowExecResult saveResult = bdSettleSaveFlowStep.execute(param);
        if (saveResult.isFail()) {
            SaveFailStepParam failStepParam = buildSaveFailStepParam(param, saveResult);
            FlowExecResult flowExecResult = saveFailFlowStep.execute(failStepParam);
            return OrchestrationExecResult.fail(BDSettleSaveFlowStep.class, saveResult, flowExecResult);
        }
        if (GlobalFlowSessionHandler.getSgOrYyDrFlow()) {
            return OrchestrationExecResult.success();
        }
        //保存后置校验
        FlowExecResult afterSaveCheckResult = bizCheckAfterSaveFlowStep.execute(buildBizCheckAfterSaveParam(param));
        if (afterSaveCheckResult.isFail()) {
            FlowExecResult flowExecResult = saveFailFlowStep.execute(buildSaveFailStepParam(param, afterSaveCheckResult));
            return OrchestrationExecResult.fail(BizCheckAfterSaveFlowStep.class, afterSaveCheckResult, flowExecResult);
        }
        //提审
        CommitAuditStepParam commitAuditStepParam = buildCommitAuditStepParam(param);
        FlowExecResult commitAuditResult = commitAuditFlowStep.execute(commitAuditStepParam);
        if (commitAuditResult.isFail()) {
            AuditRejectStepParam auditRejectStepParam = buildAuditRejectStepParam(param, commitAuditResult);
            FlowExecResult flowExecResult = commitAuditFailFlowStep.execute(auditRejectStepParam);
            return OrchestrationExecResult.fail(CommitAuditFlowStep.class, commitAuditResult, flowExecResult);
        }
        return OrchestrationExecResult.success();

    }

    private BizCheckAfterSaveParam buildBizCheckAfterSaveParam(HeronContractBDSettleSaveParam param) {
        BizCheckAfterSaveParam bizCheckAfterSaveParam = new BizCheckAfterSaveParam();
        bizCheckAfterSaveParam.setWmPoiId(param.getWmPoiId());
        bizCheckAfterSaveParam.setSessionId(GlobalFlowSessionHandler.getSessionId());
        bizCheckAfterSaveParam.setBaseParam("");//当前只有批量平台场景有需要这个字段，暂时置空
        return bizCheckAfterSaveParam;
    }

    private SaveFailStepParam buildSaveFailStepParam(HeronContractBDSettleSaveParam param, FlowExecResult saveResult) {
        return SaveFailStepParam.builder()
                .causeDomain(saveResult.getFailCauseDomain())
                .wmPoiId(param.getWmPoiId())
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .failReason("保存失败:" + saveResult.getMessage())
                .operator(param.getOperator())
                .currentSgOrYyTransferDrProcess(GlobalFlowSessionHandler.getSgOrYyDrFlow())
                .build();
    }

    private SaveCheckFailStepParam buildSaveCheckFailStepParam(HeronContractBDSettleSaveParam param, FlowExecResult checkResult) {
        return SaveCheckFailStepParam.builder()
                .causeDomain(checkResult.getFailCauseDomain())
                .wmPoiId(param.getWmPoiId())
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .failReason(checkResult.getMessage())
                .operator(param.getOperator())
                .currentSgTransferDrProcess(Objects.nonNull(GlobalFlowSessionHandler.getSgOrYyDrFlow()))
                .build();
    }

    private CommitAuditStepParam buildCommitAuditStepParam(HeronContractBDSettleSaveParam param) {
        return CommitAuditStepParam.builder()
                .operator(param.getOperator())
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .wmPoiId(param.getWmPoiId())
                .build();
    }


    private AuditRejectStepParam buildAuditRejectStepParam(HeronContractBDSettleSaveParam param, FlowExecResult commitResult) {
        return AuditRejectStepParam.builder()
                .causeDomain(commitResult.getFailCauseDomain())
                .wmPoiId(param.getWmPoiId())
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .failReason("提审失败:" + commitResult.getMessage())
                .operator(param.getOperator())
                .build();
    }
}