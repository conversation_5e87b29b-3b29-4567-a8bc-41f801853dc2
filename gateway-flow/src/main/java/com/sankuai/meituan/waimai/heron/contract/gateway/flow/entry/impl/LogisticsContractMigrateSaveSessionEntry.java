package com.sankuai.meituan.waimai.heron.contract.gateway.flow.entry.impl;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.bo.SessionContextBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.leaf.LeafIdGenerator;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.monitor.CatLogMonitor;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.addition.CustomerSwitchFlowAdditionService;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.entry.AbstractLogisticsSaveFlowBaseEntry;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.save.MigrateSaveFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.utils.DeserializeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ApplySignActionEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractMigrateSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.save.ContractMigrateSaveResult;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.PackageSignActionEnum;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.WmPoiLogisticsDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.constants.LogisticsContractMissionTypeEnum;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.mission.save.WmLogisticsContractSaveRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.mission.save.WmLogisticsSaveParamExtDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description: 配送信息保存-迁移场景
 * @author: chenyihao04
 * @create: 2023-04-26 10:03
 */
@Service
public class LogisticsContractMigrateSaveSessionEntry extends AbstractLogisticsSaveFlowBaseEntry<HeronContractMigrateSaveParam, ContractMigrateSaveResult> {

    @Resource
    private MigrateSaveFlowOrchestration migrateSaveFlowOrchestration;

    @Resource
    private CustomerSwitchFlowAdditionService customerSwitchFlowAdditionService;

    @Resource
    private CatLogMonitor catLogMonitor;

    @Override
    protected ContractMigrateSaveResult doSaveFlow(HeronContractMigrateSaveParam saveRequestParam) {
        catLogMonitor.catMonitor("migrate_save", saveRequestParam.getWmPoiId());
        OrchestrationExecResult execResult = migrateSaveFlowOrchestration.flow(saveRequestParam);
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        //刷新客户切换不下线状态
        customerSwitchFlowAdditionService.refreshTaskStatusBySession(session);
        if (execResult.getSuccess()) {
            return ContractMigrateSaveResult.builder().success(true).contractVersionId(session.getContext().getContractVersionId()).sessionId(session.getSessionLeafId()).build();
        } else {
            return ContractMigrateSaveResult.builder().success(false).code(-1).sessionId(session.getSessionLeafId()).message(execResult.getMessage()).build();
        }
    }

    @Override
    protected void fillSession(HeronContractGatewaySession session, HeronContractMigrateSaveParam saveRequestParam) throws GatewayBaseException {
        WmLogisticsContractSaveRequestDTO saveRequestDTO = DeserializeUtil.deserialize(saveRequestParam.getOriginalParam(), WmLogisticsContractSaveRequestDTO.class);
        List<String> logisticsCodeList = saveRequestDTO.getLogisticsList().stream().map(WmPoiLogisticsDTO::getLogisticsCode).collect(Collectors.toList());
        LogisticsFeeModeEnum feeModeEnum = saveRequestDTO.getLogisticsList().stream().map(WmPoiLogisticsDTO::getFeeMode).filter(Objects::nonNull).findFirst().orElse(null);
        Integer feeMode = Optional.ofNullable(feeModeEnum).map(LogisticsFeeModeEnum::getCode).orElse(null);

        SessionContextBo sessionContextBo = SessionContextBo.newFlowSession();
        sessionContextBo.setMigrateMissionType(saveRequestDTO.getMissionRequest().getMissionType().name());
        sessionContextBo.setApplySignAction(convertApplySignAction(Optional.ofNullable(saveRequestDTO.getRequestExt()).map(WmLogisticsSaveParamExtDTO::getPackageSignAction).orElse(null), saveRequestDTO.getMissionRequest().getMissionType()));
        session.setWmPoiId(saveRequestParam.getWmPoiId());
        session.setContext(sessionContextBo);
        session.setOpId(saveRequestParam.getOperator().getOpId());
        session.setOpName(saveRequestParam.getOperator().getOpName());
        session.setSessionScene(ContractCreateAndUpdateSceneEnum.LOGISTICS_CONTRACT_SAVE_MIGRATE);
        session.setBatchRelationId(Optional.ofNullable(saveRequestDTO.getMissionRequest().getBatchMissionId()).map(String::valueOf).orElse(LeafIdGenerator.generateBatchRelationId()));
        super.fillSessionContextGrayInfo(session, super.buildGrayDecideParam(saveRequestParam.getWmPoiId(), logisticsCodeList, true, feeMode));
    }

    private ApplySignActionEnum convertApplySignAction(PackageSignActionEnum packageSignAction, LogisticsContractMissionTypeEnum contractMissionType) {
        if (contractMissionType.equals(LogisticsContractMissionTypeEnum.AUTO_CHANGE_CONTRACT)) {
            return ApplySignActionEnum.AUTO_BATCH;
        } else if (PackageSignActionEnum.LATER.equals(packageSignAction)){
            return ApplySignActionEnum.LATER;
        }
        return ApplySignActionEnum.DIRECT;
    }

}