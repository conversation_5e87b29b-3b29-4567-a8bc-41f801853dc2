package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.plat;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PlatSingleFlowPoiRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.plat.PlatContractBDSettleSaveReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.plat.PlatContractNewChannelSaveReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description: 单店保存路由
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class PlatContractBDSettleSaveReqRouter extends PlatSingleFlowPoiRequestRouter {

    @Resource
    private PlatContractBDSettleSaveReqSender platContractBDSettleSaveReqSender;

    @Resource
    private PlatContractNewChannelSaveReqSender platContractNewChannelSaveReqSender;

    public RouteResult route(SinglePoiFlowRouteParam routeParam) {
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setPerfSplit(routeParam.getProcessingPerfSplit());
        routeResult.setAreaSplit(routeParam.getProcessingAreaSplit());
        routeResult.setPerfDR(routeParam.getProcessingPerfDR());
        routeResult.setRequestSender(matchSender());
        return routeResult;
    }

    private PlatDomainRequestSender matchSender() {
        if (Objects.equals(GlobalFlowSessionHandler.getSessionCategory(), SessionCategoryEnum.ARRIVE_SHOP) || Objects.equals(GlobalFlowSessionHandler.getSessionCategory(), SessionCategoryEnum.VIP_CARD)) {
            return platContractNewChannelSaveReqSender;
        } else {
            return platContractBDSettleSaveReqSender;
        }
    }

}