package com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor;

import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableMap;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.GatewayConstants;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLockHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.alarm.AlarmLevel;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.alarm.GatewayAlarmBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.alarm.GatewayAlarmUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.EffectiveStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.processing.EffectiveInnerFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.batch.HeronContractBatchFlowCompleteParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@Component
@Slf4j
public class BatchFlowCompleteProcessor {

    @Resource
    private EffectiveInnerFlowOrchestration effectiveInnerFlowOrchestration;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private EntryAndProcessorLockHandler entryAndProcessorLockHandler;

    public void batchComplete(HeronContractBatchFlowCompleteParam completeParam) throws GatewayBaseException {
        List<Long> failWmPoiIdList = new ArrayList<>();
        try {
            SessionCategoryEnum sessionCategory = Optional.ofNullable(completeParam.getSessionCategory()).orElse(SessionCategoryEnum.CORE);
            entryAndProcessorLockHandler.batchApplyWithLock(completeParam.getWmPoiIdList(), sessionCategory, null, p -> {
                for (Long wmPoiId : completeParam.getWmPoiIdList()) {
                    HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(wmPoiId, sessionCategory);
                    if (session == null) {
                        log.error("batchComplete session 不存在 wmPoiId: {}", wmPoiId);
                        failWmPoiIdList.add(wmPoiId);
                        continue;
                    }
                    EffectiveStepParam effectiveStepParam = EffectiveStepParam.builder()
                            .operator(completeParam.getOperator())
                            .sessionId(session.getSessionLeafId())
                            .wmPoiId(wmPoiId)
                            .needConfirm(session.getContext().getNeedSign())
                            .sessionCategory(session.getSessionCategory())
                            .build();

                    OrchestrationExecResult execResult = GlobalFlowSessionHandler.applyWithSession(session, effectiveStepParam, effectiveInnerFlowOrchestration::flow);
                    if (Objects.isNull(execResult) || execResult.isFail()) {
                        log.error("batchComplete 失败门店={} execResult: {}", wmPoiId, JacksonUtil.writeAsJsonStr(execResult));
                        failWmPoiIdList.add(wmPoiId);
                    }
                }
                return null;
            });
        } catch (Throwable e) {
            log.error("批量完成任务异常 completeParam error completeParam: {}", JacksonUtil.writeAsJsonStr(completeParam), e);
            Cat.logMetricForCount(GatewayConstants.BATCH_COMPLETE_FAIL, ImmutableMap.of("sessionScene", Optional.ofNullable(completeParam.getScene()).map(Enum::name).orElse("")));
            sendDxAlarm(completeParam);
            throw new GatewaySystemException(-1, "批量完成任务异常");
        }
        if (CollectionUtils.isNotEmpty(failWmPoiIdList)) {
            log.error("批量完成任务 batchComplete 部分门店失败, failWmPoiIdList: {}", JacksonUtil.writeAsJsonStr(failWmPoiIdList));
            Cat.logMetricForCount(GatewayConstants.BATCH_COMPLETE_FAIL, ImmutableMap.of("sessionScene", Optional.ofNullable(completeParam.getScene()).map(Enum::name).orElse("")));
            sendDxAlarm(completeParam);
        }
    }

    private void sendDxAlarm(HeronContractBatchFlowCompleteParam completeParam) {
        GatewayAlarmBo alarmBo = new GatewayAlarmBo();
        alarmBo.setAlarmLevel(AlarmLevel.P1);
        alarmBo.setAlarmTitle("【批量任务完成失败】");
        alarmBo.setAlarmMessage(Optional.ofNullable(completeParam.getScene()).map(Enum::name).orElse("") + "任务完成失败");
        alarmBo.setParam(JacksonUtil.writeAsJsonStr(completeParam));
        alarmBo.setTraceId(Tracer.id());
        GatewayAlarmUtil.dxAlarm(alarmBo);
    }
}
