package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.drMigrate.rollback;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.RollBackPhfContractDataParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfSettleEventThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PerfDomainEventRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/19
 */
@Component
public class PerfPhfMigrateRollbackRequestSender extends PerfDomainEventRequestSender<Long, RollBackPhfContractDataParam> {

    @Resource
    private BmPerfSettleEventThriftServiceAdapter bmPerfSettleEventThriftServiceAdapter;

    @Override
    protected BmContractPlatformProcessResp sendToPerf(RollBackPhfContractDataParam remoteParam) throws GatewayAdapterException {
        return bmPerfSettleEventThriftServiceAdapter.rollBackPhfContractData(remoteParam);
    }

    @Override
    protected RollBackPhfContractDataParam buildRemoteParam(SendParam<Long> param) throws GatewayBaseException {
        Long wmPoiId = param.getOriginParam();
        RollBackPhfContractDataParam remoteParam = new RollBackPhfContractDataParam();
        remoteParam.setWmPoiId(wmPoiId);
        return remoteParam;
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.PHF_MIGRATE_ROLLBACK;
    }
}
