package com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.GatewayPreconditions;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractMigrateSaveParam;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.WmLogisticsServicePackageDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SinglePoiSaveRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.mission.save.WmLogisticsContractSaveRequestDTO;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/6/12
 */
public class PlatContractMigrateSaveParamConverter {

    /**
     *
     * @param sendParam
     * @return
     * @throws GatewayArgumentException
     */
    public static SinglePoiSaveRequestDTO convert(SendParam<HeronContractMigrateSaveParam> sendParam) throws GatewayArgumentException {
        HeronContractMigrateSaveParam migrateSaveParam = sendParam.getOriginParam();
        WmLogisticsContractSaveRequestDTO originSaveRequestDTO =  JacksonUtil.readValue(migrateSaveParam.getOriginalParam(), WmLogisticsContractSaveRequestDTO.class);
        GatewayPreconditions.checkNotNull(originSaveRequestDTO, "原配送信息参数为空");
        boolean areaSplit = sendParam.getRouteResult().getAreaSplit();
        SinglePoiSaveRequestDTO saveRequestDTO = new SinglePoiSaveRequestDTO();
        saveRequestDTO.setAreaSplit(areaSplit);
        saveRequestDTO.setMissionRequest(originSaveRequestDTO.getMissionRequest());
        saveRequestDTO.setPerfSplit(sendParam.getRouteResult().getPerfSplit());
        saveRequestDTO.setRequestExt(originSaveRequestDTO.getRequestExt());
        saveRequestDTO.setSessionId(GlobalFlowSessionHandler.getSessionId());
        saveRequestDTO.setPerfDR(sendParam.getRouteResult().getPerfDR());
        saveRequestDTO.setLogisticsList(originSaveRequestDTO.getLogisticsList());
        List<WmLogisticsServicePackageDTO> servicePackageDTOList = originSaveRequestDTO.getServicePackageList();
        if (areaSplit) {
            //将配送范围置空
            originSaveRequestDTO.getServicePackageList().forEach(servicePackageDTO -> {
                servicePackageDTO.setLogisticsAreaList(null);
            });
        }
        saveRequestDTO.setWmPoiId(originSaveRequestDTO.getWmPoiId());
        saveRequestDTO.setServicePackageList(servicePackageDTOList);
        saveRequestDTO.setSessionCategory(GlobalFlowSessionHandler.getSessionCategory().name());
        return saveRequestDTO;
    }
}
