package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.sign.perf;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PerfSinglePoiFlowRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.sign.perf.PerfContractWaitManualSuccessSignReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 生成待打包签约任务路由
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class PerfContractWaitManualSignReqRouter extends PerfSinglePoiFlowRequestRouter {

    @Resource
    private PerfContractWaitManualSuccessSignReqSender perfContractWaitManualSuccessSignReqSender;

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        return super.buildDefaultPerfResult(routeParam, perfContractWaitManualSuccessSignReqSender);
    }

}