package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.perf;

import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PerfSinglePoiFlowRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.perf.PerfContractBDSettleSaveReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.perf.PerfContractBizCheckAfterSaveReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 保存后校验
 * @author: chenyihao04
 * @create: 2024-05-16 19:10
 */
@Component
public class PerfContractBizCheckAfterSaveReqRouter extends PerfSinglePoiFlowRequestRouter {

    @Resource
    private PerfContractBizCheckAfterSaveReqSender perfContractBizCheckAfterSaveReqSender;

    public RouteResult route(SinglePoiFlowRouteParam routeParam) {
        return super.buildDefaultPerfResult(routeParam, perfContractBizCheckAfterSaveReqSender);
    }

}