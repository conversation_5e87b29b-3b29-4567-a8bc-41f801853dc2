package com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.CheckSavePhfSinglePoiParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.PhfContractBatchSaveExtParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.PhfOriginContractParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.SavePhfSinglePoiParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.ServiceBrandProductParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfBatchSplitSingleSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfMockSaveCheckFlowStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractBatchFlowSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractBatchMockFlowParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractInfoParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractSingleFlowSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.LogisticsBrandProductParamStruct;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.PhfContractBatchSaveExtParamStruct;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.PhfContractSingleSaveExtParamStruct;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 *
 * 履约合同保存参数转换，接口文档：https://km.sankuai.com/collabpage/2588357831
 * <AUTHOR>
 * @date 2024/12/20
 */
public class PerfPhfContractSaveParamConverter {

    /**
     * 履约合同单店保存校验参数转换 businessIdentity 双写阶段41，拆分阶段4
     * @param requestParam
     * @return
     */
    public static CheckSavePhfSinglePoiParam convertCheckParamBySingle(PhfContractSingleFlowSaveParam requestParam) throws GatewayArgumentException {
        PhfContractSingleSaveExtParamStruct extParamStruct = JacksonUtil.readValueNotNull(requestParam.getProcessExtParam(), PhfContractSingleSaveExtParamStruct.class);
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        CheckSavePhfSinglePoiParam param = new CheckSavePhfSinglePoiParam();
        param.setWmPoiId(requestParam.getWmPoiId());
        param.setServiceBrandProductParam(convertServiceBrandProductParamList(extParamStruct.getServiceBrandProductList()));
        param.setSessionId(session.getSessionLeafId());
        param.setFeeMode(extParamStruct.getFeeMode());
        param.setBusinessIdentity(GlobalFlowSessionHandler.getPerfBusinessIdentity());
        param.setOperatorSourceCode(PerfSceneOpSourceConverter.convertOpSource(session.getSessionScene()).getCode());
        param.setOperateMissionType(PlatContractMissionTypeConverter.convertPhfMissionType(session.getSessionScene()).getCode());
        param.setEffectBySchedule(requestParam.getEffectBySchedule());
        param.setScheduledEffectiveTime(requestParam.getScheduledEffectiveTime());
//        param.setBatchSaveExtParam(JacksonUtil.readValue(requestParam.getProcessExtParam(), PhfContractBatchSaveExtParam.class));
        param.setPhfOriginContract(convertPhfContract(requestParam.getPhfContractInfo()));
        param.setOperatorParam(OperatorConverter.toPerfOperator(requestParam.getOperator()));
        return param;
    }

    /**
     * 履约合同单店保存参数转换 businessIdentity 双写阶段41，拆分阶段4
     * @param requestParam
     * @return
     */
    public static SavePhfSinglePoiParam convertSaveParamBySingle(PhfContractSingleFlowSaveParam requestParam, Long requestId) throws GatewayArgumentException {
        PhfContractSingleSaveExtParamStruct extParamStruct = JacksonUtil.readValueNotNull(requestParam.getProcessExtParam(), PhfContractSingleSaveExtParamStruct.class);
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        SavePhfSinglePoiParam param = new SavePhfSinglePoiParam();
        param.setWmPoiId(requestParam.getWmPoiId());
        param.setServiceBrandProductParam(convertServiceBrandProductParamList(extParamStruct.getServiceBrandProductList()));
        param.setSessionId(session.getSessionLeafId());
        param.setFeeMode(extParamStruct.getFeeMode());
        param.setBusinessIdentity(GlobalFlowSessionHandler.getPerfBusinessIdentity());
        param.setOperatorSourceCode(PerfSceneOpSourceConverter.convertOpSource(session.getSessionScene()).getCode());
        param.setOperateMissionType(PlatContractMissionTypeConverter.convertPhfMissionType(session.getSessionScene()).getCode());
        param.setEffectBySchedule(requestParam.getEffectBySchedule());
        param.setScheduledEffectiveTime(requestParam.getScheduledEffectiveTime());
//        param.setBatchSaveExtParam(JacksonUtil.readValue(requestParam.getProcessExtParam(), PhfContractBatchSaveExtParam.class));
        param.setPhfOriginContract(convertPhfContract(requestParam.getPhfContractInfo()));
        param.setOperatorParam(OperatorConverter.toPerfOperator(requestParam.getOperator()));
        param.setRequestId(requestId);
        return param;
    }


    /**
     * 履约合同单店保存校验参数转换 businessIdentity 双写阶段41，拆分阶段4
     * @param requestParam
     * @return
     */
    public static CheckSavePhfSinglePoiParam convertCheckParamByBatch(PhfBatchSplitSingleSaveParam requestParam) throws GatewayArgumentException {
        PhfContractBatchFlowSaveParam originParam = requestParam.getOriginParam();
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        PhfContractBatchSaveExtParamStruct extParamStruct = JacksonUtil.readValueNotNull(originParam.getProcessExtParam(), PhfContractBatchSaveExtParamStruct.class);
        CheckSavePhfSinglePoiParam param = new CheckSavePhfSinglePoiParam();
        param.setWmPoiId(requestParam.getWmPoiId());
        param.setServiceBrandProductParam(convertServiceBrandProductParamList(extParamStruct.getServiceBrandProductList()));
        param.setSessionId(session.getSessionLeafId());
        param.setFeeMode(extParamStruct.getFeeMode());
        param.setBusinessIdentity(GlobalFlowSessionHandler.getPerfBusinessIdentity());
        param.setOperatorSourceCode(PerfSceneOpSourceConverter.convertOpSource(session.getSessionScene()).getCode());
        param.setOperateMissionType(PlatContractMissionTypeConverter.convertPhfMissionType(session.getSessionScene()).getCode());
        param.setEffectBySchedule(originParam.getEffectBySchedule());
        param.setScheduledEffectiveTime(originParam.getScheduledEffectiveTime());
        param.setBatchSaveExtParam(JacksonUtil.readValue(originParam.getProcessExtParam(), PhfContractBatchSaveExtParam.class));
        param.setPhfOriginContract(convertPhfContract(requestParam.getContractInfoParam()));
        param.setOperatorParam(OperatorConverter.toPerfOperator(originParam.getOperator()));
        return param;
    }

    private static PhfOriginContractParam convertPhfContract(PhfContractInfoParam param) {
        PhfOriginContractParam originContractParam = new PhfOriginContractParam();
        originContractParam.setPhfContractId(param.getPhfContractId());
        originContractParam.setPhfContractCode(param.getPhfContractCode());
        originContractParam.setPsContractVersionId(param.getPhfPerfContractId());
        return originContractParam;
    }

    /**
     * 履约合同单店保存参数转换 businessIdentity 双写阶段41，拆分阶段4
     * @param requestParam
     * @return
     */
    public static SavePhfSinglePoiParam convertSaveParamByBatch(PhfBatchSplitSingleSaveParam requestParam, Long requestId) throws GatewayArgumentException {
        PhfContractBatchFlowSaveParam originParam = requestParam.getOriginParam();
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        PhfContractSingleSaveExtParamStruct extParamStruct = JacksonUtil.readValueNotNull(originParam.getProcessExtParam(), PhfContractSingleSaveExtParamStruct.class);
        SavePhfSinglePoiParam param = new SavePhfSinglePoiParam();
        param.setWmPoiId(requestParam.getWmPoiId());
        param.setServiceBrandProductParam(convertServiceBrandProductParamList(extParamStruct.getServiceBrandProductList()));
        param.setSessionId(session.getSessionLeafId());
        param.setFeeMode(extParamStruct.getFeeMode());
        param.setBusinessIdentity(GlobalFlowSessionHandler.getPerfBusinessIdentity());
        param.setOperatorSourceCode(PerfSceneOpSourceConverter.convertOpSource(session.getSessionScene()).getCode());
        param.setOperateMissionType(PlatContractMissionTypeConverter.convertPhfMissionType(session.getSessionScene()).getCode());
        param.setEffectBySchedule(originParam.getEffectBySchedule());
        param.setScheduledEffectiveTime(originParam.getScheduledEffectiveTime());
        param.setBatchSaveExtParam(JacksonUtil.readValue(originParam.getProcessExtParam(), PhfContractBatchSaveExtParam.class));
        param.setPhfOriginContract(convertPhfContract(requestParam.getContractInfoParam()));
        param.setOperatorParam(OperatorConverter.toPerfOperator(originParam.getOperator()));
        param.setRequestId(requestId);
        return param;
    }

    public static List<ServiceBrandProductParam> convertServiceBrandProductParamList(List<LogisticsBrandProductParamStruct> brandProductParamStructList) {
        if (CollectionUtils.isEmpty(brandProductParamStructList)) {
            return Collections.emptyList();
        }
        return JacksonUtil.readValue(JacksonUtil.writeAsJsonStr(brandProductParamStructList), new TypeReference<List<ServiceBrandProductParam>>() {});
    }

    public static CheckSavePhfSinglePoiParam convertCheckParamByMock(PhfMockSaveCheckFlowStepParam requestParam) throws GatewayArgumentException {
        PhfContractBatchMockFlowParam originParam = requestParam.getMockFlowParam();
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        PhfContractBatchSaveExtParamStruct extParamStruct = JacksonUtil.readValueNotNull(originParam.getProcessExtParam(), PhfContractBatchSaveExtParamStruct.class);
        CheckSavePhfSinglePoiParam param = new CheckSavePhfSinglePoiParam();
        param.setWmPoiId(requestParam.getWmPoiId());
        param.setServiceBrandProductParam(convertServiceBrandProductParamList(extParamStruct.getServiceBrandProductList()));
        param.setSessionId(session.getSessionLeafId());
        param.setFeeMode(extParamStruct.getFeeMode());
        param.setBusinessIdentity(GlobalFlowSessionHandler.getPerfBusinessIdentity());
        param.setOperatorSourceCode(PerfSceneOpSourceConverter.convertOpSource(session.getSessionScene()).getCode());
        param.setOperateMissionType(PlatContractMissionTypeConverter.convertPhfMissionType(session.getSessionScene()).getCode());
        param.setEffectBySchedule(originParam.getEffectBySchedule());
        param.setScheduledEffectiveTime(originParam.getScheduledEffectiveTime());
        param.setBatchSaveExtParam(JacksonUtil.readValue(originParam.getProcessExtParam(), PhfContractBatchSaveExtParam.class));
        param.setOperatorParam(OperatorConverter.toPerfOperator(originParam.getOperator()));
        return param;
    }
}
