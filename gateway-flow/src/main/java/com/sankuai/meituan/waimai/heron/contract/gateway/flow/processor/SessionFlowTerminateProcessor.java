package com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBusinessException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.SessionFlowTerminateFlowStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.SessionFlowTerminateFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.terminate.HeronContractFlowTerminateParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.AcquireFailActionEnum.WAIT;
import static com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLock.WmPoiIdExtractTypeEnum.PARAM_FIELD;

/**
 * @description: 流程终止
 * @author: chenyihao04
 * @create: 2023-04-26 11:47
 */
@Service
@Slf4j
public class SessionFlowTerminateProcessor {

    @Resource
    private SessionFlowTerminateFlowStep terminateFlowStep;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    /**
     * 在这个动作里，需要把签约平台的任务取消，子领域的流程取消
     */
    @EntryAndProcessorLock(extractType = PARAM_FIELD, acquireFailAction = WAIT, supportReentry = true)
    public GatewayResult terminate(HeronContractFlowTerminateParam param) throws GatewaySystemException, GatewayBusinessException {
        log.info("terminate 参数 param: {}", JacksonUtil.writeAsJsonStr(param));
        HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getByLeafId(param.getSessionId());
        if (Objects.isNull(session)) {
            log.warn("FlowTerminateProcessor sessionId不存在, param: {}", JacksonUtil.writeAsJsonStr(param));
            return GatewayResult.success();
        }
        if (!Objects.equals(param.getWmPoiId(), session.getWmPoiId())) {
            return GatewayResult.fail(-1, "门店id与sessionId不匹配");
        }
        if (!session.getStatus().isProcessing()) {
            return GatewayResult.success();
        }
        SessionFlowTerminateFlowStepParam terminateFlowStepParam = SessionFlowTerminateFlowStepParam.builder()
                .wmPoiId(param.getWmPoiId())
                .sessionId(param.getSessionId())
                .operator(param.getOperator())
                .cancelSignOperateType(param.getCancelSignOperateType())
                .sessionCategory(session.getSessionCategory())
                .failReason("外部触发终止流程")
                .build();
        FlowExecResult execResult = GlobalFlowSessionHandler.applyWithSession(session, terminateFlowStepParam, terminateFlowStep::execute);
        if (execResult.getSuccess()) {
            return GatewayResult.success();
        } else {
            return GatewayResult.fail(-1, execResult.getMessage());
        }
    }

}