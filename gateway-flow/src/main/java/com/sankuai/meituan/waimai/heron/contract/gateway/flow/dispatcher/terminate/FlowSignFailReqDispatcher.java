package com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.terminate;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.FlowTerminateBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.terminate.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 签约确认失败、取消签约等场景包含了老结构、闪购的门店
 * <AUTHOR>
 * @date 2023/7/6
 */
@Slf4j
@Component
public class FlowSignFailReqDispatcher extends BaseFlowTerminateReqDispatcher {

    @Resource
    private RouteHelper routeHelper;
    @Override
    protected List<Class<? extends RequestRouter>> registerRouterClass() {
        return ImmutableList.of(PlatContractFlowSignFailRouter.class, PerfContractFlowTerminateReqRouter.class, AreaFlowTerminateReqRouter.class, PlatContractFlowSgStructSignFailRouter.class, PlatContractFlowYyOldStructSignFailRouter.class);
    }

    @Override
    protected SinglePoiFlowRouteParam buildRouteParam(FlowTerminateBo param) {
        SinglePoiFlowRouteParam routeParam;
        try {
            if (Objects.nonNull(param.getSessionId()) && param.getSessionId() > 0) {
                routeParam = routeHelper.buildRouteParamWithSessionId(param.getWmPoiId(), param.getSessionId());
            } else if (GlobalFlowSessionHandler.getSession() != null && GlobalFlowSessionHandler.getSession().getWmPoiId().equals(param.getWmPoiId())) {
                routeParam = routeHelper.buildRouteParamWithSessionId(param.getWmPoiId(), GlobalFlowSessionHandler.getSessionId());
            } else {
                routeParam = routeHelper.buildRouteParamWithNoSession(param.getWmPoiId());
            }
            return routeParam;
        } catch (GatewaySystemException e) {
            log.error("获取路由参数异常 参数 param: {}", JacksonUtil.writeAsJsonStr(param), e);
            throw new RuntimeException(e);
        }
    }
}
