package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.areaNew;

import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.enums.WmOpSceneEnum;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaSelfSaveRequest;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmSpAreaFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.AreaDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.AreaIdentityHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.utils.DeserializeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractSelfSettleParam;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 单店自入驻保存请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class AreaNewSelfSettleSaveReqSender extends AreaDomainRequestSender<HeronContractSelfSettleParam, SpAreaSelfSaveRequest> {

    @Resource
    private BmSpAreaFlowThriftServiceAdapter bmSpAreaFlowThriftServiceAdapter;

    @Override
    protected SpAreaSelfSaveRequest buildRemoteParam(SendParam<HeronContractSelfSettleParam> param) {
        HeronContractSelfSettleParam originParam = param.getOriginParam();
        String settleBaseInfo = originParam.getSelfSettleBaseParam();
        SpAreaSelfSaveRequest saveParam = DeserializeUtil.deserialize(settleBaseInfo, SpAreaSelfSaveRequest.class);
        saveParam.setOperator(OperatorConverter.toAreaNewOperator(originParam.getOperator()));
        saveParam.setSessionId(GlobalFlowSessionHandler.getSessionId());
        saveParam.setBmSpAreaLogisticsOpSceneEnum(WmOpSceneEnum.SINGLE_SELF_SETTLE);
        saveParam.setPlatShareInfo(param.getFlowShareInfoMap().get(SubDomainEnum.PLAT));
        return saveParam;
    }


    @Override
    protected void sendToSpArea(SpAreaSelfSaveRequest remoteParam) throws GatewayAdapterException {
        bmSpAreaFlowThriftServiceAdapter.initAndSave(remoteParam, AreaIdentityHelper.convertFromSessionCategory(GlobalFlowSessionHandler.getSessionCategory()));
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.SAVE;
    }

}