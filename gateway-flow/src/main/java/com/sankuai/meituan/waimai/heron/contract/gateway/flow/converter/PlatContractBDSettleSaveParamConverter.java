package com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.LogisticsSalesPolicyThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ApplySignActionEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractBDSettleExtParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractBDSettleJudgeSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractBDSettleSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.BdSettleSaveBaseParamStruct;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.LogisticsBrandProductParamStruct;
import com.sankuai.meituan.waimai.heron.logistics.api.dto.result.LogisticsSaleStrategySettingDTO;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.*;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.WmPoiLogisticsDTO;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.sla.WmPoiSlaPackageDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.constants.LogisticsContractMissionTypeEnum;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.*;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.NewChannelSinglePoiSaveRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SinglePoiSaveRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.mission.LogisticsMissionRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.mission.save.WmLogisticsSaveParamExtDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/12
 */
@Service
@Slf4j
public class PlatContractBDSettleSaveParamConverter {

    @Resource
    private LogisticsSalesPolicyThriftServiceAdapter logisticsSalesPolicyThriftServiceAdapter;

    public SinglePoiSaveRequestDTO convert(HeronContractBDSettleSaveParam settleSaveParam) {

        BdSettleSaveBaseParamStruct baseParam = JacksonUtil.readValue(settleSaveParam.getBdSaveBaseParam(), BdSettleSaveBaseParamStruct.class);
        SinglePoiSaveRequestDTO saveRequestDTO = new SinglePoiSaveRequestDTO();
        LogisticsMissionRequestDTO missionRequestDTO = buildMissionRequestDTO(settleSaveParam.getWmPoiId(), settleSaveParam.getOperator());
        WmLogisticsSaveParamExtDTO saveParamExtDTO = buildExtParam(settleSaveParam.getExtParam());
        List<WmPoiLogisticsDTO> logisticsList = buildLogisticsList(settleSaveParam.getWmPoiId(), baseParam.getFeeMode(), baseParam.getServiceBrandProductList());
        List<WmLogisticsServicePackageDTO> servicePackageList = buildServicePackageList(settleSaveParam.getWmPoiId(), baseParam);
        saveRequestDTO.setRequestExt(saveParamExtDTO);
        saveRequestDTO.setSessionId(GlobalFlowSessionHandler.getSessionId());
        saveRequestDTO.setLogisticsList(logisticsList);
        saveRequestDTO.setServicePackageList(servicePackageList);
        saveRequestDTO.setPerfSplit(GlobalFlowSessionHandler.getPerfSplit());
        saveRequestDTO.setAreaSplit(GlobalFlowSessionHandler.getAreaSplit());
        saveRequestDTO.setPerfDR(GlobalFlowSessionHandler.getPerfDR());
        saveRequestDTO.setMissionRequest(missionRequestDTO);
        saveRequestDTO.setWmPoiId(settleSaveParam.getWmPoiId());
        saveRequestDTO.setSessionCategory(GlobalFlowSessionHandler.getSessionCategory().name());
        saveRequestDTO.setSgRefreshFee(baseParam.getSgRefreshFee());
        saveRequestDTO.setRefreshFee(baseParam.getRefreshFee());
        saveRequestDTO.setRefreshFeeMap(baseParam.getRefreshFeeMap());
        return saveRequestDTO;
    }

    private List<WmPoiLogisticsDTO> buildLogisticsList(Long wmPoiId, Integer feeMode, List<LogisticsBrandProductParamStruct> brandProductParamStructList) {
        return brandProductParamStructList.stream()
                .map(brandAndProduct -> {
                    WmPoiLogisticsDTO logisticsDTO = new WmPoiLogisticsDTO();
                    logisticsDTO.setLogisticsId(Long.parseLong(brandAndProduct.getServiceBrand()));
                    logisticsDTO.setLogisticsCode(brandAndProduct.getServiceBrand());
                    logisticsDTO.setWmPoiId(wmPoiId);
                    logisticsDTO.setFeeMode(LogisticsFeeModeEnum.codeOf(feeMode));
                    logisticsDTO.setLogisticType(LogisticsTypeEnum.MEITUAN);
                    return logisticsDTO;
                })
                .collect(Collectors.toList());
    }

    private List<WmLogisticsServicePackageDTO> buildServicePackageList(Long wmPoiId, BdSettleSaveBaseParamStruct baseParam) {
        List<WmLogisticsContractItemDTO> platContractItemList = baseParam.getPlatContractItemList();
        List<LogisticsBrandProductParamStruct> brandProductParamStructList = baseParam.getServiceBrandProductList();
        Map<String, List<WmLogisticsContractItemDTO>> itemListMap = platContractItemList.stream()
                .filter(itemDTO -> StringUtils.isNotEmpty(itemDTO.getLogisticsCode()))
                .collect(Collectors.groupingBy(WmLogisticsContractItemDTO::getLogisticsCode));

        List<WmLogisticsServicePackageDTO> servicePackageDTOList = itemListMap.entrySet().stream()
                .map(entry -> {
                    String logisticsCode = entry.getKey();
                    List<WmLogisticsContractItemDTO> itemList = entry.getValue();
                    Integer productId = brandProductParamStructList.stream()
                            .filter(brandProduct -> brandProduct.getServiceBrand().equals(logisticsCode))
                            .findFirst()
                            .flatMap(brandProduct -> Optional.ofNullable(brandProduct.getServiceProductList()).orElse(new ArrayList<>()).stream().findFirst())
                            .orElse(null);
                    WmLogisticsServicePackageDTO servicePackageDTO = new WmLogisticsServicePackageDTO();
                    servicePackageDTO.setLogisticsCode(logisticsCode);
                    WmLogisticsContractItemPackageDTO itemPackageDTO = new WmLogisticsContractItemPackageDTO();
                    itemPackageDTO.setItemList(itemList);
                    itemPackageDTO.setLogisticsCode(logisticsCode);
                    servicePackageDTO.setContractItemPackage(itemPackageDTO);
                    QkPoiLogisticsProductDTO qkPoiLogisticsProductDTO = null;
                    if (productId != null) {
                        qkPoiLogisticsProductDTO = new QkPoiLogisticsProductDTO();
                        qkPoiLogisticsProductDTO.setLogisticsCode(logisticsCode);
                        qkPoiLogisticsProductDTO.setProductId(new Long(productId));
                        qkPoiLogisticsProductDTO.setWmPoiId(wmPoiId);
                    }
                    servicePackageDTO.setLogisticsProduct(qkPoiLogisticsProductDTO);
                    if (CollectionUtils.isNotEmpty(baseParam.getSlaInfoList())) {
                        Optional<WmPoiSlaPackageDTO> sla = baseParam.getSlaInfoList().stream().filter(o -> Objects.equals(o.getLogisticsCode(), logisticsCode)).findFirst();
                        servicePackageDTO.setSlaPackage(sla.orElse(null));
                    }
                    if (CollectionUtils.isNotEmpty(baseParam.getStationList()) &&
                            (Objects.equals(logisticsCode, LogisticsCodeEnum.SCHOOL_AGGR.getCode())) || Objects.equals(GlobalFlowSessionHandler.getSessionCategory(), SessionCategoryEnum.DRONE)) {
                        servicePackageDTO.setStationList(baseParam.getStationList());
                    }
                    return servicePackageDTO;
                })
                .collect(Collectors.toList());

        List<WmLogisticsContractItemDTO> noLogisticsItemList = platContractItemList.stream()
                .filter(itemDTO -> StringUtils.isEmpty(itemDTO.getLogisticsCode()))
                .collect(Collectors.toList());
        //将没有配送方式的item放到主配中
        if (CollectionUtils.isNotEmpty(servicePackageDTOList) && CollectionUtils.isNotEmpty(noLogisticsItemList)) {
            servicePackageDTOList.stream()
                    .filter(servicePackageDTO -> {
                        try {
                            LogisticsSaleStrategySettingDTO settingDTO = logisticsSalesPolicyThriftServiceAdapter.getLogisticsSaleStrategySetting(servicePackageDTO.getLogisticsCode());
                            return settingDTO != null && BooleanUtils.isTrue(settingDTO.getSingleSaleable());
                        } catch (GatewayAdapterException e) {
                            log.warn("getLogisticsSaleStrategySetting error logisticsCode: {}", servicePackageDTO.getLogisticsCode(), e);
                            return true;
                        }
                    })
                    .forEach(servicePackageDTO -> {
                        if (servicePackageDTO.getContractItemPackage() != null && servicePackageDTO.getContractItemPackage().getItemList() != null) {
                            servicePackageDTO.getContractItemPackage().getItemList().addAll(noLogisticsItemList);
                        } else {
                            WmLogisticsContractItemPackageDTO itemPackageDTO = new WmLogisticsContractItemPackageDTO();
                            itemPackageDTO.setItemList(noLogisticsItemList);
                            itemPackageDTO.setLogisticsCode(servicePackageDTO.getLogisticsCode());
                            servicePackageDTO.setContractItemPackage(itemPackageDTO);
                        }
                    });
        }
        return servicePackageDTOList;

    }


    public static LogisticsMissionRequestDTO buildMissionRequestDTO(Long wmPoiId, HeronContractOperator operator) {
        LogisticsMissionRequestDTO missionRequestDTO = new LogisticsMissionRequestDTO();
        missionRequestDTO.setMissionType(LogisticsContractMissionTypeEnum.SINGLE_LOGISTICS_SAVE);
        missionRequestDTO.setWmPoiId(wmPoiId);
        LogisticsOperatorDTO operatorDTO = OperatorConverter.toPlatOperator(operator);
        missionRequestDTO.setOpId(operatorDTO.getOpId());
        missionRequestDTO.setOpName(operatorDTO.getOpName());
        missionRequestDTO.setOpSource(operatorDTO.getOpSource());
        return missionRequestDTO;
    }

    private WmLogisticsSaveParamExtDTO buildExtParam(HeronContractBDSettleExtParam extParam) {
        if (extParam == null) {
            WmLogisticsSaveParamExtDTO saveParamExtDTO = new WmLogisticsSaveParamExtDTO();
            saveParamExtDTO.setLogisticsChangeReason("");
            PackageSignActionEnum packageSignActionEnum = PackageSignActionEnum.DIRECT;
            saveParamExtDTO.setPackageSignAction(packageSignActionEnum);
            return saveParamExtDTO;
        }
        WmLogisticsSaveParamExtDTO saveParamExtDTO = new WmLogisticsSaveParamExtDTO();
        saveParamExtDTO.setLogisticsChangeReason(extParam.getLogisticsChangeReason());
        saveParamExtDTO.setModifyLogisticsScreenshot(extParam.getModifyLogisticsScreenshot());
        PackageSignActionEnum packageSignActionEnum = extParam.getApplySignAction() == ApplySignActionEnum.DIRECT ? PackageSignActionEnum.DIRECT : PackageSignActionEnum.LATER;
        saveParamExtDTO.setPackageSignAction(packageSignActionEnum);
        saveParamExtDTO.setAreaModifyReason(extParam.getAreaModifyReason());
        saveParamExtDTO.setAreaModifyRemark(extParam.getAreaModifyRemark());
        saveParamExtDTO.setModifySpAreaScreenshot(extParam.getModifySpAreaScreenshot());
        return saveParamExtDTO;
    }

    public SinglePoiSaveRequestDTO convertNeedSignParam(HeronContractBDSettleJudgeSignParam originParam, RouteResult routeResult) {
        BdSettleSaveBaseParamStruct baseParam = JacksonUtil.readValue(originParam.getBdSaveBaseParam(), BdSettleSaveBaseParamStruct.class);

        LogisticsMissionRequestDTO missionRequestDTO = buildMissionRequestDTO(originParam.getWmPoiId(), originParam.getOperator());
        List<WmPoiLogisticsDTO> logisticsList = buildLogisticsList(originParam.getWmPoiId(), baseParam.getFeeMode(), baseParam.getServiceBrandProductList());
        List<WmLogisticsServicePackageDTO> servicePackageList = buildServicePackageList(originParam.getWmPoiId(), baseParam);
        SinglePoiSaveRequestDTO saveRequestDTO = new SinglePoiSaveRequestDTO();
        saveRequestDTO.setLogisticsList(logisticsList);
        saveRequestDTO.setServicePackageList(servicePackageList);
        saveRequestDTO.setPerfSplit(routeResult.getPerfSplit());
        saveRequestDTO.setAreaSplit(routeResult.getAreaSplit());
        saveRequestDTO.setPerfDR(routeResult.getPerfDR());
        saveRequestDTO.setAreaDR(GlobalFlowSessionHandler.getAreaDR());
        saveRequestDTO.setMissionRequest(missionRequestDTO);
        saveRequestDTO.setWmPoiId(originParam.getWmPoiId());
        saveRequestDTO.setSessionCategory(GlobalFlowSessionHandler.getSessionCategory().name());
        saveRequestDTO.setSgRefreshFee(baseParam.getSgRefreshFee());
        saveRequestDTO.setRefreshFee(baseParam.getRefreshFee());
        saveRequestDTO.setRefreshFeeMap(baseParam.getRefreshFeeMap());
        return saveRequestDTO;
    }

    public NewChannelSinglePoiSaveRequestDTO convertNewChannelNeedSign(HeronContractBDSettleJudgeSignParam bdSettleSaveParam) {
        BdSettleSaveBaseParamStruct baseParam = JacksonUtil.readValue(bdSettleSaveParam.getBdSaveBaseParam(), BdSettleSaveBaseParamStruct.class);
        NewChannelSinglePoiSaveRequestDTO saveRequestDTO = new NewChannelSinglePoiSaveRequestDTO();
        saveRequestDTO.setWmPoiId(bdSettleSaveParam.getWmPoiId());
        saveRequestDTO.setMissionRequest(buildMissionRequestDTO(bdSettleSaveParam.getWmPoiId(), bdSettleSaveParam.getOperator()));
        saveRequestDTO.setSessionId(GlobalFlowSessionHandler.getSession().getSessionLeafId());
        saveRequestDTO.setSessionCategory(GlobalFlowSessionHandler.getSessionCategory().name());
        saveRequestDTO.setChannelBizType(GlobalFlowSessionHandler.getSessionCategory().convertToChannelBizType());
        saveRequestDTO.setContractItemList(Optional.ofNullable(baseParam).map(BdSettleSaveBaseParamStruct::getPlatContractItemList).orElse(new ArrayList<>()));
        return saveRequestDTO;
    }

    public NewChannelSinglePoiSaveRequestDTO convertNewChannelSave(HeronContractBDSettleSaveParam bdSettleSaveParam) {
        BdSettleSaveBaseParamStruct baseParam = JacksonUtil.readValue(bdSettleSaveParam.getBdSaveBaseParam(), BdSettleSaveBaseParamStruct.class);
        NewChannelSinglePoiSaveRequestDTO saveRequestDTO = new NewChannelSinglePoiSaveRequestDTO();
        saveRequestDTO.setWmPoiId(bdSettleSaveParam.getWmPoiId());
        saveRequestDTO.setMissionRequest(buildMissionRequestDTO(bdSettleSaveParam.getWmPoiId(), bdSettleSaveParam.getOperator()));
        saveRequestDTO.setSaveParamExtDTO(buildExtParam(bdSettleSaveParam.getExtParam()));
        saveRequestDTO.setSessionId(GlobalFlowSessionHandler.getSession().getSessionLeafId());
        saveRequestDTO.setSessionCategory(GlobalFlowSessionHandler.getSessionCategory().name());
        saveRequestDTO.setChannelBizType(GlobalFlowSessionHandler.getSessionCategory().convertToChannelBizType());
        saveRequestDTO.setContractItemList(Optional.ofNullable(baseParam).map(BdSettleSaveBaseParamStruct::getPlatContractItemList).orElse(new ArrayList<>()));
        return saveRequestDTO;
    }
}


