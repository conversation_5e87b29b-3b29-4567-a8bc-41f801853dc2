package com.sankuai.meituan.waimai.heron.contract.gateway.flow.entry.impl.phf;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PhfThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.WmPoiQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.config.MccConfig;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBaseException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.GatewayNumberUtils;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.GatewayPreconditions;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.alarm.GatewayAlarmUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.gray.PhfMigrateSaveGrayDecider;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.helper.PhfSessionBuildHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfMigrateToDrParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfMockSaveCheckFlowStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.phf.PhfMigrateRollbackProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor.phf.PhfMigrateToDrDataProcessor;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.save.phf.PhfMockSaveCheckFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractMigrateStageEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.PhfContractOriginErrorPoiParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.event.PhfContractDrMigrateCleanParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractBatchMockFlowParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.phf.PhfContractBatchMockResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.PhfContractBatchSaveExtParamStruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Component
@Slf4j
public class PhfContractBatchMockSaveEntry {

    @Resource
    private PhfMockSaveCheckFlowStep phfMockSaveCheckFlowStep;

    @Resource
    private WmPoiQueryThriftServiceAdapter wmPoiQueryThriftServiceAdapter;

    @Resource
    private PhfMigrateSaveGrayDecider phfMigrateSaveGrayDecider;

    @Resource
    private PhfSessionBuildHelper phfSessionBuildHelper;

    @Resource
    private PhfMigrateToDrDataProcessor phfMigrateToDrDataProcessor;

    @Resource
    private PhfThriftServiceAdapter phfThriftServiceAdapter;

    private PhfMigrateRollbackProcessor phfMigrateRollbackProcessor;

    private final ExecutorService batchMockSaveExecutorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors(), 20L,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1000), new ThreadPoolExecutor.DiscardPolicy());

    private final ExecutorService batchMigrateExecutorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors() * 2, 20L,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1000), new ThreadPoolExecutor.DiscardPolicy());


    public PhfContractBatchMockResult batchMockSave(PhfContractBatchMockFlowParam param) throws GatewayBaseException, InterruptedException {
        this.checkMockParam(param);
//        this.checkAllPoiIsSameCustomer(param.getWmPoiIdList());
        ContractMigrateStageEnum migrateStageEnum = phfMigrateSaveGrayDecider.judgeSaveMigrateStage(param.getWmPoiIdList(), param.getContractType());
        List<Long> checkSuccessWmPoiIdList = Lists.newArrayList();
        List<PhfContractOriginErrorPoiParam> errorInfoList = Lists.newArrayList();
        CountDownLatch countDownLatch = new CountDownLatch(param.getWmPoiIdList().size());
        boolean migrateSuccess = this.batchMigrate(migrateStageEnum, param.getWmPoiIdList());
        if (!migrateSuccess) {
            return PhfContractBatchMockResult.builder()
                    .allSuccess(false)
                    .errorMessage("模拟签约前清洗数据出错")
                    .build();
        }
        for (Long wmPoiId : param.getWmPoiIdList()) {
            batchMockSaveExecutorService.submit(() -> {
                try {
                    HeronContractGatewaySession session = this.buildMockSession(wmPoiId, migrateStageEnum, param);
                    PhfMockSaveCheckFlowStepParam stepParam = PhfMockSaveCheckFlowStepParam.builder()
                            .wmPoiId(wmPoiId)
                            .mockFlowParam(param).build();
                    FlowExecResult flowExecResult = GlobalFlowSessionHandler.applyWithSession(session, stepParam, phfMockSaveCheckFlowStep::execute);
                    if (flowExecResult.getSuccess()) {
                        checkSuccessWmPoiIdList.add(wmPoiId);
                    } else {
                        errorInfoList.add(PhfContractOriginErrorPoiParam.builder().wmPoiId(wmPoiId).errorMessage(flowExecResult.getMessage()).build());
                    }
                } catch (Exception e) {
                    log.error("拼好饭模拟保存失败 wmPoiId={}", wmPoiId, e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        PhfContractBatchMockResult result = new PhfContractBatchMockResult();
        result.setAllSuccess(CollectionUtils.isEmpty(errorInfoList));
        result.setCheckSuccessWmPoiIdList(checkSuccessWmPoiIdList);
        result.setErrorInfoList(errorInfoList);
        return result;
    }

    private void checkMockParam(PhfContractBatchMockFlowParam param) throws GatewayArgumentException {
        GatewayPreconditions.checkNotNull(param, "单店保存参数不能为空");
        GatewayPreconditions.checkArgument(CollectionUtils.isNotEmpty(param.getWmPoiIdList()), "批量保存参数门店id列表不能为空");
        GatewayPreconditions.checkNotBlank(param.getContractType(), "合同类型不能为空");
        GatewayPreconditions.checkNotNull(param.getEffectBySchedule(), "是否按预约日期生效不能为空");
        GatewayPreconditions.checkNotNull(param.getScheduledEffectiveTime(), "生效时间不能为空");
        GatewayPreconditions.checkNotBlank(param.getProcessExtParam(), "流程扩展参数不能为空");
        GatewayPreconditions.checkArgument(JacksonUtil.readValue(param.getProcessExtParam(), PhfContractBatchSaveExtParamStruct.class) != null, "流程扩展参数反序列化为对象失败");
        GatewayPreconditions.checkNotNull(param.getOperator(), "操作人不能为空");
        GatewayPreconditions.checkArgument(param.getOperator().getOpId() != null, "操作人ID不能为空");
        GatewayPreconditions.checkNotNull(param.getOperator().getOpSource(), "操作人来源不能为空");
    }

//    private void checkAllPoiIsSameCustomer(List<Long> wmPoiIdList) throws GatewayBaseException {
//        try {
//            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftServiceAdapter.batchGetWmPoiAggre(wmPoiIdList, ImmutableSet.of(WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_CUSTOMER_ID));
//            if (CollectionUtils.isEmpty(wmPoiAggreList)) {
//                throw new GatewayArgumentException("门店信息查询为空");
//            }
//            boolean allSameCustomer = wmPoiAggreList.stream().map(WmPoiAggre::getCustomer_id).distinct().count() == 1;
//            if (!allSameCustomer) {
//                throw new GatewayArgumentException("门店列表不属于同一个客户");
//            }
//        } catch (GatewayAdapterException e) {
//            log.error("查询门店聚合信息失败 wmPoiIdList={}", JacksonUtil.writeAsJsonStr(wmPoiIdList), e);
//            throw new GatewaySystemException(-1, "查询门店聚合信息失败");
//        }
//
//    }

    private HeronContractGatewaySession buildMockSession(Long wmPoiId, ContractMigrateStageEnum migrateStageEnum, PhfContractBatchMockFlowParam param) throws GatewayAdapterException, GatewayArgumentException {
        PhfContractBatchSaveExtParamStruct extParamStruct = JacksonUtil.readValueNotNull(param.getProcessExtParam(), PhfContractBatchSaveExtParamStruct.class);
        HeronContractGatewaySession session = phfSessionBuildHelper.buildBatchSession(wmPoiId, migrateStageEnum, extParamStruct, ContractCreateAndUpdateSceneEnum.PHF_BATCH_MOCK_PROCESS);
        session.getContext().setPhfContractType(param.getContractType());
        session.setOpId(param.getOperator().getOpId());
        session.setOpName(param.getOperator().getOpName());
        return session;
    }

    private boolean batchMigrate(ContractMigrateStageEnum migrateStageEnum, List<Long> wmPoiIdList) {
        if (!ContractMigrateStageEnum.BOTH_RUN.equals(migrateStageEnum)) {
            return true;
        }
        if (MccConfig.getPhfMigrateBothRunFinish()) {
            return true;
        }
        AtomicBoolean migrateSuccess = new AtomicBoolean(true);
        try {
            Map<Long, ContractMigrateStageEnum> poiAndMigrateStageMap = phfMigrateSaveGrayDecider.batchJudgeCurrentMigrateStage(wmPoiIdList);
            List<Long> noMigratePoiList = wmPoiIdList.stream()
                    .filter(wmPoiId -> ContractMigrateStageEnum.NO_MIGRATE.equals(poiAndMigrateStageMap.get(wmPoiId)) || (poiAndMigrateStageMap.get(wmPoiId) == null))
                    .collect(Collectors.toList());
            List<List<Long>> partitionPoiList = Lists.partition(noMigratePoiList, 10);
            CountDownLatch countDownLatch = new CountDownLatch(partitionPoiList.size());

            for (List<Long> subPoiIdList : partitionPoiList) {
                batchMigrateExecutorService.submit(() -> {
                    log.info("拼好饭模拟签约 batchMigrate 未迁移的门店为 noMigratePoiList={}", noMigratePoiList);
                    try {
                        if (!migrateSuccess.get()) {
                            log.info("拼好饭批量模拟签约 batchMigrate 已有失败门店，提前结束");
                            return;
                        }
                        Map<Long, List<PhfContractDrMigrateCleanParam>> poiMigrateParamMap = phfThriftServiceAdapter.queryPurgeData(subPoiIdList);
                        if (MapUtils.isEmpty(poiMigrateParamMap)) {
                            return;
                        }
                        for (Map.Entry<Long, List<PhfContractDrMigrateCleanParam>> entry : poiMigrateParamMap.entrySet()) {
                            Long wmPoiId = entry.getKey();
                            List<PhfContractDrMigrateCleanParam> migrateCleanParamList = entry.getValue();
                            if (CollectionUtils.isEmpty(migrateCleanParamList)) {
                                continue;
                            }
                            phfMigrateToDrDataProcessor.process(PhfMigrateToDrParam.builder().poiContractParamList(migrateCleanParamList).wmPoiId(wmPoiId).build());
                            List<Long> migrateContractIdList = migrateCleanParamList.stream()
                                    .map(PhfContractDrMigrateCleanParam::getPhfContractId)
                                    .filter(GatewayNumberUtils::isPositiveNumber)
                                    .collect(Collectors.toList());
                            phfThriftServiceAdapter.checkContract(wmPoiId, migrateContractIdList);
                        }
                    } catch (Exception e) {
                        log.warn("拼好饭模拟签约 batchMigrate 迁移拼好饭门店双写失败 subPoiIdList={}", subPoiIdList, e);
                        GatewayAlarmUtil.alarmMessage("批量模拟签约清洗门店双写失败，门店:" + StringUtils.substring(subPoiIdList.toString(), 0, 100) + "，异常信息:" + e.getMessage());
                        migrateSuccess.set(false);
                    } finally {
                        countDownLatch.countDown();
                    }

                });
            }
            countDownLatch.await();

        } catch (Exception e) {
            log.info("拼好饭模拟签约 双写清洗门店存在异常门店 wmPoiIdList={}", wmPoiIdList, e);
            migrateSuccess.set(false);
        }
        if (!migrateSuccess.get()) {
            batchRollback(wmPoiIdList, "模拟签约清洗门店存在异常门店");
            return false;
        }
        return true;
    }

    private void batchRollback(List<Long> wmPoiIdList, String remark) {
        for (Long wmPoiId : wmPoiIdList) {
            try {
                phfMigrateRollbackProcessor.migrateRollback(wmPoiId);
            } catch (Exception e) {
                log.warn("batchRollback {} 回滚失败 wmPoiId={}", remark, wmPoiId, e);
            }
        }
    }
}
