package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.preview.perf;

import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.SaveCheckSinglePoiAutoFillParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.PreviewSaveAutoFillSinglePoiData;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmPerfSettleFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PerfDomainFlowRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.PerfBusinessIdentityHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.PerfSceneOpSourceConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.utils.DeserializeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ApplySignActionEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractUpdateAndSceneParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractUpdateSceneExtParam;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SavePreviewResponseDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-16 10:53
 */
@Component
public class PerfPreviewUpdateReqSender extends PerfDomainFlowRequestSender<HeronContractUpdateAndSceneParam, SaveCheckSinglePoiAutoFillParam> {

    @Resource
    private BmPerfSettleFlowThriftServiceAdapter bmPerfSettleFlowThriftServiceAdapter;


    @Override
    protected SaveCheckSinglePoiAutoFillParam buildRemoteParam(SendParam<HeronContractUpdateAndSceneParam> param) {
        HeronContractUpdateAndSceneParam sceneParam = param.getOriginParam();
        SaveCheckSinglePoiAutoFillParam autoFillParam = DeserializeUtil.deserialize(sceneParam.getUpdateBaseParam(), SaveCheckSinglePoiAutoFillParam.class);
        if (autoFillParam == null) {
            autoFillParam = new SaveCheckSinglePoiAutoFillParam();
        }
        autoFillParam.setOperatorParam(OperatorConverter.toPerfOperator(sceneParam.getOperator()));
        autoFillParam.setSessionId(GlobalFlowSessionHandler.getSessionId());
        autoFillParam.setRequestId(param.getRequestLeafId());
        autoFillParam.setWmPoiId(sceneParam.getWmPoiId());
        autoFillParam.setNeedPackageSign(ApplySignActionEnum.LATER.equals(GlobalFlowSessionHandler.getSession().getContext().getApplySignAction()));
        autoFillParam.setOperatorSourceCode(PerfSceneOpSourceConverter.convertOpSource(sceneParam.getUpdateScene()).getCode());
        autoFillParam.setOperateMissionType(sceneParam.getUpdateSceneExtParam().getPlatMissionType());
        autoFillParam.setWmCustomerId(Optional.ofNullable(sceneParam.getUpdateSceneExtParam()).map(HeronContractUpdateSceneExtParam::getTargetCustomerId).orElse(null));
        String share = Optional.ofNullable(JacksonUtil.readValue(param.getFlowShareInfoMap().get(SubDomainEnum.PLAT), SavePreviewResponseDTO.class)).map(SavePreviewResponseDTO::getShareInfo).orElse("");
        autoFillParam.setPlatShareInfo(share);
        autoFillParam.setBusinessIdentity(GlobalFlowSessionHandler.getPerfBusinessIdentity());
        return autoFillParam;
    }

    @Override
    protected BmContractPlatformProcessResp<PreviewSaveAutoFillSinglePoiData> sendToPerf(SaveCheckSinglePoiAutoFillParam remoteParam) throws GatewayAdapterException {
        return bmPerfSettleFlowThriftServiceAdapter.previewUpdate(remoteParam);
    }

    @Override
    protected SendResult doSend(SaveCheckSinglePoiAutoFillParam remoteParam) throws GatewayAdapterException {
        BmContractPlatformProcessResp<PreviewSaveAutoFillSinglePoiData> responseDTO = sendToPerf(remoteParam);
        return SendResult.success(JacksonUtil.writeAsJsonStr(responseDTO.getData()), PreviewSaveAutoFillSinglePoiData.class);
    }


    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.PREVIEW_UPDATE;
    }

}