package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.area.phf;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.AreaSinglePoiFlowRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.area.phf.AreaPhfBatchSplitSingleSaveCheckReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/20
 */
@Component
public class AreaPhfBatchSplitSingleSaveCheckReqRouter extends AreaSinglePoiFlowRequestRouter {

    @Resource
    private AreaPhfBatchSplitSingleSaveCheckReqSender areaPhfBatchSplitSingleSaveCheckReqSender;

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        return doBuildDefaultAreaResult(routeParam, areaPhfBatchSplitSingleSaveCheckReqSender);
    }
}
