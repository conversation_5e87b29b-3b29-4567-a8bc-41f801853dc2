package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.plat;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.utils.DeserializeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractMultiSettleParam;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.MultiPoiSelfSettleTempSaveRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SaveResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 多店入驻保存请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
@Slf4j
public class PlatContractMultiSettleSaveReqSender extends PlatDomainRequestSender<HeronContractMultiSettleParam, MultiPoiSelfSettleTempSaveRequestDTO> {

    @Resource
    private PlatContractFlowThriftServiceAdapter platContractFlowThriftServiceAdapter;


    @Override
    protected MultiPoiSelfSettleTempSaveRequestDTO buildRemoteParam(SendParam<HeronContractMultiSettleParam> sendParam) {
        HeronContractMultiSettleParam originParam = sendParam.getOriginParam();
        String settleBaseInfo = originParam.getMultiSettleBaseParam();
        MultiPoiSelfSettleTempSaveRequestDTO multiPoiSelfSettleTempSaveRequestDTO = DeserializeUtil.deserialize(settleBaseInfo, MultiPoiSelfSettleTempSaveRequestDTO.class);
        multiPoiSelfSettleTempSaveRequestDTO.setSessionId(GlobalFlowSessionHandler.getSessionId());
        multiPoiSelfSettleTempSaveRequestDTO.setPerfSplit(sendParam.getRouteResult().getPerfSplit());
        multiPoiSelfSettleTempSaveRequestDTO.setAreaSplit(sendParam.getRouteResult().getAreaSplit());
        multiPoiSelfSettleTempSaveRequestDTO.setPerfDR(sendParam.getRouteResult().getPerfDR());
        multiPoiSelfSettleTempSaveRequestDTO.setOperator(OperatorConverter.toPlatOperator(originParam.getOperator()));
        return multiPoiSelfSettleTempSaveRequestDTO;
    }

    @Override
    protected SendResult doSend(MultiPoiSelfSettleTempSaveRequestDTO remoteParam) throws GatewayAdapterException {

        SaveResponseDTO responseDTO = platContractFlowThriftServiceAdapter.tempSaveMultiPoiSelfSettle(remoteParam);
        recordContractVersionId(responseDTO);
        return convertResult(responseDTO);

    }



    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.SAVE;
    }

}