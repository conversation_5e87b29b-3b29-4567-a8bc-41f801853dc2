package com.sankuai.meituan.waimai.heron.contract.gateway.flow.model;

import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/6/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WaitMissionCompleteStepParam {


    private Long wmPoiId;

    private Long sessionId;

    private SessionCategoryEnum sessionCategory;

    private HeronContractOperator operator;
}
