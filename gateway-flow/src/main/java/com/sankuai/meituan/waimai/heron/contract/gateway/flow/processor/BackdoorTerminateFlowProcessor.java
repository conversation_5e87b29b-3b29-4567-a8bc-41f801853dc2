package com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayNoAlarmBusinessException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.GatewayPreconditions;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.AllStructFlowTerminateFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.terminate.HeronContractFlowTerminateParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/1/29
 */
@Slf4j
@Component
public class BackdoorTerminateFlowProcessor {

    @Resource
    private AllStructFlowTerminateFlowStep allStructFlowTerminateFlowStep;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    public void terminate(Long wmPoiId, String targetStatus) throws GatewayNoAlarmBusinessException {
        GatewayPreconditions.checkArgumentNoAlarm(wmPoiId != null && wmPoiId > 0, "门店id必须大于0");
        GatewayPreconditions.checkArgumentNoAlarm(StringUtils.isNoneBlank(targetStatus), "目标状态不能为空");
        ContractSessionStatusEnum targetStatusEnum = ContractSessionStatusEnum.valueOf(targetStatus);
        HeronContractFlowTerminateParam terminateParam = buildTerminateStepParam(wmPoiId, targetStatusEnum);
        allStructFlowTerminateFlowStep.execute(terminateParam);
    }

    private HeronContractFlowTerminateParam buildTerminateStepParam(Long wmPoiId, ContractSessionStatusEnum targetStatusEnum) {
        HeronContractFlowTerminateParam terminateParam = new HeronContractFlowTerminateParam();
        terminateParam.setWmPoiId(wmPoiId);
        HeronContractGatewaySession session = heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(wmPoiId, SessionCategoryEnum.CORE);
        terminateParam.setSessionId(Optional.ofNullable(session).map(HeronContractGatewaySession::getSessionLeafId).orElse(-1L));
        terminateParam.setTargetStatus(targetStatusEnum);
        terminateParam.setOperator(HeronContractOperator.builder()
                .opId(-1L)
                .opName("系统终止流程")
                .build());
        terminateParam.setTerminateReason("人工终止流程");
        terminateParam.setSessionCategory(SessionCategoryEnum.CORE);
        return terminateParam;
    }
}
