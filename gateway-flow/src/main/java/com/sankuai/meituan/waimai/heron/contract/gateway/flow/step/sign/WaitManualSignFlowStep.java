package com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.sign;

import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.DispatchParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.DispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.sign.WaitManualSignReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.WaitManualSignStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.statushandler.FlowNextStatusHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.LogisticsFlowStep;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum.AUDIT_PASS;
import static com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum.WAIT_MANUAL_SIGN;

/**
 * @description: 生成待打包签约任务步骤
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
@Slf4j
public class WaitManualSignFlowStep implements LogisticsFlowStep<WaitManualSignStepParam> {

    @Resource
    private WaitManualSignReqDispatcher waitManualSignReqDispatcher;


    @Override
    @FlowNextStatusHandler(description = "生成待签约任务", requiredEnterStatus = {AUDIT_PASS}, successStatus = WAIT_MANUAL_SIGN)
    public FlowExecResult execute(WaitManualSignStepParam param) {
        DispatchParam<WaitManualSignStepParam> dispatchParam = new DispatchParam<>(param);

        DispatchResult dispatchResult = waitManualSignReqDispatcher.dispatch(dispatchParam);
        return FlowExecResult.build(dispatchResult);

    }
}