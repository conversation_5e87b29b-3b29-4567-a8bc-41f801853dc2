package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.audit.heron;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.HeronLogisticsThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestErrorTypeEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainMultiRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.MultiSendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.MultiSendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.audit.MultiCommitAuditBatchParam;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.WmPoiLogisticsBatchSubmitRequestDTO;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.WmPoiLogisticsBatchSubmitResponseDTO;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.batch.ResultDTO;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 多店提审请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class HeronMultiCommitAuditReqSender extends PlatDomainMultiRequestSender<MultiCommitAuditBatchParam, WmPoiLogisticsBatchSubmitRequestDTO> {


    @Resource
    private HeronLogisticsThriftServiceAdapter heronLogisticsThriftServiceAdapter;


    @Override
    protected WmPoiLogisticsBatchSubmitRequestDTO buildRemoteParam(MultiSendParam<MultiCommitAuditBatchParam> param) {
        MultiCommitAuditBatchParam commitAuditStepParam = param.getOriginParam();
        WmPoiLogisticsBatchSubmitRequestDTO requestDTO = new WmPoiLogisticsBatchSubmitRequestDTO();
        requestDTO.setWmPoiIdList(commitAuditStepParam.getWmPoiIdList());
        requestDTO.setOperatorDTO(OperatorConverter.toHeronOperator(commitAuditStepParam.getOperator()));
        requestDTO.setCustomerId(commitAuditStepParam.getCustomerId());
        requestDTO.setBatchTaskId(commitAuditStepParam.getBatchTaskId());
        return requestDTO;
    }

    @Override
    protected List<Long> getBatchWmPoiIdList(MultiSendParam<MultiCommitAuditBatchParam> param) {
        return param.getOriginParam().getWmPoiIdList();
    }

    @Override
    protected MultiSendResult doSend(WmPoiLogisticsBatchSubmitRequestDTO remoteParam) throws GatewayAdapterException {
        List<WmPoiLogisticsBatchSubmitResponseDTO> wmPoiLogisticsBatchSubmitResponseDTOS = heronLogisticsThriftServiceAdapter.multiCommitAudit(remoteParam);
        return buildResult(wmPoiLogisticsBatchSubmitResponseDTOS);
    }

    private MultiSendResult buildResult(List<WmPoiLogisticsBatchSubmitResponseDTO> wmPoiLogisticsBatchSubmitResponseDTOS) {
        Map<Long, SendResult> resultMap = new HashMap<>();
        for (WmPoiLogisticsBatchSubmitResponseDTO responseDTO : wmPoiLogisticsBatchSubmitResponseDTOS) {
            long wmPoiId = responseDTO.getWmPoiId();
            ResultDTO resultDTO = responseDTO.getResultDTO();
            if (Objects.nonNull(resultDTO) && BooleanUtils.isTrue(resultDTO.getSuccess())) {
                resultMap.put(wmPoiId, SendResult.success());
            } else {
                resultMap.put(wmPoiId, SendResult.fail(resultDTO.getCode(), resultDTO.getMsg(), RequestErrorTypeEnum.DOMAIN_BUSINESS_EXCEPTION));
            }
        }
        return new MultiSendResult(resultMap);
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.COMMIT_AUDIT;
    }

}