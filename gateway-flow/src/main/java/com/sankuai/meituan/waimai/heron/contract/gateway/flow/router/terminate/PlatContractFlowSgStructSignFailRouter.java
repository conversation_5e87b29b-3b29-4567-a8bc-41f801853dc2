package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.terminate;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.SinglePoiFlowRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.sign.old.HeronCancelSignReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.terminate.SgCancelSignReqSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ProcessingContractStructEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 医药结构流程数据终止
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Slf4j
@Component
public class PlatContractFlowSgStructSignFailRouter extends SinglePoiFlowRequestRouter {

    @Resource
    private HeronCancelSignReqSender heronCancelSignReqSender;

    @Resource
    private SgCancelSignReqSender sgCancelSignReqSender;

    @Override
    public SubDomainEnum routeTargetDomain() {
        return SubDomainEnum.SG;
    }

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setAreaSplit(routeParam.getProcessingAreaSplit());
        routeResult.setPerfSplit(routeParam.getProcessingPerfSplit());
        routeResult.setPerfDR(routeParam.getProcessingPerfDR());
        routeResult.setRequestSender(matchReqSender(routeParam));
        return routeResult;
    }


    private RequestSender matchReqSender(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        log.info("PlatContractFlowSgStructSignFailRouter routeParam: {}", JacksonUtil.writeAsJsonStr(routeParam));
        ProcessingContractStructEnum contractStruct = routeParam.getProcessingContractStruct();
        if (BooleanUtils.isTrue(routeParam.getSgTransferDrProcess())) {
            if (routeParam.getHasSgProcess()) {
                return sgCancelSignReqSender;
            } else if (Objects.isNull(contractStruct)) {
                return null;
            } else {
                return heronCancelSignReqSender;
            }
        }
        return null;
    }
}
