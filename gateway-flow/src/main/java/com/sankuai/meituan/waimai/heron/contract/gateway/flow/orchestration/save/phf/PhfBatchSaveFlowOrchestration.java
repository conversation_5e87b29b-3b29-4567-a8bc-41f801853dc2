package com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.save.phf;

import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.batchflow.BatchSignFlowRedisHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.config.MccConfig;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.BatchFlowRedisStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.BatchSignFlowSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLockHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.audit.CommitAuditStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfBatchSaveWithSessionParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfBatchSplitSingleSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfBatchTerminateStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.AuditRejectStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.SaveFailStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.LogisticsFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.audit.CommitAuditFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.save.phf.PhfBatchSplitSingleSaveCheckFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.save.phf.PhfBatchSplitSingleSaveFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.CommitAuditFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.SaveFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.phf.PhfBatchTerminateFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractBatchFlowSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.save.PhfContractInfoParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.GatewayResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.result.SinglePoiResult;
import com.sankuai.meituan.waimai.logistics.contract.client.constants.phf.PhfContractTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
@Service
@Slf4j
public class PhfBatchSaveFlowOrchestration implements LogisticsFlowOrchestration<PhfBatchSaveWithSessionParam> {


    private final ExecutorService flowOrchestrationExecutorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 2, Runtime.getRuntime().availableProcessors() * 2, 0L,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(20000), new ThreadPoolExecutor.DiscardPolicy());


    @Resource
    private BatchSignFlowRedisHelper batchSignFlowRedisHelper;

    @Resource
    private PhfBatchSplitSingleSaveCheckFlowStep phfBatchSplitSingleSaveCheckFlowStep;

    @Resource
    private PhfBatchSplitSingleSaveFlowStep phfBatchSplitSingleSaveFlowStep;

    @Resource
    private SaveFailFlowStep saveFailFlowStep;

    @Resource
    private CommitAuditFlowStep commitAuditFlowStep;

    @Resource
    private CommitAuditFailFlowStep commitAuditFailFlowStep;

    @Resource
    private PhfBatchTerminateFlowStep phfBatchTerminateFlowStep;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private EntryAndProcessorLockHandler entryAndProcessorLockHandler;


    @Override
    public OrchestrationExecResult flow(PhfBatchSaveWithSessionParam saveWithSessionParam) {
        PhfContractBatchFlowSaveParam saveParam = saveWithSessionParam.getSaveParam();
        List<Long> wmPoiIdList = saveParam.getWmPoiIdList();
        log.info("PhfBatchSaveFlowOrchestration param={}", JacksonUtil.writeAsJsonStr(saveParam));
        Map<Long, PhfContractInfoParam> contractInfoParamMap = saveParam.getContractInfoList().stream().collect(Collectors.toMap(PhfContractInfoParam::getWmPoiId, Function.identity()));
        //多线程按单门店进行保存
        Map<Long, Future<OrchestrationExecResult>> poiSaveResultFutureMap = Maps.newHashMap();
        Map<Long, HeronContractGatewaySession> poiAndSessionMap = saveWithSessionParam.getSessionList().stream()
                .collect(Collectors.toMap(HeronContractGatewaySession::getWmPoiId, Function.identity()));
        List<SinglePoiResult> poiSaveResultList = Collections.synchronizedList(new ArrayList<>());
        RateLimiter rateLimiter = RateLimiter.create(MccConfig.getPhfBatchSaveFlowPoiRateLimit());
        for (Long wmPoiId : wmPoiIdList) {
            rateLimiter.acquire();
            if (batchSignFlowRedisHelper.getBatchFlowStatus(String.valueOf(saveParam.getBatchSignTaskId()), BatchSignFlowSceneEnum.PHF_BATCH_SIGN_FLOW_SCENE) != BatchFlowRedisStatusEnum.SAVING) {
                log.info("门店所在批量任务不是保存状态 wmPoiId={}, batchSignTaskId={}", wmPoiId, saveParam.getBatchSignTaskId());
                poiSaveResultList.add(SinglePoiResult.builder().wmPoiId(wmPoiId).result(GatewayResult.fail(-1, "批量任务已终止")).build());
            }
            HeronContractGatewaySession session = poiAndSessionMap.get(wmPoiId);
            log.info("批量保存 wmPoiId={} session={}", wmPoiId, JacksonUtil.writeAsJsonStr(session));

            Future<OrchestrationExecResult> poiSaveResultFuture = flowOrchestrationExecutorService.submit(() ->
                    GlobalFlowSessionHandler.applyWithSession(session, null, p -> {
                        try {
                            return entryAndProcessorLockHandler.applyWithLock(wmPoiId, SessionCategoryEnum.PHF, null, x -> {
                                OrchestrationExecResult execResult = doFlow(wmPoiId, saveParam, contractInfoParamMap);
                                // 如果保存失败且合同类型为拼好送合同，则终止批量流程，其他类型合同只处理该门店
                                if (execResult.isFail()) {
                                    log.info("门店校验、保存失败或提审失败 wmPoiId={}, batchSignTaskId={} execResult={}", wmPoiId, saveParam.getBatchSignTaskId(), JacksonUtil.writeAsJsonStr(execResult));
                                    //从缓存门店列表中移出该门店
                                    batchSignFlowRedisHelper.poiSaveFailOrAuditBack(String.valueOf(saveParam.getBatchSignTaskId()), BatchSignFlowSceneEnum.PHF_BATCH_SIGN_FLOW_SCENE, wmPoiId);
                                    //拼好送正式版保存、提审失败
                                    if (PhfContractTypeEnum.PHS_CONTRACT.getCode().equals(saveParam.getContractType()) && !PhfBatchSplitSingleSaveCheckFlowStep.class.equals(execResult.getExecFailStepClazz())) {
                                        phfBatchTerminateFlowStep.execute(PhfBatchTerminateStepParam.builder()
                                                .batchRelationId(String.valueOf(saveParam.getBatchSignTaskId()))
                                                .flowFailReason("门店[" + wmPoiId + "]保存失败:" + execResult.getMessage())
                                                .build());
                                    }
                                }
                                return execResult;
                            });
                        } catch (Throwable e) {
                            log.info("拼好饭批量发起签约 门店={} applyWithLock error", wmPoiId, e);
                            return OrchestrationExecResult.fail("门店[" + wmPoiId + "]拼好饭批量发起签约处理失败");
                        }

                    }));
            poiSaveResultFutureMap.put(wmPoiId, poiSaveResultFuture);
        }
        if (batchSignFlowRedisHelper.getBatchFlowStatus(String.valueOf(saveParam.getBatchSignTaskId()), BatchSignFlowSceneEnum.PHF_BATCH_SIGN_FLOW_SCENE) != BatchFlowRedisStatusEnum.SAVING) {
            return buildAllPoiSaveFailResult(saveParam);
        }
        boolean allPoiSaveSuccess = true;

        for (Map.Entry<Long, Future<OrchestrationExecResult>> poiSaveResultFutureEntry : poiSaveResultFutureMap.entrySet()) {
            Long wmPoiId = poiSaveResultFutureEntry.getKey();
            try {
                OrchestrationExecResult poiSaveResult = poiSaveResultFutureEntry.getValue().get();
                SinglePoiResult singlePoiResult = new SinglePoiResult();
                singlePoiResult.setWmPoiId(wmPoiId);
                if (poiSaveResult != null && poiSaveResult.getSuccess()) {
                    singlePoiResult.setResult(GatewayResult.success());
                } else if (poiSaveResult != null) {
                    singlePoiResult.setResult(GatewayResult.fail(-1, poiSaveResult.getMessage()));
                    allPoiSaveSuccess = false;
                } else {
                    singlePoiResult.setResult(GatewayResult.fail(-1, "保存失败"));
                    allPoiSaveSuccess = false;
                }
                poiSaveResultList.add(singlePoiResult);
            } catch (Exception e) {
                log.error("获取门店保存结果异常 wmPoiId={}, batchSignTaskId={}", wmPoiId, saveParam.getBatchSignTaskId(), e);
                poiSaveResultList.add(SinglePoiResult.builder()
                        .wmPoiId(wmPoiId)
                        .result(GatewayResult.fail(-1, "获取门店保存结果异常"))
                        .build());
                allPoiSaveSuccess = false;
            }
        }
        OrchestrationExecResult result = new OrchestrationExecResult();
        result.setSuccess(allPoiSaveSuccess);
        result.setData(poiSaveResultList);
        result.setMessage(buildFlowMessage(poiSaveResultList));
        return result;
    }

    private OrchestrationExecResult<List<SinglePoiResult>> buildAllPoiSaveFailResult(PhfContractBatchFlowSaveParam param) {
        OrchestrationExecResult<List<SinglePoiResult>> result = new OrchestrationExecResult();
        result.setSuccess(false);
        List<SinglePoiResult> singlePoiResultList = new ArrayList<>();
        for (Long wmPoiId : param.getWmPoiIdList()) {
            singlePoiResultList.add(SinglePoiResult.builder()
                    .wmPoiId(wmPoiId)
                    .result(GatewayResult.fail(-1, "保存失败"))
                    .build());
        }
        result.setData(singlePoiResultList);
        result.setMessage("批量任务全部处理失败");
        return result;
    }

    private String buildFlowMessage(List<SinglePoiResult> poiSaveResultList) {
        List<Long> failPoiList = poiSaveResultList.stream()
                .filter(x -> !x.getResult().getSuccess())
                .map(SinglePoiResult::getWmPoiId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(failPoiList)) {
            return null;
        } else if (failPoiList.size() == poiSaveResultList.size()) {
            return "批量任务全部处理失败";
        } else {
            return "批量任务部分门店保存失败，门店列表：[" + StringUtils.substring(failPoiList.toString(), 0, 200) + "]";
        }
    }


    private OrchestrationExecResult doFlow(Long wmPoiId, PhfContractBatchFlowSaveParam param, Map<Long, PhfContractInfoParam> contractInfoParamMap) {

        try {
            PhfBatchSplitSingleSaveParam singleSaveParam = PhfBatchSplitSingleSaveParam.build(wmPoiId, param, contractInfoParamMap);
            FlowExecResult checkResult = phfBatchSplitSingleSaveCheckFlowStep.execute(singleSaveParam);
            boolean batchSignStatusError = batchSignFlowRedisHelper.getBatchFlowStatus(String.valueOf(param.getBatchSignTaskId()), BatchSignFlowSceneEnum.PHF_BATCH_SIGN_FLOW_SCENE) != BatchFlowRedisStatusEnum.SAVING;
            HeronContractGatewaySession prevProcessingFlowSession = heronContractGatewaySessionBasicService.getLastProcessingFlowSessionRT(wmPoiId, SessionCategoryEnum.PHF);
            if (checkResult.isFail()) {
                if (prevProcessingFlowSession == null) {
                    saveCheckFailSession("failTraceId=" + Tracer.id() + ", 子领域" + checkResult.getFailCauseDomain() + ", 错误原因: " + checkResult.getMessage());
                }
                return OrchestrationExecResult.fail(PhfBatchSplitSingleSaveCheckFlowStep.class, checkResult);
            }
            if (batchSignStatusError) {
                saveCheckFailSession("failTraceId=" + Tracer.id() + ", 批量任务已终止");
                log.info("批次任务状态非保存中，门店签约流程终止 wmPoiId={}, batchSignTaskId={}", wmPoiId, param.getBatchSignTaskId());
                return OrchestrationExecResult.fail("批量任务流程已终止，单门店流程终止");
            }
            if (prevProcessingFlowSession != null) {
                return OrchestrationExecResult.fail("存在流程中session，无法发起新流程");
            }
            //拼好饭在校验通过时插入session
            heronContractGatewaySessionBasicService.insert(GlobalFlowSessionHandler.getSession());
            FlowExecResult saveResult = phfBatchSplitSingleSaveFlowStep.execute(singleSaveParam);
            if (saveResult.isFail()) {
                FlowExecResult terminateResult = saveFailFlowStep.execute(buildSaveFailStepParam(wmPoiId, param, saveResult));
                return OrchestrationExecResult.fail(PhfBatchSplitSingleSaveFlowStep.class, saveResult, terminateResult);
            }
            FlowExecResult commitAuditResult = commitAuditFlowStep.execute(buildCommitAuditParam(wmPoiId, param));
            if (commitAuditResult.isFail()) {
                FlowExecResult terminateResult = commitAuditFailFlowStep.execute(buildCommitAuditFailStepParam(wmPoiId, param, commitAuditResult));
                return OrchestrationExecResult.fail(CommitAuditFlowStep.class, commitAuditResult, terminateResult);
            }
            return OrchestrationExecResult.success();
        } catch (Exception e) {
            log.error("doFlow error, wmPoiId:{}", wmPoiId, e);
            return OrchestrationExecResult.fail("系统异常");
        }
    }

    private void saveCheckFailSession(String failRemark) {
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        session.setStatus(ContractSessionStatusEnum.SAVE_FAIL);
        session.setFailRemark(failRemark.substring(0, Math.min(failRemark.length(), 200)));
        heronContractGatewaySessionBasicService.insert(session);
    }

    private CommitAuditStepParam buildCommitAuditParam(Long wmPoiId, PhfContractBatchFlowSaveParam param) {
        return CommitAuditStepParam.builder()
                .operator(param.getOperator())
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .wmPoiId(wmPoiId)
                .build();
    }

    private AuditRejectStepParam buildCommitAuditFailStepParam(Long wmPoiId, PhfContractBatchFlowSaveParam param, FlowExecResult commitAuditResult) {
        return AuditRejectStepParam.builder()
                .causeDomain(commitAuditResult.getFailCauseDomain())
                .wmPoiId(wmPoiId)
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .failReason("提审失败:" + commitAuditResult.getMessage())
                .operator(param.getOperator())
                .build();
    }



    private SaveFailStepParam buildSaveFailStepParam(Long wmPoiId, PhfContractBatchFlowSaveParam param, FlowExecResult saveResult) {
        return SaveFailStepParam.builder()
                .causeDomain(saveResult.getFailCauseDomain())
                .wmPoiId(wmPoiId)
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .failReason("保存失败:" + saveResult.getMessage())
                .operator(param.getOperator())
                .build();
    }


}
