package com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.save;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.dispatcher.BaseFlowProcessingRequestDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.RouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.area.AreaMigrateSaveCheckReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.plat.PlatContractMigrateSavePreCheckReqRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractMigrateSaveParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 迁移场景-配送信息保存校验分发
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
@Slf4j
public class MigrateSubmitInfoPreCheckReqDispatcher extends BaseFlowProcessingRequestDispatcher<HeronContractMigrateSaveParam> {

    @Resource
    private RouteHelper routeHelper;

    @Override
    protected List<Class<? extends RequestRouter>> registerRouterClass() {
        return Lists.newArrayList(PlatContractMigrateSavePreCheckReqRouter.class, AreaMigrateSaveCheckReqRouter.class);
    }


    @Override
    protected RouteParam buildRouteParam(HeronContractMigrateSaveParam originParam) throws GatewayAdapterException {
        return routeHelper.buildFlowRouteParam(originParam.getWmPoiId());
    }
}