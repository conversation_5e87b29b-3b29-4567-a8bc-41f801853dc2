package com.sankuai.meituan.waimai.heron.contract.gateway.flow.helper;

import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 签约信息构建相关helper
 * @author: chenyihao04
 * @create: 2023-05-30 20:20
 */
@Slf4j
public class SignInfoBuildHelper {

    //直接组装电子合同需要的数据结构比较麻烦，先打平，然后再处理数据
    public static List<EcontractDataSourceBo> buildDataSourceList(Long wmPoiId, Long sessionId, List<EcontractDataSourceEnum> dataSourceEnumList) {
        List<EcontractDataSourceBo> list = new ArrayList<>();
        for (EcontractDataSourceEnum dataSourceEnum : dataSourceEnumList) {
            EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
            EcontractDataPoiBizBo econtractDataPoiBizBo = new EcontractDataPoiBizBo();
            econtractDataPoiBizBo.setWmPoiId(wmPoiId);
            econtractDataPoiBizBo.setBizId(sessionId);
            dataSourceBo.setSorceEnum(dataSourceEnum);
            dataSourceBo.setWmPoiIdAndBizIdList(Collections.singletonList(econtractDataPoiBizBo));
            list.add(dataSourceBo);
        }
        return list;
    }

    public static List<EcontractDataSourceBo> formatEcontractSignData(List<EcontractDataSourceBo> sourceBoList) {
        List<EcontractDataSourceBo> list = new ArrayList<>();
        Map<EcontractDataSourceEnum, List<EcontractDataPoiBizBo>> enumToPoiInfoBoMap = sourceBoList.stream()
                .collect(Collectors.groupingBy(EcontractDataSourceBo::getSorceEnum))
                .entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, o -> o.getValue().stream().map(EcontractDataSourceBo::getWmPoiIdAndBizIdList).flatMap(Collection::stream).collect(Collectors.toList())));
        for (Map.Entry<EcontractDataSourceEnum, List<EcontractDataPoiBizBo>> entry : enumToPoiInfoBoMap.entrySet()) {
            EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
            dataSourceBo.setSorceEnum(entry.getKey());
            dataSourceBo.setWmPoiIdAndBizIdList(entry.getValue());
            list.add(dataSourceBo);
        }
        return list;
    }


    public static List<EcontractDataSourceBo> formatEcontractSignData(List<EcontractDataSourceBo> sourceBoList, List<List<Long>> wmPoiIdGroupList) {
        log.info("formatEcontractSignData wmPoiIdGroupList={}", wmPoiIdGroupList);
        List<EcontractDataSourceBo> resultSourceBoList = formatEcontractSignData(sourceBoList);
        if (CollectionUtils.isEmpty(wmPoiIdGroupList)) {
            return resultSourceBoList;
        }
        for (EcontractDataSourceBo dataSourceBo : resultSourceBoList) {
            List<EcontractDataPoiBizBo> poiBizBoList = dataSourceBo.getWmPoiIdAndBizIdList();
            List<Long> allWmPoiIdList = poiBizBoList.stream().map(EcontractDataPoiBizBo::getWmPoiId).collect(Collectors.toList());
            List<List<Long>> resultWmPoiIdGroupList = new ArrayList<>();
            for (List<Long> wmPoiIdGroup : wmPoiIdGroupList) {
                List<Long> wmPoiIdList = wmPoiIdGroup.stream().filter(allWmPoiIdList::contains).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(wmPoiIdList)) {
                    resultWmPoiIdGroupList.add(wmPoiIdList);
                }
            }
            dataSourceBo.setWmPoiIdGroupList(resultWmPoiIdGroupList);
        }
        return resultSourceBoList;
    }


}