//package com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.audit.phf;
//
//import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewayRequestBasicService;
//import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// * @date 2024/12/17
// */
//public class PhfAuditPassFlowStep {
//
//    @Resource
//    private HeronContractGatewayRequestBasicService heronContractGatewayRequestBasicService;
//
//    @Resource
//    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;
//}
