package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.plat;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PlatSingleFlowPoiRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.plat.PlatContractUpdateInfoSaveReqSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 换签任务和更新路由
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
@Slf4j
public class PlatContractUpdateInfoSaveReqRouter extends PlatSingleFlowPoiRequestRouter {

    @Resource
    private PlatContractUpdateInfoSaveReqSender platContractUpdateInfoSaveReqSender;

    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setPerfSplit(routeParam.getProcessingPerfSplit());
        routeResult.setAreaSplit(routeParam.getProcessingAreaSplit());
        routeResult.setPerfDR(routeParam.getProcessingPerfDR());
        routeResult.setRequestSender(matchReqSender(routeParam));
        return routeResult;
    }

    private RequestSender matchReqSender(SinglePoiFlowRouteParam routeParam) {
        if (routeParam.getProcessingContractStruct().isContractStruct()) {
            return platContractUpdateInfoSaveReqSender;
        } else if (routeParam.getProcessingContractStruct().isOldStruct()) {
            log.error("本期暂不支持老结构流程, wmPoiId: {}", routeParam.getWmPoiId());
        }
        return null;
    }

}