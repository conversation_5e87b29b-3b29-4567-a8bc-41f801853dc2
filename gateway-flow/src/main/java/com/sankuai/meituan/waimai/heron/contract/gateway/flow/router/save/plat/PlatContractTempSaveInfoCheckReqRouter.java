package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.plat;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PlatSingleFlowPoiRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.plat.PlatContractSettleSaveInfoCheckReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 暂存信息校验路由
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class PlatContractTempSaveInfoCheckReqRouter  extends PlatSingleFlowPoiRequestRouter {

    @Resource
    private PlatContractSettleSaveInfoCheckReqSender platContractSettleSaveInfoCheckReqSender;

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) {
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setPerfSplit(routeParam.getProcessingPerfSplit());
        routeResult.setAreaSplit(routeParam.getProcessingAreaSplit());
        routeResult.setPerfDR(routeParam.getProcessingPerfDR());
        routeResult.setRequestSender(platContractSettleSaveInfoCheckReqSender);
        return routeResult;
    }

}