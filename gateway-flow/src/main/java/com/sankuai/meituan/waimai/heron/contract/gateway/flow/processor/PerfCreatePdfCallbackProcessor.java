package com.sankuai.meituan.waimai.heron.contract.gateway.flow.processor;

import com.dianping.cat.util.StringUtils;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.bo.RequestBatchCallbackResultUpdateBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.bo.RequestContextBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewayRequest;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewayRequestBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestErrorTypeEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.lock.EntryAndProcessorLockHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.PerfContractCreatePdfCallbackParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.SessionFlowTerminateFlowStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.processing.PerfPdfCreateCallbackOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.SessionFlowTerminateFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.CancelSignOperateType;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Component
@Slf4j
public class PerfCreatePdfCallbackProcessor {

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private EntryAndProcessorLockHandler entryAndProcessorLockHandler;

    @Resource
    private SessionFlowTerminateFlowStep sessionFlowTerminateFlowStep;

    @Resource
    private PerfPdfCreateCallbackOrchestration perfPdfCallbackOrchestration;

    @Resource
    private HeronContractGatewayRequestBasicService heronContractGatewayRequestBasicService;

    public void perfPdfCallback(PerfContractCreatePdfCallbackParam param) throws Throwable {
        //暂时只支持phf
        ContractCreateAndUpdateSceneEnum sessionScene = ContractCreateAndUpdateSceneEnum.valueOf(param.getScene());
        List<HeronContractGatewaySession> sessionList = heronContractGatewaySessionBasicService.queryByBatchRelationRT(String.valueOf(param.getBatchRelationId()), sessionScene, SessionCategoryEnum.PHF);
        if (CollectionUtils.isEmpty(sessionList)) {
            log.warn("未查询到session信息，batchRelationId={}", param.getBatchRelationId());
            return;
        }
        //修改回调requestContext
        this.batchUpdatePdfCallback(sessionList, param);
        //校验session状态
        Map<Long, ContractSessionStatusEnum> poiSessionStatusMap = sessionList.stream()
                .collect(Collectors.toMap(HeronContractGatewaySession::getWmPoiId, HeronContractGatewaySession::getStatus));
        log.info("pdf创建完成回调 poiSessionStatusMap={}", JacksonUtil.writeAsJsonStr(poiSessionStatusMap));
        List<HeronContractGatewaySession> processingSessionList = sessionList.stream().filter(session -> session.getStatus().equals(ContractSessionStatusEnum.AUDIT_PASS)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(processingSessionList)) {
            log.warn("没有session是审核通过，不处理，sessionList={}", JacksonUtil.writeAsJsonStr(sessionList));
            return;
        }
        List<Long> wmPoiIdList = processingSessionList.stream().map(HeronContractGatewaySession::getWmPoiId).collect(Collectors.toList());
        SessionCategoryEnum sessionCategory = processingSessionList.get(0).getSessionCategory();
        entryAndProcessorLockHandler.batchApplyWithLock(wmPoiIdList, sessionCategory, null, p -> {
            if (param.getSuccess()) {
                perfPdfCallbackOrchestration.flow(processingSessionList);
            } else {
                //创建pdf失败，整体流程全部取消
                for (HeronContractGatewaySession session : processingSessionList) {
                    HeronContractOperator operator = HeronContractOperator.builder()
                            .opId(-1L)
                            .opName("创建履约pdf失败")
                            .build();
                    SessionFlowTerminateFlowStepParam stepParam = SessionFlowTerminateFlowStepParam.builder()
                            .sessionId(session.getSessionLeafId())
                            .failReason("创建履约pdf失败，批量任务流程终止")
                            .wmPoiId(session.getWmPoiId())
                            .cancelSignOperateType(CancelSignOperateType.NO_CANCEL_SIGN)
                            .operator(operator)
                            .sessionCategory(session.getSessionCategory())
                            .build();
                    GlobalFlowSessionHandler.applyWithSession(session, stepParam, sessionFlowTerminateFlowStep::execute);
                }
            }
            return null;
        });


    }


    private void batchUpdatePdfCallback(List<HeronContractGatewaySession> sessionList, PerfContractCreatePdfCallbackParam param) {
        try {
            log.info("batchUpdatePdfCallback start param={}", JacksonUtil.writeAsJsonStr(param));
            List<Long> sessionIdList = sessionList.stream().map(HeronContractGatewaySession::getSessionLeafId).collect(Collectors.toList());
            List<HeronContractGatewayRequest> requestList = heronContractGatewayRequestBasicService.queryPerfPdfRequestBySessionIds(sessionIdList);
            //单店有request，批量的没有request
            if (CollectionUtils.isEmpty(requestList)) {
                log.warn("batchUpdatePdfCallback 不存在创建pdf的request param={}", JacksonUtil.writeAsJsonStr(param));
                return;
            }
            List<Long> requestIdList = requestList.stream().map(HeronContractGatewayRequest::getRequestLeafId).collect(Collectors.toList());
            RequestContextBo requestContextBo = new RequestContextBo();
            requestContextBo.setCallbackTraceId(Tracer.id());
            requestContextBo.setPerfPdfCreateSuccess(param.getSuccess());
            RequestStatusEnum statusEnum = BooleanUtils.isTrue(param.getSuccess()) ? RequestStatusEnum.CALLBACK_SUCCESS : RequestStatusEnum.CALLBACK_FAIL;
            RequestErrorTypeEnum errorTypeEnum = BooleanUtils.isNotTrue(param.getSuccess()) ? RequestErrorTypeEnum.CALLBACK_TO_ERROR : null;
            String errorMessage = BooleanUtils.isNotTrue(param.getSuccess()) && StringUtils.isNotEmpty(param.getMessage()) ? param.getMessage() : "";
            RequestBatchCallbackResultUpdateBo requestCallbackResultUpdateBo = RequestBatchCallbackResultUpdateBo.builder()
                    .context(JacksonUtil.writeAsJsonStrIgnoreNull(requestContextBo))
                    .requestLeafIdList(requestIdList)
                    .status(statusEnum)
                    .errorType(errorTypeEnum)
                    .errorMessage(errorMessage)
                    .build();
            heronContractGatewayRequestBasicService.updateCallbackResultByIdList(requestCallbackResultUpdateBo);
        } catch (Exception e) {
            log.error("batchUpdatePdfCallback error param={}", JacksonUtil.writeAsJsonStr(param), e);
        }
    }

}
