package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.confirm;

import com.sankuai.meituan.banma.poi.sparea.thrift.param.sign.BmOpenSpAreaPoiConfirmedNotificationRequest;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmAreaFlowThriftServiceAdapterOld;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.AreaDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.ConfirmSignStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 签约确认请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class AreaSignConfirmReqSender extends AreaDomainRequestSender<ConfirmSignStepParam, BmOpenSpAreaPoiConfirmedNotificationRequest> {

    @Resource
    private BmAreaFlowThriftServiceAdapterOld bmAreaFlowThriftServiceAdapterOld;



    @Override
    protected BmOpenSpAreaPoiConfirmedNotificationRequest buildRemoteParam(SendParam<ConfirmSignStepParam> param) {
        ConfirmSignStepParam originParam = param.getOriginParam();
        BmOpenSpAreaPoiConfirmedNotificationRequest request = new BmOpenSpAreaPoiConfirmedNotificationRequest();
        request.setPoiId(originParam.getWmPoiId());
        request.setSessionId(originParam.getSessionId());
        request.setOperator(OperatorConverter.toAreaOperator(originParam.getOperator()));
        return request;
    }


    @Override
    protected void sendToSpArea(BmOpenSpAreaPoiConfirmedNotificationRequest remoteParam) throws GatewayAdapterException {
        bmAreaFlowThriftServiceAdapterOld.notifyPoiConfirmed(remoteParam);
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.CONFIRM_SIGN;
    }

}