package com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.processing.phf;

import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.WaitMissionCompleteStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.ConfirmSignStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.LogisticsFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.effect.WaitMissionCompleteFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.sign.SessionFlowConfirmSignFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.phf.flow.PhfContractDrWaitCompleteParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/2/20
 */
@Component
@Slf4j
public class PhfWaitCompleteDrFlowOrchestration implements LogisticsFlowOrchestration<PhfContractDrWaitCompleteParam> {

    @Resource
    private SessionFlowConfirmSignFlowStep sessionFlowConfirmSignFlowStep;

    @Resource
    private WaitMissionCompleteFlowStep waitMissionCompleteFlowStep;


    @Override
    public OrchestrationExecResult flow(PhfContractDrWaitCompleteParam param) {
        //待生效有pdf，则调用签约确认
        if (StringUtils.isNotEmpty(param.getTechPdfUrl())) {
            FlowExecResult confirmExecResult = sessionFlowConfirmSignFlowStep.execute(buildConfirmSignStepParam(param));
            if (!confirmExecResult.getSuccess()) {
                log.warn("拼好饭双写待生效 签约确认处理失败 param={} confirmExecResult={}", JacksonUtil.writeAsJsonStr(param), JacksonUtil.writeAsJsonStr(confirmExecResult));
            }
        }
        FlowExecResult waitCompleteResult = waitMissionCompleteFlowStep.execute(buildWaitMissionCompleteStepParam(param.getWmPoiId()));
        if (waitCompleteResult.getSuccess()) {
            return OrchestrationExecResult.success();
        }
        return OrchestrationExecResult.fail(waitMissionCompleteFlowStep.getClass(), waitCompleteResult);
    }


    private ConfirmSignStepParam buildConfirmSignStepParam(PhfContractDrWaitCompleteParam param) {
        HeronContractGatewaySession session = GlobalFlowSessionHandler.getSession();
        ConfirmSignStepParam signStepParam = ConfirmSignStepParam.builder()
                .wmPoiId(param.getWmPoiId())
                .sessionId(Optional.ofNullable(session).map(HeronContractGatewaySession::getSessionLeafId).orElse(null))
                .operator(param.getOperator())
                .sessionCategory(GlobalFlowSessionHandler.getSessionCategory())
                .build();

        signStepParam.setTechPdfUrl(param.getTechPdfUrl());
        signStepParam.setPerfPdfUrl(param.getPerfPdfUrl());

        return signStepParam;
    }

    private WaitMissionCompleteStepParam buildWaitMissionCompleteStepParam(Long wmPoiId) {
        HeronContractOperator operator = HeronContractOperator.builder()
                .opName(GlobalFlowSessionHandler.getSession().getOpName())
                .opId(GlobalFlowSessionHandler.getSession().getOpId())
                .build();
        return WaitMissionCompleteStepParam.builder()
                .wmPoiId(wmPoiId)
                .operator(operator)
                .sessionId(GlobalFlowSessionHandler.getSessionId())
                .build();
    }



}
