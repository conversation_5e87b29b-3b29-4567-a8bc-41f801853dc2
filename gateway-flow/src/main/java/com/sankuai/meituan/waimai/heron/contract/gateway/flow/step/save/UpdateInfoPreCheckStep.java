package com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.save;

import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.DispatchParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.DispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.save.UpdateInfoPreCheckReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.LogisticsFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractUpdateAndSceneParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-09-05 16:59
 */
@Service
@Slf4j
public class UpdateInfoPreCheckStep implements LogisticsFlowStep<HeronContractUpdateAndSceneParam> {

    @Resource
    private UpdateInfoPreCheckReqDispatcher updateInfoPreCheckReqDispatcher;


    @Override
    public FlowExecResult execute(HeronContractUpdateAndSceneParam param) {
        DispatchParam<HeronContractUpdateAndSceneParam> dispatchParam = new DispatchParam<>(param);

        DispatchResult dispatchResult = updateInfoPreCheckReqDispatcher.dispatch(dispatchParam);
        return FlowExecResult.build(dispatchResult);
    }
}