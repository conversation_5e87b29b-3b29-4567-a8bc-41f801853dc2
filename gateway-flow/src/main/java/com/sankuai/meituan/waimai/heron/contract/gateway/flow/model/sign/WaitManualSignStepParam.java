package com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign;

import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 发起待签约任务
 * @author: chenyihao04
 * @create: 2023-05-17 19:19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WaitManualSignStepParam {

    private Long wmPoiId;

    private Long sessionId;

    private Long manualConfirmId;

//    private Long customerId;

    private HeronContractOperator operator;
}