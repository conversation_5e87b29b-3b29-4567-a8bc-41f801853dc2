package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.save.perf.phf;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PerfSinglePoiFlowRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.perf.phf.PerfPhfMockSaveCheckReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/11
 */
@Component
public class PerfPhfMockSaveCheckReqRouter extends PerfSinglePoiFlowRequestRouter {

    @Resource
    private PerfPhfMockSaveCheckReqSender perfPhfMockSaveCheckReqSender;

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        return super.buildDefaultPerfResult(routeParam, perfPhfMockSaveCheckReqSender);
    }
}
