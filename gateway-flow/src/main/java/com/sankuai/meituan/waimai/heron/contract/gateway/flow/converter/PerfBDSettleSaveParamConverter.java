package com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter;

import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.dto.SpAreaLogisticsAreaSaveDTO;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.CheckSaveSinglePoiParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.SaveSinglePoiParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.ServiceBrandProductParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.StationParam;
import com.sankuai.meituan.banma.thrift.deliveryproduct.common.ProductPackUtils;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.GatewayPreconditions;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.WmLogisticsSaleStrategyHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ApplySignActionEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.OperateSourceEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractBDSettleJudgeSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.save.HeronContractBDSettleSaveParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.BdSettleSaveBaseParamStruct;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.structure.LogisticsBrandProductParamStruct;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.WmLogisticsContractItemDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.NeedSignResponseDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.GatewayConstants.SG_SETTLE_SOURCE;
import static com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.GatewayConstants.SG_SPECIAL_APPROVAL_URL_ITEM_NAME;
import static com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.GatewayConstants.YY_SETTLE_SOURCE;

/**
 * <AUTHOR>
 * @date 2023/6/12
 */
public class PerfBDSettleSaveParamConverter {


    public static SaveSinglePoiParam convertSaveParam(SendParam<HeronContractBDSettleSaveParam> sendParam) throws GatewayArgumentException {
        HeronContractBDSettleSaveParam bdSettleSaveParam = sendParam.getOriginParam();
        BdSettleSaveBaseParamStruct baseParam = JacksonUtil.readValue(bdSettleSaveParam.getBdSaveBaseParam(), BdSettleSaveBaseParamStruct.class);
        GatewayPreconditions.checkNotNull(baseParam, "配送信息基本参数不能为空");
        SaveSinglePoiParam saveSinglePoiParam = new SaveSinglePoiParam();
        saveSinglePoiParam.setOperatorParam(OperatorConverter.toPerfOperator(bdSettleSaveParam.getOperator()));

        saveSinglePoiParam.setSessionId(GlobalFlowSessionHandler.getSessionId());
        saveSinglePoiParam.setWmPoiId(bdSettleSaveParam.getWmPoiId());
        saveSinglePoiParam.setFeeMode(baseParam.getFeeMode());
        List<ServiceBrandProductParam> brandProductParamList = convertPerfProductList(baseParam.getServiceBrandProductList());
        saveSinglePoiParam.setServiceBrandProductParam(brandProductParamList);
        saveSinglePoiParam.setContractFeeItemList(baseParam.getPerfContractItemList());
        saveSinglePoiParam.setDeliveryFee(extractDeliveryFee(baseParam));
        saveSinglePoiParam.setSpecialApprovalUrl(extractSpecialApprovalUrl(bdSettleSaveParam.getOperator().getOpSource(), baseParam));
        saveSinglePoiParam.setRequestId(sendParam.getRequestLeafId());
        saveSinglePoiParam.setNeedPackageSign(ApplySignActionEnum.LATER.equals(GlobalFlowSessionHandler.getSession().getContext().getApplySignAction()));
        saveSinglePoiParam.setPlatShareInfo(sendParam.getFlowShareInfoMap().get(SubDomainEnum.PLAT));
        saveSinglePoiParam.setBusinessIdentity(GlobalFlowSessionHandler.getPerfBusinessIdentity());
        saveSinglePoiParam.setStationParamList(buildStationParamList(baseParam));
        saveSinglePoiParam.setQueryPerfSourceId(baseParam.getQueryPerfSourceId());
        saveSinglePoiParam.setQueryPerfSourceIdMap(baseParam.getQueryPerfSourceIdMap());
        saveSinglePoiParam.setRefreshFee(BooleanUtils.isTrue(baseParam.getSgRefreshFee()) || BooleanUtils.isTrue(baseParam.getRefreshFee()));
        saveSinglePoiParam.setRefreshFeeMap(baseParam.getRefreshFeeMap());
        saveSinglePoiParam.setNeedSpecial(baseParam.getNeedSpecial());
        saveSinglePoiParam.setSpecialApprovalApplyType(baseParam.getSpecialApprovalApplyType());
        saveSinglePoiParam.setSpecialApprovalUrlMap(extractSpecialApprovalUrlMap(baseParam));
        return saveSinglePoiParam;
    }

    public static SaveSinglePoiParam convertNeedSignParam(SendParam<HeronContractBDSettleJudgeSignParam> judgeSignParam) throws GatewayArgumentException {
        HeronContractBDSettleJudgeSignParam originParam = judgeSignParam.getOriginParam();
        BdSettleSaveBaseParamStruct baseParam = JacksonUtil.readValue(originParam.getBdSaveBaseParam(), BdSettleSaveBaseParamStruct.class);
        GatewayPreconditions.checkNotNull(baseParam, "配送信息基本参数不能为空");
        SaveSinglePoiParam saveSinglePoiParam = new SaveSinglePoiParam();
        saveSinglePoiParam.setOperatorParam(OperatorConverter.toPerfOperator(originParam.getOperator()));

        saveSinglePoiParam.setWmPoiId(originParam.getWmPoiId());
        saveSinglePoiParam.setFeeMode(baseParam.getFeeMode());
        List<ServiceBrandProductParam> brandProductParamList = convertPerfProductList(baseParam.getServiceBrandProductList());
        saveSinglePoiParam.setServiceBrandProductParam(brandProductParamList);
        saveSinglePoiParam.setContractFeeItemList(baseParam.getPerfContractItemList());
        saveSinglePoiParam.setDeliveryFee(extractDeliveryFee(baseParam));
        saveSinglePoiParam.setSpecialApprovalUrl(extractSpecialApprovalUrl(originParam.getOperator().getOpSource(), baseParam));
        String platShareInfo = judgeSignParam.getFlowShareInfoMap().get(SubDomainEnum.PLAT);
        NeedSignResponseDTO needSignResponseDTO = JacksonUtil.readValue(platShareInfo, NeedSignResponseDTO.class);
        saveSinglePoiParam.setPlatShareInfo(Optional.ofNullable(needSignResponseDTO).map(NeedSignResponseDTO::getShareInfo).orElse(""));
        saveSinglePoiParam.setBusinessIdentity(GlobalFlowSessionHandler.getPerfBusinessIdentity());
        saveSinglePoiParam.setStationParamList(buildStationParamList(baseParam));
        saveSinglePoiParam.setQueryPerfSourceId(baseParam.getQueryPerfSourceId());
        saveSinglePoiParam.setQueryPerfSourceIdMap(baseParam.getQueryPerfSourceIdMap());
        saveSinglePoiParam.setRefreshFee(BooleanUtils.isTrue(baseParam.getSgRefreshFee()) || BooleanUtils.isTrue(baseParam.getRefreshFee()));
        saveSinglePoiParam.setRefreshFeeMap(baseParam.getRefreshFeeMap());
        saveSinglePoiParam.setNeedSpecial(baseParam.getNeedSpecial());
        saveSinglePoiParam.setSpecialApprovalApplyType(baseParam.getSpecialApprovalApplyType());
        saveSinglePoiParam.setSpecialApprovalUrlMap(extractSpecialApprovalUrlMap(baseParam));
        return saveSinglePoiParam;
    }



    public static CheckSaveSinglePoiParam convertCheckParam(SendParam<HeronContractBDSettleSaveParam> sendParam) throws GatewayArgumentException {
        HeronContractBDSettleSaveParam bdSettleSaveParam = sendParam.getOriginParam();
        BdSettleSaveBaseParamStruct baseParam = JacksonUtil.readValue(bdSettleSaveParam.getBdSaveBaseParam(), BdSettleSaveBaseParamStruct.class);
        GatewayPreconditions.checkNotNull(baseParam, "配送信息基本参数不能为空");
        CheckSaveSinglePoiParam checkSaveSinglePoiParam = new CheckSaveSinglePoiParam();
        checkSaveSinglePoiParam.setOperatorParam(OperatorConverter.toPerfOperator(bdSettleSaveParam.getOperator()));
        checkSaveSinglePoiParam.setSessionId(GlobalFlowSessionHandler.getSessionId());
        checkSaveSinglePoiParam.setWmPoiId(bdSettleSaveParam.getWmPoiId());
        checkSaveSinglePoiParam.setFeeMode(baseParam.getFeeMode());
        List<ServiceBrandProductParam> brandProductParamList = convertPerfProductList(baseParam.getServiceBrandProductList());
        checkSaveSinglePoiParam.setServiceBrandProductParam(brandProductParamList);
        checkSaveSinglePoiParam.setContractFeeItemList(baseParam.getPerfContractItemList());
        checkSaveSinglePoiParam.setDeliveryFee(extractDeliveryFee(baseParam));
        checkSaveSinglePoiParam.setSpecialApprovalUrl(extractSpecialApprovalUrl(bdSettleSaveParam.getOperator().getOpSource(), baseParam));
        checkSaveSinglePoiParam.setPlatShareInfo(sendParam.getFlowShareInfoMap().get(SubDomainEnum.PLAT));
        checkSaveSinglePoiParam.setBusinessIdentity(GlobalFlowSessionHandler.getPerfBusinessIdentity());
        checkSaveSinglePoiParam.setStationParamList(buildStationParamList(baseParam));
        checkSaveSinglePoiParam.setQueryPerfSourceId(baseParam.getQueryPerfSourceId());
        checkSaveSinglePoiParam.setQueryPerfSourceIdMap(baseParam.getQueryPerfSourceIdMap());
        checkSaveSinglePoiParam.setRefreshFee(BooleanUtils.isTrue(baseParam.getSgRefreshFee()) || BooleanUtils.isTrue(baseParam.getRefreshFee()));
        checkSaveSinglePoiParam.setRefreshFeeMap(baseParam.getRefreshFeeMap());
        checkSaveSinglePoiParam.setNeedSpecial(baseParam.getNeedSpecial());
        checkSaveSinglePoiParam.setSpecialApprovalApplyType(baseParam.getSpecialApprovalApplyType());
        checkSaveSinglePoiParam.setSpecialApprovalUrlMap(extractSpecialApprovalUrlMap(baseParam));
        return checkSaveSinglePoiParam;
    }

    private static List<StationParam> buildStationParamList(BdSettleSaveBaseParamStruct baseParam) {
        return Optional.ofNullable(baseParam.getStationList()).orElse(new ArrayList<>()).stream().map(o -> {
            StationParam stationParam = new StationParam();
            stationParam.setStationId(o.getStationId());
            return stationParam;
        }).collect(Collectors.toList());
    }


    public static List<ServiceBrandProductParam> convertPerfProductList(List<LogisticsBrandProductParamStruct> brandParamStructList) {
        if (brandParamStructList == null) {
            return null;
        }
        return brandParamStructList.stream()
                .map(originBrand -> {
                    ServiceBrandProductParam brandProductParam = new ServiceBrandProductParam();
                    brandProductParam.setServiceBrand(originBrand.getServiceBrand());
                    brandProductParam.setServiceProductList(originBrand.getServiceProductList());
                    return brandProductParam;
                })
                .collect(Collectors.toList());
    }


    private static Double extractDeliveryFee(BdSettleSaveBaseParamStruct baseParam) {
        if (CollectionUtils.isEmpty(baseParam.getLogisticsAreaList())) {
            return -1D;
        }
        // 对于自配+跑腿+聚合配时，hasPerf=true，会走到履约的保存，需要取deliveryFee
        // 自配+跑腿的范围里（area.getLogisticsCode()）是1003，不是0000
        // 1003不可独立售卖，取不到deliveryFee，此时直接返回-1
        if (baseParam.getLogisticsAreaList().stream().anyMatch(item -> ProductPackUtils.isZbByLogisticsCode(item.getLogisticsCode()))) {
            return baseParam.getLogisticsAreaList().stream()
                    .filter(area -> WmLogisticsSaleStrategyHelper.isSingleSaleableLogistics(area.getLogisticsCode()))
                    .map(SpAreaLogisticsAreaSaveDTO::getDeliveryFee)
                    .findAny()
                    .orElse(-1D);
        }
        return baseParam.getLogisticsAreaList().stream()
                .filter(area -> WmLogisticsSaleStrategyHelper.isSingleSaleableLogistics(area.getLogisticsCode()))
                .map(SpAreaLogisticsAreaSaveDTO::getDeliveryFee)
                .findAny()
                .orElse(null);

    }

    private static String extractSpecialApprovalUrl(OperateSourceEnum opSource, BdSettleSaveBaseParamStruct baseParam) {
//        OperateSourceEnum opSource = bdSettleSaveParam.getOperator().getOpSource();
        if (!SG_SETTLE_SOURCE.contains(opSource) && !YY_SETTLE_SOURCE.contains(opSource)) {
            return null;
        } else {
            return baseParam.getPlatContractItemList().stream()
                    .filter(item -> item.getLogisticsCode() != null)
                    .filter(item -> SG_SPECIAL_APPROVAL_URL_ITEM_NAME.equals(item.getName()))
                    .map(WmLogisticsContractItemDTO::getValue)
                    .findAny()
                    .orElse(null);

        }
    }


    private static Map<String, String> extractSpecialApprovalUrlMap(BdSettleSaveBaseParamStruct baseParam) {
        return baseParam.getPlatContractItemList().stream()
                .filter(item -> item.getLogisticsCode() != null)
                .filter(item -> SG_SPECIAL_APPROVAL_URL_ITEM_NAME.equals(item.getName()))
                .collect(Collectors.toMap(WmLogisticsContractItemDTO::getLogisticsCode, WmLogisticsContractItemDTO::getValue, (v1, v2) -> v1));
    }
}
