package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.terminate;

import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaStopProcessRequest;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.BmSpAreaFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.GatewayPreconditions;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.AreaDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.opensdk.util.AreaIdentityHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.OperatorConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.StatusConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.FlowTerminateBo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 流程终止请求发送
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
public class AreaNewFlowTerminateReqSender extends AreaDomainRequestSender<FlowTerminateBo, SpAreaStopProcessRequest> {

    @Resource
    private BmSpAreaFlowThriftServiceAdapter bmSpAreaFlowThriftServiceAdapter;

    @Override
    protected SpAreaStopProcessRequest buildRemoteParam(SendParam<FlowTerminateBo> param) {
        FlowTerminateBo originParam = param.getOriginParam();
        SpAreaStopProcessRequest bmOpenSpAreaStopProcessRequest = new SpAreaStopProcessRequest();
        bmOpenSpAreaStopProcessRequest.setComment(originParam.getFailReason());
        bmOpenSpAreaStopProcessRequest.setOperator(OperatorConverter.toAreaNewOperator(originParam.getOperator()));
        bmOpenSpAreaStopProcessRequest.setSessionId(originParam.getSessionId());
        bmOpenSpAreaStopProcessRequest.setTargetStatus(StatusConverter.toAreaNewStatus(originParam.getTargetStatus()));
        bmOpenSpAreaStopProcessRequest.setWmPoiId(originParam.getWmPoiId());
        return bmOpenSpAreaStopProcessRequest;
    }


    @Override
    protected void sendToSpArea(SpAreaStopProcessRequest remoteParam) throws GatewayAdapterException {
        bmSpAreaFlowThriftServiceAdapter.stopSpAreaProcess(remoteParam, AreaIdentityHelper.convertFromSessionCategory(GlobalFlowSessionHandler.getSessionCategory()));
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.TERMINATE_FLOW;
    }

}