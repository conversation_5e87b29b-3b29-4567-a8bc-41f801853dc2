package com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.processing.phf;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.WmPoiQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.model.domain.HeronContractGatewaySession;
import com.sankuai.meituan.waimai.heron.contract.gateway.basic.service.HeronContractGatewaySessionBasicService;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.batchflow.BatchSignFlowRedisHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.config.MccConfig;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.BatchFlowRedisStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.JacksonUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.utils.GlobalFlowSessionHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.OrchestrationExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.BatchApplySignStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.PerfContractCreatePdfStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.sign.SignItemBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.ApplySignFailStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.orchestration.LogisticsFlowOrchestration;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.sign.BatchApplySignFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.sign.PerfContractCratePdfFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.terminate.ApplySignFailFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractCreateAndUpdateSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractMigrateStageEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.SessionCategoryEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.BatchSignFlowSceneEnum.PHF_BATCH_SIGN_FLOW_SCENE;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_CUSTOMER_ID;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID;

/**
 * <AUTHOR>
 * @date 2024/12/17
 */
@Service
@Slf4j
public class PhfAutoBatchApplySignFlowOrchestration implements LogisticsFlowOrchestration<String> {

    @Resource
    private BatchSignFlowRedisHelper batchSignFlowRedisHelper;

    @Resource
    private HeronContractGatewaySessionBasicService heronContractGatewaySessionBasicService;

    @Resource
    private PerfContractCratePdfFlowStep perfContractCratePdfFlowStep;

    @Resource
    private BatchApplySignFlowStep batchApplySignFlowStep;

    @Resource
    private ApplySignFailFlowStep applySignFailFlowStep;

    @Resource
    private WmPoiQueryThriftServiceAdapter wmPoiQueryThriftServiceAdapter;

    @Override
    public OrchestrationExecResult flow(String batchSignTaskId) {
        log.info("拼好饭发起签约开始 batchSignTaskId={}", batchSignTaskId);
        BatchFlowRedisStatusEnum statusEnum = batchSignFlowRedisHelper.getBatchFlowStatus(String.valueOf(batchSignTaskId), PHF_BATCH_SIGN_FLOW_SCENE);
        if (!BatchFlowRedisStatusEnum.APPLY_SIGNING.equals(statusEnum)) {
            return OrchestrationExecResult.fail("非签约中状态");
        }
        List<HeronContractGatewaySession> sessionList = heronContractGatewaySessionBasicService.queryByBatchRelationRT(String.valueOf(batchSignTaskId), ContractCreateAndUpdateSceneEnum.PHF_BATCH_PROCESS, SessionCategoryEnum.PHF);
        if (CollectionUtils.isEmpty(sessionList)) {
            log.info("拼好饭发起签约结束 batchSignTaskId={} 未查询到session，无需发起签约", batchSignTaskId);
            return OrchestrationExecResult.fail("未查询到session");
        }
        List<HeronContractGatewaySession> processingSessionList = sessionList.stream()
                .filter(session -> session.getStatus().isProcessing())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(processingSessionList)) {
            log.info("拼好饭发起签约结束 batchSignTaskId={} 未查询到流程中session，无需发起签约", batchSignTaskId);
            return OrchestrationExecResult.fail("未查询到流程中的session");
        }
        //对批量签约门店进行分组，只对保存提审成功的门店进行签约分组
        this.updateSessionGroupIndex(processingSessionList);
        List<HeronContractGatewaySession> needCreatePerfPdfSessionList = processingSessionList.stream()
                .filter(session -> BooleanUtils.isTrue(session.getContext().getNeedPerfCreatePdf()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needCreatePerfPdfSessionList)) {
            PerfContractCreatePdfStepParam createPdfStepParam = new PerfContractCreatePdfStepParam();
            createPdfStepParam.setBatchRelationId(Long.valueOf(batchSignTaskId));
            createPdfStepParam.setSessionList(needCreatePerfPdfSessionList);
            createPdfStepParam.setBatchSignScene(ContractCreateAndUpdateSceneEnum.PHF_BATCH_PROCESS.name());
            FlowExecResult execResult = perfContractCratePdfFlowStep.execute(createPdfStepParam);
            if (!execResult.getSuccess()) {
                batchApplySignFail(batchSignTaskId, processingSessionList, SubDomainEnum.PERF, "履约创建pdf失败:" + Optional.ofNullable(execResult.getMessage()).orElse(""));
                return OrchestrationExecResult.fail(execResult.getMessage());
            }
            return OrchestrationExecResult.success();
        } else {
            Map<Long, List<HeronContractGatewaySession>> customerIdAndSessionMap = groupSessionByCustomer(processingSessionList);
            if (MapUtils.isEmpty(customerIdAndSessionMap)) {
                return OrchestrationExecResult.fail("批量签约按门店id列表未查询关联客户");
            }
            List<Long> failApplyCustomerIdList = new ArrayList<>();
            for (Map.Entry<Long, List<HeronContractGatewaySession>> entry : customerIdAndSessionMap.entrySet()) {
                BatchApplySignStepParam applySignStepParam = buildBatchApplySignStepParam(entry.getValue());
                if (applySignStepParam == null) {
                    continue;
                }
                FlowExecResult execResult = batchApplySignFlowStep.execute(applySignStepParam);
                if (!execResult.getSuccess()) {
                    log.info("拼好饭发起签约失败 batchSignTaskId={} customerId={} failReason={}", batchSignTaskId, entry.getKey(), execResult.getMessage());
                    batchApplySignFail(batchSignTaskId, entry.getValue(), SubDomainEnum.E_CONTRACT, "电子合同发起签约失败:" + Optional.ofNullable(execResult.getMessage()).orElse(""));
                    failApplyCustomerIdList.add(entry.getKey());
                }
            }
            OrchestrationExecResult applyResult = CollectionUtils.isEmpty(failApplyCustomerIdList) ? OrchestrationExecResult.success() : OrchestrationExecResult.fail("客户:" + failApplyCustomerIdList +" 签约失败");
            log.info("拼好饭批量签约发起完成 batchSignTaskId={} applyResult={}", batchSignTaskId, JacksonUtil.writeAsJsonStr(applyResult));
            return applyResult;
        }
    }

    //
    private void updateSessionGroupIndex(List<HeronContractGatewaySession> sessionList) {
        if (CollectionUtils.isEmpty(sessionList)) {
            return;
        }
        List<HeronContractGatewaySession> orderedSessionList = sessionList.stream().sorted(Comparator.comparing(HeronContractGatewaySession::getWmPoiId)).collect(Collectors.toList());
        List<List<HeronContractGatewaySession>> partitionSessionList = Lists.partition(orderedSessionList, MccConfig.getPhfBatchSignGroupPoiLimit());
        int groupIndex = 1;
        for (List<HeronContractGatewaySession> partition : partitionSessionList) {
            for (HeronContractGatewaySession session : partition) {
                session.getContext().setSignGroupIndex(groupIndex);
                heronContractGatewaySessionBasicService.updateContext(session.getSessionLeafId(), session.getContext());
            }
            groupIndex++;
        }
    }

    private void batchApplySignFail(String batchSignTaskId, List<HeronContractGatewaySession> sessionList, SubDomainEnum failDomain, String applyFailReason) {
        for (HeronContractGatewaySession session : sessionList) {
            try {
                GlobalFlowSessionHandler.applyWithSession(session, buildApplySignFailStepParam(session, failDomain, applyFailReason), applySignFailFlowStep::execute);
            } catch (Exception e) {
                log.warn("batchApplySignFail execute exception batchSignTaskId={} wmPoiId={}", batchSignTaskId, session.getWmPoiId(), e);
            }
        }
    }

    private ApplySignFailStepParam buildApplySignFailStepParam(HeronContractGatewaySession session, SubDomainEnum failDomain, String applyFailReason) {
        ApplySignFailStepParam applySignFailBo = new ApplySignFailStepParam();
        applySignFailBo.setWmPoiId(session.getWmPoiId());
        applySignFailBo.setFailReason(applyFailReason);
        applySignFailBo.setCauseDomain(failDomain);
        applySignFailBo.setOperator(HeronContractOperator.builder()
                .opId(-1L)
                .opName("拼好饭自动打包签约发起失败")
                .build());
        applySignFailBo.setSessionId(session.getSessionLeafId());
        applySignFailBo.setSessionCategory(SessionCategoryEnum.PHF);
        return applySignFailBo;
    }


    private BatchApplySignStepParam buildBatchApplySignStepParam(List<HeronContractGatewaySession> sessionList) {

        List<SignItemBo> signItemBoList = new ArrayList<>();
        Map<Integer, List<Long>> groupIndexAndWmPoiIdListMap = Maps.newHashMap();
        ContractMigrateStageEnum migrateStageEnum = sessionList.get(0).getContext().getMigrateStage();
        for (HeronContractGatewaySession session : sessionList) {
            if (session != null && ContractSessionStatusEnum.AUDIT_PASS.equals(session.getStatus())) {
                Long sessionId = session.getSessionLeafId();
                SignItemBo signItemBo = new SignItemBo();
                signItemBo.setWmPoiId(session.getWmPoiId());
                signItemBo.setSessionId(sessionId);
                signItemBo.setSession(session);
                signItemBoList.add(signItemBo);
                List<Long> wmPoiIdList = groupIndexAndWmPoiIdListMap.computeIfAbsent(Optional.ofNullable(session.getContext().getSignGroupIndex()).orElse(0), k -> new ArrayList<>());
                wmPoiIdList.add(session.getWmPoiId());
            }
        }
        if (CollectionUtils.isEmpty(signItemBoList)) {
            return null;
        }
        BatchApplySignStepParam batchApplySignStepParam = new BatchApplySignStepParam();
        batchApplySignStepParam.setSignItemBoList(signItemBoList);
        batchApplySignStepParam.setOperator(HeronContractOperator.builder()
                .opId(-1L)
                .opName("拼好饭自动批量发起签约")
                .build());
        batchApplySignStepParam.setSessionCategory(SessionCategoryEnum.PHF);
        batchApplySignStepParam.setWmPoiIdGroupList(Lists.newArrayList(groupIndexAndWmPoiIdListMap.values()));
        batchApplySignStepParam.setMigrateStage(migrateStageEnum);
        return batchApplySignStepParam;
    }


    private Map<Long, List<HeronContractGatewaySession>> groupSessionByCustomer(List<HeronContractGatewaySession> sessionList) {
        Map<Long, HeronContractGatewaySession> wmPoiIdAndSessionMap = sessionList.stream().collect(Collectors.toMap(HeronContractGatewaySession::getWmPoiId, Function.identity(), (x1, x2) -> x1));
        List<Long> wmPoiIdList = sessionList.stream().map(HeronContractGatewaySession::getWmPoiId).collect(Collectors.toList());

        //尝试查询门店聚合信息，最多重试3次。
        for (int i = 0; i < 3; i++) {
            try {
                Map<Long, List<HeronContractGatewaySession>> customerIdAndSessionMap = Maps.newHashMap();
                List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftServiceAdapter.batchGetWmPoiAggre(wmPoiIdList, ImmutableSet.of(WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_CUSTOMER_ID));
                for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                    if (wmPoiAggre.getCustomer_id() > 0) {
                        List<HeronContractGatewaySession> customerSessionList = customerIdAndSessionMap.computeIfAbsent(wmPoiAggre.getCustomer_id(), k -> Lists.newArrayList());
                        customerSessionList.add(wmPoiIdAndSessionMap.get(wmPoiAggre.getWm_poi_id()));
                    }
                }
                return customerIdAndSessionMap;
            } catch (Exception e) {
                log.error("groupSessionByCustomer 查询门店聚合信息失败 wmPoiIdList={}", JacksonUtil.writeAsJsonStr(wmPoiIdList), e);

            }
        }
        return null;
    }

}
