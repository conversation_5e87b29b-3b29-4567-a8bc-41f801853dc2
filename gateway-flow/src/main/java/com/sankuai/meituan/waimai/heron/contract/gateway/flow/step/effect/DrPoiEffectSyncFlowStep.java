package com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.effect;

import com.google.common.collect.ImmutableSet;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.SubDomainEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.utils.TimeUtil;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.helper.RouteHelper;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.DispatchParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.DispatchResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.FlowExecResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.effect.EffectiveCheckReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.effect.EffectiveReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.dispatcher.terminate.SessionFlowTerminateReqDispatcher;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.EffectiveStepParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.teminate.FlowTerminateBo;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.statushandler.FlowNextStatusHandler;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.step.LogisticsFlowStep;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum.SAVE;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2024-04-23 20:19
 */
@Component
@Slf4j
public class DrPoiEffectSyncFlowStep implements LogisticsFlowStep<EffectiveStepParam> {

    @Resource
    private EffectiveReqDispatcher effectiveReqDispatcher;

    @Resource
    private EffectiveCheckReqDispatcher effectiveCheckReqDispatcher;

    @Resource
    private SessionFlowTerminateReqDispatcher sessionFlowTerminateReqDispatcher;

    @Resource
    private RouteHelper routeHelper;

    /**
     * 双写链路生效只需要生效履约合同，技术和范围都不需要生效，只需要终止流程
     */
    @Override
    @FlowNextStatusHandler(description = "原链路生效同步双写链路", requiredEnterStatus = {SAVE}, successStatus = ContractSessionStatusEnum.COMPLETE)
    public FlowExecResult execute(EffectiveStepParam param) {
        DispatchParam<EffectiveStepParam> effectiveDispatchParam = new DispatchParam<>(param);

        DispatchResult effectiveCheckDispatchResult = effectiveCheckReqDispatcher.dispatch(effectiveDispatchParam);
        if (!effectiveCheckDispatchResult.getSuccess()) {
            return FlowExecResult.build(effectiveCheckDispatchResult);
        }

        DispatchResult effectiveDispatchResult = effectiveReqDispatcher.dispatch(effectiveDispatchParam);
        if (!effectiveDispatchResult.getSuccess()) {
            return FlowExecResult.build(effectiveDispatchResult);
        }

        FlowTerminateBo terminateBo = buildFlowTerminateBo(param);
        DispatchParam<FlowTerminateBo> terminateDispatchParam = new DispatchParam<>(terminateBo);
        DispatchResult terminateDispatchResult = sessionFlowTerminateReqDispatcher.dispatch(terminateDispatchParam);

        return FlowExecResult.build(terminateDispatchResult);
    }

    private FlowTerminateBo buildFlowTerminateBo(EffectiveStepParam param) {
        return FlowTerminateBo.builder()
                .wmPoiId(param.getWmPoiId())
                .sessionId(param.getSessionId())
                .targetStatus(ContractSessionStatusEnum.TERMINATE)
                .failReason("原链路生效同步双写链路")
                .dealTime(TimeUtil.unixTime())
                .skipDomainSet(ImmutableSet.of(SubDomainEnum.PERF))
                .operator(param.getOperator()).build();
    }
}