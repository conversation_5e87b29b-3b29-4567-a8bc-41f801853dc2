package com.sankuai.meituan.waimai.heron.contract.gateway.flow.statushandler;

import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.constant.ContractSessionStatusEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface FlowFailStatusHandler {

    ContractSessionStatusEnum targetFailStatus();

    /**
     * 终止流程失败时是否强制更新session状态
     * @return
     */
    boolean forceUpdateSessionStatus() default true;
}
