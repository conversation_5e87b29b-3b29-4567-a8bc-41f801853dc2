package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.sign.plat;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.ExecTagEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.api.RequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PlatSingleFlowPoiRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.sign.plat.PlatContractWaitManualSignSuccessReqSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description: 生成待打包签约任务路由
 * @author: chenyihao04
 * @create: 2023-04-26 16:17
 */
@Component
@Slf4j
public class PlatContractWaitManualSignReqRouter extends PlatSingleFlowPoiRequestRouter {

    @Resource
    private PlatContractWaitManualSignSuccessReqSender platContractWaitManualSignSuccessReqSender;


    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        RouteResult routeResult = new RouteResult();
        routeResult.setExecTag(ExecTagEnum.EXEC);
        routeResult.setPerfSplit(routeParam.getProcessingPerfSplit());
        routeResult.setAreaSplit(routeParam.getProcessingAreaSplit());
        routeResult.setPerfDR(routeParam.getProcessingPerfDR());
        routeResult.setRequestSender(matchReqSender(routeParam));
        return routeResult;
    }

    private RequestSender matchReqSender(SinglePoiFlowRouteParam routeParam) {
        if (Objects.isNull(routeParam.getProcessingContractStruct())) {
            log.info("无流程数据场景调用生成待打包签约任务");
            return null;
        } else if (routeParam.getProcessingContractStruct().isContractStruct()) {
            return platContractWaitManualSignSuccessReqSender;
        } else if (routeParam.getProcessingContractStruct().isOldStruct()) {
            log.error("本期不涉及此场景, wmPoiId: {}", routeParam.getWmPoiId());
        } else {
            log.error("识别门店结构类型失败, 请排查. wmPoiId: {}", routeParam.getWmPoiId());
        }
        return null;
    }

}