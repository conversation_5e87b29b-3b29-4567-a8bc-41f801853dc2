package com.sankuai.meituan.waimai.heron.contract.gateway.flow.router.preview.perf;

import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.router.flow.PerfSinglePoiFlowRequestRouter;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SinglePoiFlowRouteParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.RouteResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.preview.perf.PerfPreviewUpdateReqSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2023-08-16 10:53
 */
@Component
public class PerfPreviewUpdateReqRouter extends PerfSinglePoiFlowRequestRouter {

    @Resource
    private PerfPreviewUpdateReqSender perfPreviewUpdateReqSender;

    @Override
    public RouteResult route(SinglePoiFlowRouteParam routeParam) throws GatewaySystemException {
        return super.buildDefaultPerfResult(routeParam, perfPreviewUpdateReqSender);
    }
}