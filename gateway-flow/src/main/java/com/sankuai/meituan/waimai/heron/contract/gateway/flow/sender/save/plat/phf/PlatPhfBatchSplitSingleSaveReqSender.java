package com.sankuai.meituan.waimai.heron.contract.gateway.flow.sender.save.plat.phf;

import com.sankuai.meituan.waimai.heron.contract.gateway.adapter.service.PlatContractFlowThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.constant.RequestSceneEnum;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayAdapterException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayArgumentException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewayBusinessException;
import com.sankuai.meituan.waimai.heron.contract.gateway.common.exception.GatewaySystemException;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.base.sender.subdomain.PlatDomainRequestSender;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.param.SendParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.core.model.result.SendResult;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.converter.PlatPhfContractSaveParamConverter;
import com.sankuai.meituan.waimai.heron.contract.gateway.flow.model.phf.PhfBatchSplitSingleSaveParam;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfContractSinglePoiSaveRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.phf.platform.PhfSaveResponseDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/3
 */
@Component
public class PlatPhfBatchSplitSingleSaveReqSender extends PlatDomainRequestSender<PhfBatchSplitSingleSaveParam, PhfContractSinglePoiSaveRequestDTO> {

    @Resource
    private PlatContractFlowThriftServiceAdapter platContractFlowThriftServiceAdapter;

    @Override
    protected PhfContractSinglePoiSaveRequestDTO buildRemoteParam(SendParam<PhfBatchSplitSingleSaveParam> param) throws GatewaySystemException, GatewayBusinessException, GatewayArgumentException, GatewayAdapterException {
        return PlatPhfContractSaveParamConverter.convertByBatch(param.getOriginParam());
    }

    @Override
    protected SendResult doSend(PhfContractSinglePoiSaveRequestDTO remoteParam) throws GatewayAdapterException {
        PhfSaveResponseDTO responseDTO = platContractFlowThriftServiceAdapter.savePhfSinglePoi(remoteParam);
        return convertResult(responseDTO.getResult());
    }

    @Override
    public RequestSceneEnum getRequestScene() {
        return RequestSceneEnum.SAVE;
    }
}
